import { debounce } from 'lodash';

export const useRemoteListMethod = (
  proxy,
  { FetchUrl, label = 'name', value = 'code', queryStr = 'name', query = { pageNum: 1, pageSize: 10, name: '' } }
) => {
  // 使用解构获取 modal
  const { $modal } = proxy;
  const list = ref([]);
  const initList = ref([]);
  const loading = ref(false);

  // 抽离数据转换逻辑
  const transformData = (data) =>
    data.map((item) => ({
      label: item[label],
      value: item[value]
    }));

  // 封装请求方法
  const fetchData = async (params) => {
    try {
      const res = await FetchUrl(params);
      return res.code === 200 ? transformData(res.rows) : [];
    } catch (error) {
      console.error('Failed to fetch data:', error);
      return [];
    }
  };

  const _remoteMethod = async (queryString, codeValue, codeName) => {
    if (loading.value) return;
    if (!queryString) {
      list.value = initList.value;
      return;
    }
    loading.value = true;
    try {
      // 构建查询参数
      const searchQuery = { ...query };
      if (typeof codeValue === 'string' && typeof codeName === 'string') {
        searchQuery[codeName] = codeValue;
      } else {
        searchQuery[queryStr] = queryString;
      }

      list.value = await fetchData(searchQuery);
      if (!list.value.length) {
        $modal.msg('没有匹配数据');
      }
    } catch (error) {
      list.value = [];
      $modal.msgError('搜索失败');
    } finally {
      loading.value = false;
    }
  };

  const remoteMethod = debounce(_remoteMethod, 300);

  // 初始化 并且保留返回的值 当出现为空的查询条件时返回
  const init = async () => {
    initList.value = await fetchData(query);
    list.value = initList.value;
  };

  init();

  return {
    list,
    remoteMethod,
    loading
  };
};
