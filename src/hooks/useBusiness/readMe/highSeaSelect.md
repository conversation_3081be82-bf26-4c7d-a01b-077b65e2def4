# useHighSeaSelect Hook 使用手册

## 功能说明

`useHighSeaSelect` 是一个用于管理 CRM 系统中公海池选择相关状态和操作的 Vue3 组合式 API。主要用于处理公海池的数据加载、选择和名称查找等功能。

## 使用方法

```typescript
import { useHighSeaSelect } from '@/hooks/useBusiness/highSeaSelect';

// 在组件中使用
const {
  highSeaOptions, // 公海池选项列表
  loading, // 加载状态
  loadHighSeaList, // 加载公海池列表方法
  findHighSeaName // 查找公海池名称方法
} = useHighSeaSelect();

// 初始化时加载数据
onMounted(async () => {
  await loadHighSeaList();
});
```

## 接口说明

返回参数：

| 参数            | 类型                                      | 说明               |
| --------------- | ----------------------------------------- | ------------------ |
| highSeaOptions  | `Ref<HighSeaVO[]>`                        | 公海池选项列表     |
| loading         | `Ref<boolean>`                            | 加载状态标志       |
| loadHighSeaList | `() => Promise<void>`                     | 加载公海池列表方法 |
| findHighSeaName | `(highSeaId: string \| number) => string` | 查找公海池名称方法 |

数据类型：

```typescript
interface HighSeaVO {
  id: string | number; // 公海池 ID
  seaName: string; // 公海池名称
  // ... 其他字段
}
```

方法说明：

loadHighSeaList()

- 功能：加载公海池列表数据
- 参数：无
- 返回：Promise<void>
- 说明：
  - 自动处理加载状态
  - 默认加载前 30 条数据
  - 加载完成后自动构建映射关系

findHighSeaName(highSeaId)

- 功能：查找公海池名称
- 参数：highSeaId (string | number)
- 返回：string
- 说明：找不到时返回 ID 或空字符串

## 代码示例

表单选择器：

```vue
<template>
  <el-select v-model="selectedHighSea" :loading="loading">
    <el-option v-for="item in highSeaOptions" :key="item.id" :label="item.seaName" :value="item.id" />
  </el-select>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useHighSeaSelect } from '@/hooks/useBusiness/highSeaSelect';

const selectedHighSea = ref();
const { highSeaOptions, loading, loadHighSeaList } = useHighSeaSelect();

onMounted(() => {
  loadHighSeaList();
});
</script>
```

显示名称：

```vue
<template>
  <span>{{ findHighSeaName(highSeaId) }}</span>
</template>

<script setup lang="ts">
import { useHighSeaSelect } from '@/hooks/useBusiness/highSeaSelect';

const { findHighSeaName } = useHighSeaSelect();
const highSeaId = 'xxx'; // 公海池 ID
</script>
```

## 错误处理

```typescript
try {
  await loadHighSeaList();
} catch (error) {
  console.error('加载失败:', error);
}
```

## 注意事项

1. 建议在组件挂载时初始化数据
2. loading 状态会自动管理
3. 默认最多加载 30 条数据
4. 使用 Map 缓存提升查询效率
5. 组件销毁时自动清理状态

## 最佳实践

基础用法：

```typescript
onMounted(async () => {
  await loadHighSeaList();
});
```

错误处理：

```typescript
onMounted(async () => {
  try {
    await loadHighSeaList();
  } catch (error) {
    // 错误处理
  }
});
```

动态显示：

```typescript
const displayName = computed(() => findHighSeaName(currentId.value));
```
