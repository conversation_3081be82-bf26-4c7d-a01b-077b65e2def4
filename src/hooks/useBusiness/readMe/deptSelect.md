我来帮你基于 highSea 的使用示例编写一个详细的操作手册。

````markdown:src/hooks/useBusiness/readMe/deptSelect.md
# deptSelect 部门选择器使用手册

## 简介
`deptSelect` 是一个用于部门选择的自定义 Hook，提供了部门树形数据的加载、选择和标签展示等功能。主要用于系统中需要选择部门的场景。

## 基本用法

### 1. 引入
```typescript
import { useDeptSelect } from '@/hooks/useBusiness/deptSelect'
````

### 2. 初始化

```typescript
const { deptOptions, loadDeptTree, findDeptLabel } = useDeptSelect();
```

### 3. 主要功能

#### 3.1 返回值说明

- `deptOptions`: 部门树形数据
- `loadDeptTree`: 加载部门树数据的方法
- `findDeptLabel`: 根据部门ID获取部门名称的方法

#### 3.2 加载部门数据

```typescript
// 在组件挂载时加载部门数据
onMounted(() => {
  loadDeptTree();
});
```

#### 3.3 在表单中使用

```typescript
<template>
  <el-tree-select
    v-model="form.deptId"
    :data="deptOptions"
    :props="{ value: 'id', label: 'label', children: 'children' }"
    value-key="id"
    placeholder="请选择归属部门"
    check-strictly
  />
</template>
```

#### 3.4 在表格中显示部门名称

```typescript
<template>
  <el-table-column label="所属部门" prop="deptId">
    <template #default="scope">
      <span>{{ findDeptLabel(scope.row.deptId) }}</span>
    </template>
  </el-table-column>
</template>
```

## 配置说明

### 树形数据结构

```typescript
interface DeptOption {
  id: string | number;
  label: string;
  children?: DeptOption[];
}
```

### 方法说明

1. `loadDeptTree()`

   - 功能：加载部门树形数据
   - 返回：void
   - 使用时机：组件初始化时调用

2. `findDeptLabel(deptId: string | number)`
   - 功能：根据部门ID获取部门名称
   - 参数：deptId - 部门ID
   - 返回：string - 部门名称
   - 使用时机：需要显示部门名称时调用

## 使用示例

```typescript
<script setup lang="ts">
import { useDeptSelect } from '@/hooks/useBusiness/deptSelect'

// 初始化 hook
const { deptOptions, loadDeptTree, findDeptLabel } = useDeptSelect()

// 加载数据
onMounted(() => {
  loadDeptTree()
})

// 在表单中使用
const form = reactive({
  deptId: undefined
})

// 在表格中使用
const handleDeptName = (deptId) => {
  return findDeptLabel(deptId)
}
</script>

<template>
  <!-- 选择器使用 -->
  <el-tree-select
    v-model="form.deptId"
    :data="deptOptions"
    :props="{ value: 'id', label: 'label', children: 'children' }"
    value-key="id"
    placeholder="请选择归属部门"
    check-strictly
  />

  <!-- 显示部门名称 -->
  <span>{{ findDeptLabel(deptId) }}</span>
</template>
```

## 注意事项

1. 确保在使用组件前调用 `loadDeptTree()` 加载部门数据
2. 树形选择器支持单选模式，如需多选可以通过配置实现
3. `findDeptLabel` 方法在部门数据加载完成后才能正确返回部门名称
4. 部门数据会被缓存，不需要重复加载

## 最佳实践

1. 在组件初始化时加载数据
2. 使用 `v-model` 进行数据双向绑定
3. 表格展示时使用 `findDeptLabel` 方法转换部门ID为名称
4. 配合表单验证使用时，建议设置 `required` 属性

```

```
