import { ref } from 'vue';
import type { DeptTreeVO } from '@/api/system/dept/types';
import { deptTreeSelectByOrgId } from '@/api/system/dept';
import { useUserStore } from '@/store/modules/user';
export const useDeptSelect = () => {
  const deptOptions = ref<DeptTreeVO[]>([]);
  const loading = ref(false);
  const userStore = useUserStore();
  // 按组织Id查询部门下拉树结构
  const loadDeptTree = async () => {
    try {
      loading.value = true;
      // const orgId = localStorage.getItem('orgId');
      const orgId = userStore.orgId;
      const res = await deptTreeSelectByOrgId(orgId);
      deptOptions.value = res.data;
    } finally {
      loading.value = false;
    }
  };

  // 根据部门ID查找部门名称
  const findDeptLabel = (deptId: number | string): string => {
    const findInTree = (deptList: DeptTreeVO[]): string => {
      for (const dept of deptList) {
        if (dept.id === deptId) {
          return dept.label;
        }
        if (dept.children?.length) {
          const label = findInTree(dept.children);
          if (label) return label;
        }
      }
      return '';
    };
    return findInTree(deptOptions.value);
  };

  return {
    deptOptions,
    loading,
    loadDeptTree,
    findDeptLabel
  };
};
