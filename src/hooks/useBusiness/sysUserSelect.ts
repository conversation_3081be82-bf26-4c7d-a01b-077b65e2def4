// 基于API：src/api/system/user，参考src/hooks/useBusiness/deptSelect.ts，形成的可复用的组件
import { ref } from 'vue';
import { listUser } from '@/api/system/user';
import type { UserVO } from '@/api/system/user/types';

export const useSysUserSelect = () => {
  const userOptions = ref<UserVO[]>([]);
  const loading = ref(false);
  const userMap = ref<Map<string | number, string>>(new Map());

  // 加载用户列表
  const loadUserList = async () => {
    try {
      loading.value = true;
      const orgId = localStorage.getItem('orgId'); // 获取当前用户的OrgId
      const res = await listUser({
        pageNum: 1,
        pageSize: 30, // 获取所有用户
        orgId: orgId,
        status: '0'
      });
      userOptions.value = res.rows;
      // 构建用户ID到用户名的映射
      userOptions.value.forEach((user) => {
        userMap.value.set(user.userId, user.nickName);
      });
    } finally {
      loading.value = false;
    }
  };

  // 根据用户ID查找用户名
  const findUserName = (userId: string | number): string => {
    return userMap.value.get(userId) || userId?.toString() || '';
  };

  return {
    userOptions,
    loading,
    loadUserList,
    findUserName
  };
};
