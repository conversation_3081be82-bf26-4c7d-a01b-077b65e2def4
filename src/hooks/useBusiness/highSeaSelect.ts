import { ref } from 'vue';
import { listHighSea } from '@/api/crm/highSea';
import type { HighSeaVO } from '@/api/crm/highSea/types';

export const useHighSeaSelect = () => {
  const highSeaOptions = ref<HighSeaVO[]>([]);
  const loading = ref(false);
  const highSeaMap = ref<Map<string | number, string>>(new Map());

  // 加载公海池列表
  const loadHighSeaList = async () => {
    try {
      loading.value = true;
      const res = await listHighSea({
        pageNum: 1,
        pageSize: 30
      });
      highSeaOptions.value = res.rows;
      // 构建公海池ID到名称的映射
      highSeaOptions.value.forEach((highSea) => {
        highSeaMap.value.set(highSea.id, highSea.seaName);
      });
    } finally {
      loading.value = false;
    }
  };

  // 根据公海池ID查找公海池名称
  const findHighSeaName = (highSeaId: string | number): string => {
    return highSeaMap.value.get(highSeaId) || highSeaId?.toString() || '';
  };

  return {
    highSeaOptions,
    loading,
    loadHighSeaList,
    findHighSeaName
  };
};
