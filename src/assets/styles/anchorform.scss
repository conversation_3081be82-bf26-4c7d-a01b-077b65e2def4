/* 可选：自定义滚动条样式 */
:deep(.el-tabs__content) {
  overflow-y: auto;
  height: calc(70vh - 55px); /* 减去tab标签的高度 */
}

:deep(.el-tabs__content)::-webkit-scrollbar {
  width: 6px;
}

:deep(.el-tabs__content)::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 3px;
}

.el-anchor {
  position: fixed;
  margin-top: 20px;
}

:deep(.el-anchor-link) {
  padding: 8px 16px;
}

/* 表单区域悬停效果 */
.form-section {
  margin-bottom: 10px;
  padding: 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.form-section:hover {
  background-color: var(--el-fill-color-light);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

/* 标题样式优化 */
.section-title {
  position: relative;
  margin-bottom: 24px;
  padding-left: 12px;
  border-left: 4px solid var(--el-color-primary);
  line-height: 20px;
  transition: all 0.3s ease;
  display: flex; /* 添加flex布局 */
  align-items: center; /* 垂直居中 */
}

.title-text {
  font-size: 14px; /* 调整字体大小与其他一致 */
  font-weight: 500;
  color: var(--el-text-color-primary);
  transition: all 0.3s ease;
  margin-right: 12px; /* 添加右侧间距 */
}

/* 修改底边为右侧分隔线 */
.section-title::after {
  content: '';
  flex: 1; /* 让线条填充剩余空间 */
  height: 1px;
  background: var(--el-border-color-lighter);
  transition: all 0.3s ease;
  margin-left: 8px; /* 与文字保持间距 */
}

.form-section:hover .section-title::after {
  background: var(--el-border-color); /* hover时颜色稍深 */
}

/* 设置滚动区域 */
:deep(.el-dialog__body) {
  height: 70vh;
  overflow-y: auto;
}
