<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="标题" prop="title">
                  <el-input v-model="queryParams.title" placeholder="请输入标题" clearable @keyup.enter="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="模块名称" prop="moduleKey">
                  <el-select v-model="queryParams.moduleKey" placeholder="请选择模块名称" clearable>
                    <el-option v-for="dict in dict_sys_module" :key="dict.value" :label="dict.label" :value="dict.value" @change="handleQuery" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="状态" prop="status">
                  <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
                    <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" @change="handleQuery" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="更新时间" style="width: 308px">
                  <el-date-picker
                    v-model="dateRangeUpdateTime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                  <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['org:helpDoc:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['org:helpDoc:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['org:helpDoc:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['org:helpDoc:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="helpDocList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="模块名称" prop="moduleKey">
          <template #default="scope">
            <dict-tag :options="dict_sys_module" :value="scope.row.moduleKey" />
          </template>
        </el-table-column>
        <el-table-column label="标题" prop="title">
          <template #default="scope">
            <el-tooltip :content="scope.row.title" placement="top">
              <el-link type="primary" @click="openLink(scope.row.url)">{{ scope.row.title }}</el-link>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="排序" prop="orderNum" />
        <el-table-column label="状态" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" />
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['org:helpDoc:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['org:helpDoc:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改帮助文档对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="helpDocFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="模块名称" prop="moduleKey">
          <dict-select v-model="form.moduleKey" placeholder="请选择模块名称" dict-key="dict_sys_module" />
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="链接" prop="url">
          <el-input v-model="form.url" placeholder="请输入链接" />
        </el-form-item>
        <el-form-item label="排序" prop="orderNum">
          <el-input-number v-model="form.orderNum" placeholder="请输入排序" style="width: 90%" />
          <el-tooltip content="获取最大排序" placement="top">
            <el-button link plain icon="MagicStick" class="ml-2" @click="getExistHelpDocCount"></el-button>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="HelpDoc" lang="ts">
import { listHelpDoc, getHelpDoc, delHelpDoc, addHelpDoc, updateHelpDoc, existHelpDocCount } from '@/api/org/helpDoc';
import { HelpDocVO, HelpDocQuery, HelpDocForm } from '@/api/org/helpDoc/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { dict_sys_module, sys_normal_disable } = toRefs<any>(proxy?.useDict('dict_sys_module', 'sys_normal_disable'));

const helpDocList = ref<HelpDocVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const helpDocFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const dialogEditStatus = ref(1); //1:新增，0：编辑

const initFormData: HelpDocForm = {
  id: undefined,
  title: undefined,
  url: undefined,
  moduleKey: undefined,
  orderNum: undefined,
  status: '0',
  remark: undefined
};
const data = reactive<PageData<HelpDocForm, HelpDocQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: undefined,
    moduleKey: undefined,
    status: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
    url: [
      { required: true, message: '链接不能为空', trigger: 'blur' },
      { type: 'url', message: '请输入正确的链接', trigger: 'blur' }
    ],
    moduleKey: [{ required: true, message: '模块名称不能为空', trigger: 'change' }],
    orderNum: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '状态不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询帮助文档列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listHelpDoc(queryParams.value);
  helpDocList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  helpDocFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: HelpDocVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加帮助文档';
  dialogEditStatus.value = 1;
  getExistHelpDocCount();
};

/** 修改按钮操作 */
const handleUpdate = async (row?: HelpDocVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getHelpDoc(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改帮助文档';
  dialogEditStatus.value = 0;
};

/** 提交按钮 */
const submitForm = () => {
  helpDocFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateHelpDoc(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addHelpDoc(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: HelpDocVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除帮助文档编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delHelpDoc(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'org/helpDoc/export',
    {
      ...queryParams.value
    },
    `helpDoc_${new Date().getTime()}.xlsx`
  );
};

/** 获取已存在帮助文档的数量 */
const getExistHelpDocCount = async () => {
  const res = await existHelpDocCount();
  if (res >= 0) {
    form.value.orderNum = res + dialogEditStatus.value;
  }
};
// 打开链接
const openLink = (url: string) => {
  window.open(url, '_blank');
};

onMounted(() => {
  getList();
});
</script>
