// 帮助文档选择组件

import { ref } from 'vue';
import { listHelpDoc } from '@/api/org/helpDoc';
import type { HelpDocVO } from '@/api/org/helpDoc/types';

export const useHelpDocSelect = () => {
  const helpDocList = ref<HelpDocVO[]>([]);
  const helpDocLoading = ref(false);

  const loadHelpDocList = async (moduleKey?: string) => {
    try {
      helpDocLoading.value = true;
      const res = await listHelpDoc({ pageNum: 1, pageSize: 50, status: '0', moduleKey: moduleKey });
      helpDocList.value = res.rows;
    } finally {
      helpDocLoading.value = false;
    }
  };

  return {
    helpDocList,
    helpDocLoading,
    loadHelpDocList
  };
};
