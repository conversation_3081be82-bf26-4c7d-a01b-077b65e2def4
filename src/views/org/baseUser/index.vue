<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="登录手机" prop="loginPhone" style="width: 100%">
                  <el-input v-model="queryParams.loginPhone" placeholder="请输入登录手机" clearable @keyup.enter="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="用户名称" prop="userName" style="width: 100%">
                  <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable @keyup.enter="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="性别" prop="gender" style="width: 100%">
                  <el-select
                    v-model="queryParams.gender"
                    placeholder="请选择性别"
                    clearable
                    filterable
                    @keyup.enter="handleQuery"
                    @change="handleQuery"
                  >
                    <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="是否黑名单" prop="isBlacklist" style="width: 100%">
                  <el-select
                    v-model="queryParams.isBlacklist"
                    placeholder="选择是否黑名单"
                    clearable
                    filterable
                    @keyup.enter="handleQuery"
                    @change="handleQuery"
                  >
                    <el-option v-for="dict in dict_blcklist" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="更新时间" style="width: 100%">
                  <el-date-picker
                    v-model="dateRangeUpdateTime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                  <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row>
          <div style="color: red" class="md-">基础用户是按照登录手机作为唯一标识进行创建的，当前页面仅不对业务人员开放！</div>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="baseUserList">
        <el-table-column type="index" width="50" label="序号" />
        <el-table-column v-if="false" label="Id" prop="id" />
        <el-table-column label="登录手机" prop="loginPhone" />
        <el-table-column label="用户名称" prop="userName" />

        <el-table-column label="用户头像" prop="avatarUrlUrl" width="100">
          <template #default="scope">
            <image-preview :src="scope.row.avatarUrlUrl" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="性别" prop="gender" width="150">
          <template #default="scope">
            <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
          </template>
        </el-table-column>
        <el-table-column label="黑名单" prop="isBlacklist" width="150">
          <template #default="scope">
            <dict-tag :options="dict_blcklist" :value="scope.row.isBlacklist" />
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" width="200" />
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{m}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="150">
          <template #default="scope">
            <el-tooltip content="编辑" placement="top">
              <el-button v-hasPermi="['org:baseUser:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <!-- <el-tooltip content="重置密码" placement="top">
              <el-button v-hasPermi="['org:baseUser:resetPwd']" link type="primary" icon="Key" @click="handleResetPwd(scope.row)"></el-button>
            </el-tooltip> -->
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改Base User对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="800px" append-to-body>
      <el-form ref="baseUserFormRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="登录手机" prop="loginPhone">
          <el-input v-model="form.loginPhone" disabled />
        </el-form-item>
        <el-form-item label="用户名称" prop="userName">
          <el-input v-model="form.userName" disabled />
        </el-form-item>
        <el-form-item label="头像" prop="avatarUrl">
          <image-upload v-model="form.avatarUrl" :limit="1" />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-select v-model="form.gender" placeholder="select Gender" :disabled="dialogEditStatus != 1">
            <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="黑名单" prop="isBlacklist">
          <el-radio-group v-model="form.isBlacklist">
            <el-radio v-for="dict in dict_blcklist" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确定</el-button>
          <el-button @click="cancel">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 重置密码 -->
    <el-dialog v-model="resetPwdDialogVisible" title="重置基础用户密码" width="400px">
      <el-form ref="resetPwdFormRef" :model="resetPwdForm" :rules="resetPwdRules" label-width="140px" label-position="top">
        <el-form-item label="登录手机" prop="loginPhone">
          <el-input v-model="resetPwdForm.loginPhone" disabled />
        </el-form-item>
        <el-form-item label="User Name" prop="userName">
          <el-input v-model="resetPwdForm.userName" disabled />
        </el-form-item>
        <el-form-item label="新密码" prop="password">
          <el-input v-model="resetPwdForm.password" placeholder="请输入新密码，如‘bgy123456’ " />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitResetPwdForm">确定</el-button>
          <el-button @click="cancelResetPwd">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 页面操作提醒 -->
    <el-dialog v-model="tipDialogVisible" title="重要提醒" width="40%">
      <span>此页面为运维页面，不要轻易通过这里修改信息。</span>
      <div>
        如果你是运维人员，可以通过这里对用户进行「<span style="color: red; font-weight: bold; font-size: larger">黑名单</span
        >」设置，一个用户被设置成为黑名单之后，则不能再访问系统。
      </div>
      <div style="color: red">如果你真的要修改，希望你知道你在做的事情对系统影响很大!</div>
    </el-dialog>
  </div>
</template>

<script setup name="BaseUser" lang="ts">
import { login } from '@/api/login';
import { listBaseUser, getBaseUser, updateBaseUser, resetPwd } from '@/api/org/baseUser/index';
import { BaseUserVO, BaseUserQuery, BaseUserForm, ResetPwdForm } from '@/api/org/baseUser/types';
import { to } from 'await-to-js';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { dict_blcklist, sys_user_sex } = toRefs<any>(proxy?.useDict('dict_blcklist', 'sys_user_sex'));

const baseUserList = ref<BaseUserVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const dialogEditStatus = ref(0); // 编辑状态 1:新增 0:编辑
const tipDialogVisible = ref(true);

const queryFormRef = ref<ElFormInstance>();
const baseUserFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: BaseUserForm = {
  id: undefined,
  loginPhone: undefined,
  userName: undefined,
  password: undefined,
  avatarUrl: undefined,
  gender: undefined,
  isBlacklist: '0',
  remark: undefined
};
const data = reactive<PageData<BaseUserForm, BaseUserQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    loginPhone: undefined,
    userName: undefined,
    gender: undefined,
    isBlacklist: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    loginPhone: [
      { required: true, message: '登录手机不能为空', trigger: 'blur' },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: '请输入正确的手机号码',
        trigger: ['blur', 'change']
      }
    ],
    blacklistFlag: [{ required: true, message: 'Blacklist Flag cannot be empty', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询Base User列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listBaseUser(queryParams.value);
  baseUserList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  dialogEditStatus.value = 0;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  baseUserFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = 'Add Base User';
  dialogEditStatus.value = 1;
};

/** 修改按钮操作 */
const handleUpdate = async (row?: BaseUserVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getBaseUser(_id);
  Object.assign(form.value, res.data);
  console.log('form', form.value);
  dialog.visible = true;
  dialog.title = 'Edit Base User';
  dialogEditStatus.value = 0;
};

/** 提交按钮 */
const submitForm = () => {
  baseUserFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateBaseUser(form.value).finally(() => (buttonLoading.value = false));
      } else {
        console.log('不存在新建按钮');
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

// 重置密码
const resetPwdDialogVisible = ref(false);
const resetPwdFormRef = ref<ElFormInstance>();
const initResetPwdForm = reactive<ResetPwdForm>({
  loginPhone: undefined,
  userName: undefined,
  password: undefined
});
const resetPwdForm = reactive<ResetPwdForm>({ ...initResetPwdForm });
const resetPwdRules = reactive<ElFormRules>({
  password: [
    { required: true, message: '密码不能为空', trigger: 'blur' },
    { min: 8, max: 56, message: 'Password length must be between 8 and 56 characters', trigger: 'blur' }
  ]
});

/** 重置密码按钮操作 */
const handleResetPwd = (row: BaseUserVO) => {
  resetResetPwd();
  resetPwdDialogVisible.value = true;
  resetPwdForm.id = row.id;
  resetPwdForm.loginPhone = row.loginPhone;
  resetPwdForm.userName = row.userName;
};

// 重置密码提交表单
const resetResetPwd = () => {
  Object.assign(resetPwdForm, initResetPwdForm);
  resetPwdFormRef.value?.resetFields();
};

// 取消重置密码
const cancelResetPwd = () => {
  resetResetPwd();
  resetPwdDialogVisible.value = false;
};

/** 提交重置密码表单 */
const submitResetPwdForm = () => {
  resetPwdFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      await resetPwd(resetPwdForm);
      proxy?.$modal.msgSuccess('Operation successful');
      resetResetPwd();
      resetPwdDialogVisible.value = false;
      await getList();
      buttonLoading.value = false;
    }
  });
};

onMounted(() => {
  getList();
});
</script>
