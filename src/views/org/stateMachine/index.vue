<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="字典类型" prop="dictType">
                  <el-select v-model="queryParams.dictType" @change="handleQuery" disabled>
                    <el-option v-for="item in typeOptions" :key="item.dictId" :label="item.dictName" :value="item.dictType" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="事件名称" prop="eventName">
                  <el-input v-model="queryParams.eventName" placeholder="请输入事件名称" clearable @keyup.enter="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="当前状态" prop="currentStatus">
                  <el-select v-model="queryParams.currentStatus" placeholder="请选择当前状态" @change="handleQuery" clearable>
                    <el-option v-for="item in dictDataList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="下一状态" prop="nextStatus">
                  <el-select v-model="queryParams.nextStatus" placeholder="请选择下一状态" @change="handleQuery" clearable>
                    <el-option v-for="item in dictDataList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="事件名称" prop="eventName">
                  <el-input v-model="queryParams.eventName" placeholder="请输入事件" clearable @keyup.enter="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="更新时间" style="width: 308px">
                  <el-date-picker
                    v-model="dateRangeUpdateTime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="更新人" prop="updateBy">
                  <el-input v-model="queryParams.updateBy" placeholder="请输入更新人" clearable @keyup.enter="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                  <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['org:stateMachine:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['org:stateMachine:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['org:stateMachine:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['org:stateMachine:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="stateMachineList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="当前状态" prop="currentStatus">
          <template #default="scope">
            {{ findDictLabel(scope.row.currentStatus) }}
          </template>
        </el-table-column>
        <el-table-column label="当前状态编码" prop="currentStatus" />
        <el-table-column label="下一状态" prop="nextStatus">
          <template #default="scope">
            {{ findDictLabel(scope.row.nextStatus) }}
          </template>
        </el-table-column>
        <el-table-column label="下一状态编码" prop="nextStatus" />
        <el-table-column label="事件名称" prop="eventName" />
        <el-table-column label="排序" prop="sort" width="60" />
        <el-table-column label="备注" prop="remark" width="350" show-overflow-tooltip> </el-table-column>
        <!-- <el-table-column label="更新人" prop="updateBy" /> -->
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="150">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['org:stateMachine:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['org:stateMachine:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改状态机对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="stateMachineFormRef" :model="form" :rules="rules" label-width="80px">
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="字典类型" prop="dictType">
              <el-select v-model="form.dictType" disabled>
                <el-option v-for="item in typeOptions" :key="item.dictId" :label="item.dictName" :value="item.dictType" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="当前状态" prop="currentStatus">
              <el-select v-model="form.currentStatus" placeholder="请选择当前状态" @change="handleCurrentStatusChange">
                <el-option v-for="item in dictDataList" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="下一状态" prop="nextStatus">
              <el-select v-model="form.nextStatus" placeholder="请选择下一状态" :disabled="!form.currentStatus" @change="handleCompareStatus">
                <el-option v-for="item in dictDataListForNextStatus" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="事件名称" prop="eventName">
              <el-input v-model="form.eventName" placeholder="请输入事件名称" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="排序" prop="sort">
              <el-input-number
                v-model="form.sort"
                placeholder="请输入排序"
                :min="0"
                :max="1000"
                controls-position="right"
                :disabled="!form.currentStatus"
              />
              <el-tooltip content="获取最大排序" placement="top" v-if="form.currentStatus">
                <el-button link plain icon="MagicStick" class="ml-2" @click="getExistNextStateCount"></el-button>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="StateMachine" lang="ts">
import {
  listStateMachine,
  getStateMachine,
  delStateMachine,
  addStateMachine,
  updateStateMachine,
  getStateMachineCount
} from '@/api/org/stateMachine';
import { StateMachineVO, StateMachineQuery, StateMachineForm } from '@/api/org/stateMachine/types';

import { optionselect as getDictOptionselect, getType } from '@/api/system/dict/type';
import { listData, getData, delData, addData, updateData, countDictLableByDictType } from '@/api/system/dict/data';
import { DictTypeVO } from '@/api/system/dict/type/types';
import { DictDataForm, DictDataQuery, DictDataVO } from '@/api/system/dict/data/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

import { useDictDataSelect } from '@/views/system/dict/dictDataSelect';
const { dictDataList, dictDataLoading, getDictDataList } = useDictDataSelect();
const dictDataListForNextStatus = ref<DictDataVO[]>([]);

const stateMachineList = ref<StateMachineVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const typeOptions = ref<DictTypeVO[]>([]);
const queryFormRef = ref<ElFormInstance>();
const stateMachineFormRef = ref<ElFormInstance>();

const dialogEdit = ref(false); // 是否是编辑状态,true 为编辑状态,false 为新增状态

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: StateMachineForm = {
  id: undefined,
  dictType: localStorage.getItem('stateMachine_dictType') || '',
  currentStatus: undefined,
  nextStatus: undefined,
  sort: undefined,
  remark: undefined
};
const data = reactive<PageData<StateMachineForm, StateMachineQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dictType: localStorage.getItem('stateMachine_dictType') || '',
    currentStatus: undefined,
    nextStatus: undefined,
    sort: undefined,
    updateBy: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    dictType: [{ required: true, message: '字典类型不能为空', trigger: 'change' }],
    eventName: [{ required: true, message: '事件名称不能为空', trigger: 'blur' }],
    currentStatus: [{ required: true, message: '当前状态不能为空', trigger: 'change' }],
    nextStatus: [{ required: true, message: '下一状态不能为空', trigger: 'change' }],
    sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询状态机列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listStateMachine(queryParams.value);
  stateMachineList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  stateMachineFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: StateMachineVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加状态机';
  dialogEdit.value = false;
};

/** 修改按钮操作 */
const handleUpdate = async (row?: StateMachineVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getStateMachine(_id);
  // 确保在设置表单值之前已经获取到最新的字典数据
  await getDictDataList(res.data.dictType);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改状态机';
  dialogEdit.value = true;
  assembleNextStatusList(); // 组装下一状态的列表数据
};

/** 提交按钮 */
const submitForm = () => {
  stateMachineFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateStateMachine(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addStateMachine(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: StateMachineVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除状态机编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delStateMachine(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'org/stateMachine/export',
    {
      ...queryParams.value
    },
    `stateMachine_${new Date().getTime()}.xlsx`
  );
};

/** 查询字典类型列表 */
const getTypeList = async () => {
  const res = await getDictOptionselect();
  // 只保留 isStateMachine 为 '1' 的字典类型
  typeOptions.value = res.data.filter((item) => item.isStateMachine === '1');
};

// 外部跳转逻辑维护
// 获取路由实例
const route = useRoute();
const router = useRouter();
const handleOpenPage = () => {
  const dictType = route.query.dictType;
  if (dictType) {
    queryParams.value.dictType = dictType as string;
    initFormData.dictType = dictType as string;
    // 存储到localStorage
    localStorage.setItem('stateMachine_dictType', dictType as string);
  }
  router.replace({ query: {} });
};

// 比较状态
const handleCompareStatus = () => {
  if (form.value.currentStatus === form.value.nextStatus) {
    proxy?.$modal.msgError('当前状态和下一状态不能相同');
    form.value.nextStatus = undefined;
  }
};

// 查找字典标签,支持列表
const findDictLabel = (value: string) => {
  return dictDataList.value.find((item) => item.dictValue === value)?.dictLabel;
};

/** 获取最大排序 */
const getExistNextStateCount = async () => {
  const query: StateMachineQuery = {
    currentStatus: form.value.currentStatus,
    dictType: form.value.dictType,
    pageNum: 1,
    pageSize: 10
  };
  const res = await getStateMachineCount(query);
  form.value.sort = Number(res) + (dialogEdit.value ? 0 : 1);
};

/** 当前状态改变 */
const handleCurrentStatusChange = () => {
  getExistNextStateCount();
  handleCompareStatus();
  form.value.nextStatus = undefined;
  assembleNextStatusList(); // 组装下一状态的列表数据
};

// 组装下一状态的列表数据
const assembleNextStatusList = () => {
  // 如果是编辑状态，应该包含当前记录的下一状态
  const init = dictDataList.value.filter((item) => {
    if (dialogEdit.value && form.value.nextStatus === item.dictValue) {
      return true;
    }
    return item.dictValue !== form.value.currentStatus;
  });

  // 排除已经存在的下一状态（除了当前编辑记录的状态）
  const exist = stateMachineList.value.filter(
    (item) => item.currentStatus === form.value.currentStatus && (!dialogEdit.value || item.id !== form.value.id)
  );

  dictDataListForNextStatus.value = init.filter((item) => !exist.some((existItem) => existItem.nextStatus === item.dictValue));
};

onMounted(async () => {
  await getTypeList();
  handleOpenPage();
  const dictType = localStorage.getItem('stateMachine_dictType');
  if (dictType) {
    await getDictDataList(dictType);
  }
  await getList();
});
</script>

<style scoped>
.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px; /* 或其他合适的宽度 */
}
</style>
