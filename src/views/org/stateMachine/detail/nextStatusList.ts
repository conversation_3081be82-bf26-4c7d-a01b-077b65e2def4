// 基于当前状态查询下一状态列表，以支持具体的应用

import { ref } from 'vue';
import { listStateMachine } from '@/api/org/stateMachine';
import type { StateMachineVO } from '@/api/org/stateMachine/types';
import type { DictValueVO } from './types';

export const useNextStatusList = () => {
  const nextStatusList = ref<StateMachineVO[]>([]);
  const nextStatusLoading = ref(false);
  const filteredStatus = ref<DictValueVO[]>([]);

  const loadNextStatusList = async (dictType?: string, currentStatus?: string) => {
    try {
      nextStatusLoading.value = true;
      const res = await listStateMachine({ dictType, currentStatus, pageNum: 1, pageSize: 10 });
      nextStatusList.value = res.rows;
    } finally {
      nextStatusLoading.value = false;
    }
  };

  const gainNextStatusList = async (dictType: string, dictOptions: DictValueVO[], currentStatus: string) => {
    await loadNextStatusList(dictType, currentStatus);
    const currentStatusOption = dictOptions.filter((item) => item.value === currentStatus);
    const nextStatusOptions = dictOptions.filter((item) => nextStatusList.value.some((next) => next.nextStatus === item.value));
    filteredStatus.value = [...currentStatusOption, ...nextStatusOptions];
  };

  return {
    nextStatusList,
    nextStatusLoading,
    filteredStatus,
    loadNextStatusList,
    gainNextStatusList
  };
};
