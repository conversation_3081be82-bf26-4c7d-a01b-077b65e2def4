// 这里是状态机组件的说明文档

# 状态机组件使用说明

## nextStatusList.ts

这是一个用于获取和过滤状态机下一状态列表的 Vue3 组合式 API。主要用于处理状态流转时可选状态的获取和过滤。

### 基本用法

```typescript
import { useNextStatusList } from './nextStatusList';

const {
  nextStatusList, // 下一状态列表
  nextStatusLoading, // 加载状态
  filteredStatus, // 过滤后的状态列表
  loadNextStatusList, // 加载下一状态列表方法
  gainNextStatusList // 获取并过滤状态列表方法
} = useNextStatusList();
```

### API 说明

#### 返回值

| 名称               | 类型                  | 说明                     |
| ------------------ | --------------------- | ------------------------ |
| nextStatusList     | Ref<StateMachineVO[]> | 下一状态列表             |
| nextStatusLoading  | Ref<boolean>          | 加载状态标识             |
| filteredStatus     | Ref<DictValueVO[]>    | 过滤后的状态列表         |
| loadNextStatusList | Function              | 加载下一状态列表的方法   |
| gainNextStatusList | Function              | 获取并过滤状态列表的方法 |

#### 方法说明

**loadNextStatusList**

```typescript
// 直接加载下一状态列表
await loadNextStatusList('dict_clue_status', 'current_status');
```

参数:

- dictType: string - 字典类型
- currentStatus: string - 当前状态

**gainNextStatusList**

```typescript
// 获取过滤后的状态列表
await gainNextStatusList(dictType, dictOptions, currentStatus);
```

参数:

- dictType: string - 字典类型,如 'dict_clue_status'
- dictOptions: DictValueVO[] - 字典选项列表
- currentStatus: string - 当前状态

### 使用示例

以下是在业务组件中使用状态机组件的完整示例:

```typescript
import { useNextStatusList } from './nextStatusList';

// 1. 获取状态机相关方法和数据
const {
  nextStatusList,
  nextStatusLoading,
  filteredStatus,
  gainNextStatusList
} = useNextStatusList();

// 2. 在修改状态时使用
const handleUpdate = async (row?: CustomerVO) => {
  const currentStatus = row?.status;
  // 获取当前状态可选的下一状态列表
  await gainNextStatusList('dict_customer_status', dict_customer_status.value, currentStatus);
};

// 3. 在模板中使用过滤后的状态列表
<el-select v-model="form.status">
  <el-option
    v-for="item in filteredStatus"
    :key="item.value"
    :label="item.label"
    :value="item.value"
  />
</el-select>
```

### 实现原理

1. 状态机组件通过调用后端API获取状态流转规则
2. 根据当前状态过滤出可选的下一状态列表
3. 将过滤后的状态列表与原字典选项合并,生成最终的状态选项

### 注意事项

1. 使用 `gainNextStatusList` 时需要传入:

   - 字典类型(dictType)
   - 完整的字典选项列表(dictOptions)
   - 当前状态(currentStatus)

2. 状态机组件会自动处理状态过滤逻辑:

   - 保留当前状态
   - 添加可选的下一状态
   - 移除不可选状态

3. 组件内部会处理加载状态,可以通过 `nextStatusLoading` 来控制UI的加载效果

4. 该组件主要用于状态流转场景,比如:
   - 客户状态管理
   - 线索状态管理
   - 工单状态流转
   - 审批流程状态

### 类型定义

```typescript
interface StateMachineVO {
  id: number | string;
  dictType: string; // 字典类型
  currentStatus: string; // 当前状态
  nextStatus: string; // 下一状态
  createBy?: string; // 创建者
  createTime?: string; // 创建时间
  updateBy?: string; // 更新人
  updateTime?: string; // 更新时间
}

interface DictValueVO {
  dictType: string; // 字典类型
  value: string; // 状态值
  label: string; // 状态标签
  listClass?: string; // 表格回显样式
  cssClass?: string; // CSS样式
  isDefault?: string; // 是否默认
  status?: string; // 状态
  remark?: string; // 备注
}
```

### 相关文件

- src/views/org/stateMachine/detail/nextStatusList.ts - 状态机核心实现
- src/views/org/stateMachine/detail/types.ts - 类型定义
- src/api/system/dict/value/types.ts - 字典相关类型

### 使用场景示例

1. 客户状态管理(customer/index.vue):

```typescript
// 修改客户状态
const handleUpdate = async (row?: CustomerVO) => {
  const currentStatus = row?.status;
  await gainNextStatusList('dict_customer_status', dict_customer_status.value, currentStatus);
};
```

2. 线索状态管理(clue/index.vue):

```typescript
// 修改线索状态
const handleUpdate = async (row?: ClueVO) => {
  const currentStatus = row?.status;
  await gainNextStatusList('dict_clue_status', dict_clue_status.value, currentStatus);
};
```
