<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-if="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="120px">
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="组织角色" prop="orgName">
                  <el-select v-model="queryParams.orgRoleKey" placeholder="请选择组织角色" clearable @change="handleQuery">
                    <el-option v-for="dict in dict_org_role" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="组织名称" prop="orgName">
                  <el-input v-model="queryParams.orgName" placeholder="请输入组织名称" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="状态" prop="status">
                  <el-select v-model="queryParams.status" placeholder="请选择状态" clearable @change="handleQuery">
                    <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="更新时间" style="width: 100%">
                  <el-date-picker
                    v-model="dateRangeUpdateTime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                    @change="handleQuery"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                  <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </transition>

    <!-- 列表 -->
    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['org:org:add']" type="primary" plain icon="Plus" @click="handleAdd">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['org:org:export']" type="warning" plain icon="Download" @click="handleExport">下载</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-tooltip content="如果你有部门管理的权限，前往部门管理" placement="top">
              <el-button v-hasPermi="['system:dept:query']" type="default" plain icon="Position" @click="goToDept"></el-button>
            </el-tooltip>
          </el-col>
          <right-toolbar v-model:show-search="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <!-- 表格 -->
      <el-table v-loading="loading" :data="orgList">
        <el-table-column type="index" width="50" align="center" label="序号" />
        <el-table-column v-if="false" label="Id" prop="id" />
        <el-table-column label="组织名称" prop="orgName" min-width="150">
          <template #default="scope">
            <el-tooltip content="查看该组织的部门设置" placement="top">
              <el-link type="primary" @click="handleOrgNameClick(scope.row)">{{ scope.row.orgName }}</el-link>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="排序" prop="orderNum" min-width="100" />
        <el-table-column label="组织角色" prop="orgRoleKey" min-width="150">
          <template #default="scope">
            <dict-tag :options="dict_org_role" :value="scope.row.orgRoleKey" />
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" min-width="150">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="250" />
        <el-table-column label="更新时间" prop="updateTime" min-width="150" />
        <el-table-column label="操作" min-width="150" fixed="right">
          <template #default="scope">
            <el-tooltip content="编辑" placement="top">
              <el-button v-hasPermi="['org:org:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row.id)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['org:org:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 添加或修改Org弹窗 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="700px" append-to-body>
      <el-form ref="orgFormRef" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="组织角色" prop="orgRoleKey">
              <el-select v-model="form.orgRoleKey" placeholder="请选择组织角色" clearable>
                <el-option v-for="dict in dict_org_role" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                <template #footer>
                  <!-- 使用 DictOptionFooter 组件 -->
                  <DictOptionFooter :dict-key="'dict_org_role'" @refresh-dict="refreshDictOegRoleDict" />
                </template>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组织名称" prop="orgName">
              <el-input v-model="form.orgName" placeholder="请输入组织名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="orderNum">
              <el-input-number v-model="form.orderNum" :min="0" :max="9999" controls-position="right" />
              <el-tooltip content="获取最大排序" placement="top">
                <el-button link plain icon="MagicStick" class="ml-2" @click="getExistOrgCount"></el-button>
              </el-tooltip>
              <template #label>
                <span>
                  <el-tooltip content="与一级部门的排序一致" placement="top">
                    <el-icon>
                      <question-filled />
                    </el-icon>
                  </el-tooltip>
                  排序
                </span>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组织超管" prop="orgAdmin">
              <el-input v-model="form.orgAdmin" placeholder="要改为选择组件" />
              <template #label>
                <span>
                  <el-tooltip content="超管用户可以管理该组织下的所有用户" placement="top">
                    <el-icon>
                      <question-filled />
                    </el-icon>
                  </el-tooltip>
                  组织超管
                </span>
              </template>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="Logo" prop="orgLogo">
              <image-upload v-model="form.orgLogo" :limit="1" />
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="组织状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
              <template #label>
                <span>
                  <el-tooltip content="停用后，组织将无法使用" placement="top">
                    <el-icon>
                      <question-filled />
                    </el-icon>
                  </el-tooltip>
                  组织状态
                </span>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="门户状态" prop="portalStatus">
              <el-radio-group v-model="form.portalStatus">
                <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
              <template #label>
                <span>
                  <el-tooltip content="后期拓展，如果门户状态为停用，则该组织将无法使用基于该组织角色的门户" placement="top">
                    <el-icon>
                      <question-filled />
                    </el-icon>
                  </el-tooltip>
                  门户状态
                </span>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="3" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="cancel">取消</el-button>
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Org" lang="ts">
import { OrgVO, OrgQuery, OrgForm } from '@/api/org/org/types';
import { listOrg, existOrgCount, addOrg, updateOrg, getOrgById, deleteOrg } from '@/api/org/org/index';
import { onMounted, nextTick, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
const queryFormRef = ref<ElFormInstance>();
const orgFormRef = ref<ElFormInstance>();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { dict_org_role, sys_normal_disable } = toRefs<any>(proxy?.useDict('dict_org_role', 'sys_normal_disable'));

const orgList = ref<OrgVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);
const buttonLoading = ref(false);
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const dialogEditStatus = ref(1); //1:新增，0：编辑

const initFormData: OrgForm = {
  id: undefined,
  orgName: undefined,
  orgRoleKey: undefined,
  orgAdmin: undefined,
  orderNum: undefined,
  status: '0',
  portalStatus: '0',
  orgLogo: undefined,
  remark: undefined
};

const data = reactive<PageData<OrgForm, OrgQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orgName: undefined,
    orgRoleKey: undefined,
    status: undefined,
    portalStatus: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    orgName: [{ required: true, message: '组织名称不能为空', trigger: 'blur' }],
    orgRoleKey: [{ required: true, message: '组织角色不能为空', trigger: 'change' }],
    orderNum: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '状态不能为空', trigger: 'change' }],
    portalStatus: [{ required: true, message: '门户状态不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询Org列表 */
const getList = async () => {
  loading.value = true;
  // 重置params对象
  queryParams.value.params = {
    updateTime: undefined
  };
  // 添加日期范围
  if (dateRangeUpdateTime.value) {
    proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  }
  const res = await listOrg(queryParams.value);
  orgList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  orgFormRef.value?.resetFields();
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  getList();
};

/** 添加按钮操作 */
const handleAdd = () => {
  reset();
  // 根据Id查询Org的信息
  dialog.visible = true;
  dialog.title = '添加组织';
  dialogEditStatus.value = 1;
  getExistOrgCount();
};

/** 修改按钮操作 */
const handleUpdate = async (id?: string | number) => {
  reset();
  const res = await getOrgById(id);
  Object.assign(form.value, res.data);
  console.log('form', form.value);
  dialog.visible = true;
  dialog.title = '修改组织';
  dialogEditStatus.value = 0;
};

/** 提交按钮 */
const submitForm = () => {
  orgFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        console.log('form', form.value);
        await updateOrg(form.value).finally(() => {
          buttonLoading.value = false;
        });
      } else {
        await addOrg(form.value).finally(() => {
          buttonLoading.value = false;
        });
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: OrgVO, skipConfirm: boolean = false) => {
  if (!skipConfirm) {
    await proxy?.$modal.confirm('确定要删除' + row.orgName + '吗？').finally(() => (loading.value = false));
  }
  await deleteOrg(row.id);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    '/org/org/export',
    {
      ...queryParams.value
    },
    `org_${new Date().getTime()}.xlsx`
  );
};

/** 跳转到部门列表页 */
const router = useRouter();
const goToDept = () => {
  router.replace('/redirect' + '/system/dept');
};
// 订阅事件
const refreshDictOegRoleDict = (value) => {
  dict_org_role.value = value;
};

/** 获取Org存在的数量 */
const getExistOrgCount = async () => {
  const res = await existOrgCount();
  if (res >= 0) {
    form.value.orderNum = res + dialogEditStatus.value;
    ElMessage({
      message: '获取到已存在组织的数量',
      type: 'success'
    });
  }
};

const route = useRoute();

// 监听路由变化
watch(
  () => route.query,
  async (query) => {
    if (query.openDialog === 'add') {
      handleAdd();
      router.replace({
        path: route.path,
        query: {}
      });
    } else if (query.openDialog === 'edit' && query.orgId) {
      handleUpdate(query.orgId as string);
      router.replace({
        path: route.path,
        query: {}
      });
    } else if (query.openDialog === 'delete' && query.orgId) {
      const res = await getOrgById(query.orgId as string);
      handleDelete(res.data);
      router.replace({
        path: route.path,
        query: {}
      });
    }
  },
  { immediate: true }
);

/** 点击组织名称 */
const handleOrgNameClick = (row: OrgVO) => {
  const routeUrl = `/system/dept?orgId=${row.id}`;
  window.open(routeUrl);
};

onMounted(() => {
  getList();
});
</script>
