<template>
  <el-dialog :title="title || '选择商品'" v-model="visible" width="70%" :close-on-click-modal="false" destroy-on-close>
    <div class="goods-select-container">
      <!-- 商品搜索 -->
      <el-form :model="queryParams" :inline="true">
        <el-form-item label="商品名称">
          <el-input v-model="queryParams.name" placeholder="请输入商品名称" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="商品编码">
          <el-input v-model="queryParams.code" placeholder="请输入商品编码" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="商品类型" v-if="showMoreCondition">
          <el-select v-model="queryParams.goodsType" placeholder="请选择商品类型" clearable @change="handleQuery">
            <el-option v-for="dict in mall_goods_type" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button link @click="showMoreCondition = !showMoreCondition">
            {{ showMoreCondition ? '收起' : '展开' }}
            <el-icon class="el-icon--right">
              <ArrowUp v-if="showMoreCondition" />
              <ArrowDown v-else />
            </el-icon>
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 商品列表 -->
      <el-table
        :data="goodsList"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
        ref="goodsTableRef"
        border
        highlight-current-row
        style="width: 100%"
        v-loading="loading"
        :row-class-name="tableRowClassName"
      >
        <el-table-column type="selection" width="55" v-if="multiple" :selectable="rowSelectable" />
        <el-table-column type="index" width="50" />
        <el-table-column label="商品名称" prop="name" show-overflow-tooltip />
        <el-table-column label="商品编码" prop="code" width="120" />
        <el-table-column label="图片" prop="imagesUrl" width="80">
          <template #default="scope">
            <image-preview :src="scope.row.images" width="50px" height="50px" :is-id="true" />
          </template>
        </el-table-column>
        <el-table-column label="零售价(元)" prop="currentSalePrice" width="120">
          <template #default="scope">
            {{ formatPrice(scope.row.currentSalePrice) }}
          </template>
        </el-table-column>
        <el-table-column label="市场价(元)" prop="currentMarketPrice" width="120">
          <template #default="scope">
            {{ formatPrice(scope.row.currentMarketPrice) }}
          </template>
        </el-table-column>
        <el-table-column label="销售单位" prop="saleUnit" width="80">
          <template #default="scope">
            <dict-tag :options="mall_goods_unit" :value="scope.row.saleUnit" />
          </template>
        </el-table-column>
        <el-table-column label="库存" prop="stockQty" width="100" />
        <el-table-column label="状态" prop="saleStatus" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.saleStatus === '0' ? 'success' : 'info'">
              {{ scope.row.saleStatus === '0' ? '上架' : '下架' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="multiple ? selectedList.length === 0 : !selectedItem">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { listGoods } from '@/api/mall/goods';
import { getCurrentInstance, toRefs } from 'vue';

const { proxy } = getCurrentInstance();
const { mall_goods_unit, mall_goods_type } = toRefs(proxy?.useDict('mall_goods_unit', 'mall_goods_type'));

const props = defineProps({
  // 是否多选
  multiple: {
    type: Boolean,
    default: false
  },
  // 排除的商品ID列表（已选择的不再显示）
  excludeIds: {
    type: Array,
    default: () => []
  },
  // 对话框标题
  title: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['select']);

// 状态变量
const visible = ref(false);
const loading = ref(false);
const goodsList = ref([]);
const total = ref(0);
const selectedItem = ref(null);
const selectedList = ref([]);
const goodsTableRef = ref(null);
const showMoreCondition = ref(false);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: '',
  code: '',
  goodsType: '',
  // 排除已选择的商品
  excludeIds: computed(() => props.excludeIds)
});

// 获取商品列表
const getGoodsList = async () => {
  loading.value = true;
  try {
    const res = await listGoods(queryParams);
    if (res.code === 200) {
      goodsList.value = res.rows || [];
      total.value = res.total || 0;
    }
  } catch (error) {
    console.error('获取商品列表失败:', error);
    ElMessage.error('获取商品列表失败');
  } finally {
    loading.value = false;
  }
};

// 格式化价格（分转元）
const formatPrice = (price) => {
  if (price === undefined || price === null) return '0.00';
  return (price / 100).toFixed(2);
};

// 处理查询
const handleQuery = () => {
  queryParams.pageNum = 1;
  getGoodsList();
};

// 重置查询
const resetQuery = () => {
  queryParams.name = '';
  queryParams.code = '';
  queryParams.goodsType = '';
  queryParams.pageNum = 1;
  getGoodsList();
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  queryParams.pageSize = size;
  getGoodsList();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  queryParams.pageNum = page;
  getGoodsList();
};

// 判断行是否可选择
const rowSelectable = (row) => {
  return !isDisabled(row);
};

// 处理表格行点击
const handleRowClick = (row, column, event) => {
  // 如果商品已被禁用，则不允许选择
  if (isDisabled(row)) {
    if (row.saleStatus !== '0') {
      ElMessage.warning('已下架的商品不可选择');
    } else if (props.excludeIds.includes(row.id)) {
      ElMessage.warning('该商品已被选择');
    }
    return;
  }

  if (props.multiple) {
    // 多选模式下，点击行切换选中状态
    goodsTableRef.value?.toggleRowSelection(row);
  } else {
    // 单选模式下，设置选中的商品
    selectedItem.value = row;
    // 清除之前的选择并选中当前行
    goodsTableRef.value?.clearSelection();
    goodsTableRef.value?.toggleRowSelection(row, true);
  }
};

// 判断商品是否被禁用（已被选择或已下架）
const isDisabled = (row) => {
  // 已被选择的商品禁用
  const isExcluded = props.excludeIds && props.excludeIds.includes(row.id);
  // 下架的商品禁用（saleStatus !== '0' 表示商品已下架）
  const isOffShelf = row.saleStatus !== '0';

  return isExcluded || isOffShelf;
};

// 处理多选变化
const handleSelectionChange = (selection) => {
  // 多选模式下保存选中的商品列表
  if (props.multiple) {
    selectedList.value = selection;
  } else if (selection.length > 0) {
    // 单选模式下只保存第一个选中的商品
    selectedItem.value = selection[0];
  } else {
    selectedItem.value = null;
  }
};

// 打开对话框
const open = () => {
  visible.value = true;
  // 重置选择状态
  selectedItem.value = null;
  selectedList.value = [];
  // 加载商品列表
  handleQuery();
};

// 取消选择
const handleCancel = () => {
  visible.value = false;
};

// 确认选择
const handleConfirm = () => {
  if (props.multiple) {
    if (selectedList.value.length === 0) {
      ElMessage.warning('请至少选择一个商品');
      return;
    }
    emit('select', selectedList.value);
  } else {
    if (!selectedItem.value) {
      ElMessage.warning('请选择一个商品');
      return;
    }
    emit('select', selectedItem.value);
  }
  visible.value = false;
};

// 表格行样式
const tableRowClassName = ({ row }) => {
  return isDisabled(row) ? 'disabled-row' : '';
};

// 暴露方法
defineExpose({
  open
});
</script>

<style scoped>
.goods-select-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

:deep(.disabled-row) {
  background-color: #f5f7fa;
  color: #909399;
  cursor: not-allowed;
}

:deep(.el-table .el-table__row) {
  cursor: pointer;
}
</style>
