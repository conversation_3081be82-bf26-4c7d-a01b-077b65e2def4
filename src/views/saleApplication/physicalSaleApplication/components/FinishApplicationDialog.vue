<template>
  <el-dialog :title="'完成销售申请'" v-model="visible" width="500px" append-to-body :close-on-click-modal="false" @close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="申请单号" prop="applicationNo">
        <el-input v-model="form.applicationNo" disabled />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="4" />
      </el-form-item>
      <el-form-item label="附件" prop="files">
        <file-upload v-model="form.files" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确 认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { finishPhysicalSaleApplication } from '@/api/saleApplication/physicalSaleApplication';
import { PhysicalSaleApplicationVO } from '@/api/saleApplication/physicalSaleApplication/types';
import { ElMessage } from 'element-plus';

const emit = defineEmits(['success', 'cancel']);
const formRef = ref();
const visible = ref(false);
const submitLoading = ref(false);

const form = reactive({
  id: '',
  applicationNo: '',
  remark: '',
  files: ''
});

const rules = reactive({
  remark: [{ required: true, message: '请输入备注', trigger: 'blur' }],
  files: [{ required: true, message: '请上传附件', trigger: 'change' }]
});

/**
 * 打开对话框
 */
const openDialog = async (row: PhysicalSaleApplicationVO) => {
  visible.value = true;
  form.id = row.id as string;
  form.applicationNo = row.applicationNo;
  form.remark = row.remark || '';
  form.files = '';
};

/**
 * 关闭对话框
 */
const handleClose = () => {
  visible.value = false;
  resetForm();
  emit('cancel');
};

/**
 * 重置表单
 */
const resetForm = () => {
  form.id = '';
  form.applicationNo = '';
  form.remark = '';
  form.files = '';
  formRef.value?.resetFields();
};

/**
 * 提交表单
 */
const submitForm = async () => {
  formRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      try {
        submitLoading.value = true;
        await finishPhysicalSaleApplication(form.id, form.remark, form.files);
        ElMessage.success('完成销售申请成功');
        handleClose();
        emit('success');
      } catch (error) {
        console.error('完成销售申请失败', error);
      } finally {
        submitLoading.value = false;
      }
    }
  });
};

// 向父组件暴露方法
defineExpose({
  openDialog
});
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
