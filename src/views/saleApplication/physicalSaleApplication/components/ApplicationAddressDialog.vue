<template>
  <!-- 收货地址对话框 -->
  <el-dialog v-model="visible" :title="title" width="600px" destroy-on-close :close-on-click-modal="false" @close="handleClose">
    <el-form ref="addressFormRef" :model="addressForm" :rules="addressRules" label-width="100px" status-icon>
      <el-form-item label="收货人姓名" prop="recipientName">
        <el-input v-model="addressForm.recipientName" placeholder="请输入收货人姓名" />
      </el-form-item>
      <el-form-item label="手机号" prop="recipientPhone">
        <el-input v-model="addressForm.recipientPhone" placeholder="请输入手机号" maxlength="11" />
      </el-form-item>
      <el-form-item label="所在区域" required>
        <div class="region-selector">
          <el-select
            v-model="addressForm.recipientProvince"
            placeholder="省份"
            class="region-select"
            clearable
            @change="handleProvinceChange"
            @blur="validateRegion"
          >
            <el-option v-for="item in provinceOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select
            v-model="addressForm.recipientCity"
            placeholder="城市"
            class="region-select"
            clearable
            @change="handleCityChange"
            :disabled="!addressForm.recipientProvince"
            @blur="validateRegion"
          >
            <el-option v-for="item in cityOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select
            v-model="addressForm.recipientArea"
            placeholder="区县"
            class="region-select"
            clearable
            :disabled="!addressForm.recipientCity"
            @blur="validateRegion"
          >
            <el-option v-for="item in districtOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="error-message" v-if="regionError">{{ regionError }}</div>
      </el-form-item>
      <el-form-item label="详细地址" prop="recipientAddress">
        <el-input v-model="addressForm.recipientAddress" type="textarea" :rows="2" placeholder="请输入详细地址" />
      </el-form-item>
      <el-form-item label="收货备注" prop="remark">
        <el-input v-model="addressForm.remark" placeholder="请输入收货备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="submitAddressForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { Plus, Minus } from '@element-plus/icons-vue';
import { regionData } from 'element-china-area-data';
import { ref, reactive, watch, defineProps, defineEmits } from 'vue';

// 组件接收的属性
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  dialogTitle: {
    type: String,
    default: '添加收货地址'
  },
  dialogType: {
    type: String,
    default: 'add'
  },
  applicationId: {
    type: [String, Number],
    default: ''
  },
  addressInfo: {
    type: Object,
    default: () => ({})
  }
});

// 事件发射
const emit = defineEmits(['update:dialogVisible', 'success', 'cancel']);

// 表单引用
const addressFormRef = ref();

// 控制弹窗显示
const visible = ref(props.dialogVisible);
const title = ref(props.dialogTitle);

// 地址表单数据
const addressForm = reactive({
  id: undefined,
  applicationId: props.applicationId,
  recipientName: '',
  recipientPhone: '',
  recipientProvince: '', // 省份编码
  recipientProvinceName: '', // 省份名称
  recipientCity: '', // 城市编码
  recipientCityName: '', // 城市名称
  recipientArea: '', // 区县编码
  recipientAreaName: '', // 区县名称
  recipientAddress: '', // 详细地址
  remark: ''
});

// 地址表单校验规则
const addressRules = {
  recipientName: [{ required: true, message: '请输入收货人姓名', trigger: 'blur' }],
  recipientPhone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  recipientAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }]
};

// 所在区域校验错误信息
const regionError = ref('');

// 省市区选项
const provinceOptions = ref(regionData);
const cityOptions = ref([]);
const districtOptions = ref([]);

// 校验所在区域
const validateRegion = () => {
  if (!addressForm.recipientProvince) {
    regionError.value = '请选择省份';
    return false;
  }
  if (!addressForm.recipientCity) {
    regionError.value = '请选择城市';
    return false;
  }
  if (!addressForm.recipientArea) {
    regionError.value = '请选择区县';
    return false;
  }
  regionError.value = '';
  return true;
};

// 处理省份变更
const handleProvinceChange = (value) => {
  addressForm.recipientCity = '';
  addressForm.recipientArea = '';
  districtOptions.value = [];

  // 根据选择的省份加载对应的城市
  const selectedProvince = provinceOptions.value.find((province) => province.value === value);
  if (selectedProvince && selectedProvince.children) {
    cityOptions.value = selectedProvince.children;
  } else {
    cityOptions.value = [];
  }
};

// 处理城市变更
const handleCityChange = (value) => {
  addressForm.recipientArea = '';

  // 根据选择的城市加载对应的区县
  const selectedProvince = provinceOptions.value.find((province) => province.value === addressForm.recipientProvince);
  if (selectedProvince && selectedProvince.children) {
    const selectedCity = selectedProvince.children.find((city) => city.value === value);
    if (selectedCity && selectedCity.children) {
      districtOptions.value = selectedCity.children;
    } else {
      districtOptions.value = [];
    }
  } else {
    districtOptions.value = [];
  }
};

// 重置地址表单
const resetAddressForm = () => {
  addressForm.id = undefined;
  addressForm.portion = 1;
  addressForm.recipientName = '';
  addressForm.recipientPhone = '';
  addressForm.recipientProvince = '';
  addressForm.recipientCity = '';
  addressForm.recipientArea = '';
  addressForm.recipientAddress = '';
  addressForm.remark = '';
  addressForm.applicationId = props.applicationId;

  // 重置表单校验状态
  if (addressFormRef.value) {
    addressFormRef.value.resetFields();
  }

  // 清除区域错误信息
  regionError.value = '';
};

// 监听外部dialogVisible变化
watch(
  () => props.dialogVisible,
  (newVal) => {
    visible.value = newVal;
  }
);

// 监听本地visible变化，同步到父组件
watch(
  () => visible.value,
  (newVal) => {
    emit('update:dialogVisible', newVal);
  }
);

// 监听外部dialogTitle变化
watch(
  () => props.dialogTitle,
  (newVal) => {
    title.value = newVal;
  }
);

// 监听外部addressInfo变化，填充表单
watch(
  () => props.addressInfo,
  (newVal) => {
    if (newVal && Object.keys(newVal).length > 0) {
      // 填充表单数据
      Object.assign(addressForm, {
        id: newVal.id,
        applicationId: props.applicationId,
        portion: newVal.portion,
        recipientName: newVal.recipientName,
        recipientPhone: newVal.recipientPhone,
        recipientProvince: newVal.recipientProvince,
        recipientCity: newVal.recipientCity,
        recipientArea: newVal.recipientArea,
        recipientAddress: newVal.recipientAddress,
        remark: newVal.remark || ''
      });

      // 设置省市区联动
      if (addressForm.recipientProvince) {
        // 确保省份数据已加载
        const selectedProvince = provinceOptions.value.find((province) => province.value === addressForm.recipientProvince);

        if (selectedProvince && selectedProvince.children) {
          cityOptions.value = selectedProvince.children;

          // 如果有城市数据，加载对应的区县数据
          if (addressForm.recipientCity) {
            const selectedCity = selectedProvince.children.find((city) => city.value === addressForm.recipientCity);

            if (selectedCity && selectedCity.children) {
              districtOptions.value = selectedCity.children;
            }
          }
        }
      }
    } else {
      resetAddressForm();
    }
  },
  { immediate: true, deep: true }
);

// 提交地址表单
const submitAddressForm = () => {
  // 先验证所在区域
  if (!validateRegion()) {
    return;
  }

  addressFormRef.value.validate((valid) => {
    if (valid) {
      // 构建提交的地址数据
      const addressData = {
        ...addressForm,
        // 从数据字典中获取省市区名称
        recipientProvinceName: addressForm.recipientProvince
          ? provinceOptions.value.find((p) => p.value === addressForm.recipientProvince)?.label || ''
          : '',
        recipientCityName: addressForm.recipientCity ? cityOptions.value.find((c) => c.value === addressForm.recipientCity)?.label || '' : '',
        recipientAreaName: addressForm.recipientArea ? districtOptions.value.find((d) => d.value === addressForm.recipientArea)?.label || '' : ''
      };

      // 通知父组件提交表单
      emit('success', addressData);
      visible.value = false;
    }
  });
};

// 弹窗关闭处理
const handleClose = () => {
  resetAddressForm();
  emit('cancel');
};
</script>

<style lang="scss" scoped>
/* 地区选择器样式 */
.region-selector {
  display: flex;
  gap: 10px;
  width: 100%;
  margin-bottom: 0;
}

.region-select {
  width: 33%;
  flex: 1;
}

/* 错误信息样式 */
.error-message {
  color: var(--el-color-danger);
  font-size: 12px;
  margin-top: 4px;
  line-height: 1;
}

/* 表单样式优化 */
:deep(.el-dialog__body) {
  padding: 20px 30px;
  height: 400px;
}

:deep(.el-input__inner) {
  height: 36px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-select .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset !important;
}

:deep(.el-select:hover .el-input__wrapper) {
  box-shadow: 0 0 0 1px #c0c4cc inset !important;
}

:deep(.el-select .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset !important;
}

:deep(.el-textarea__inner) {
  min-height: 60px;
  padding: 8px 12px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}
</style>
