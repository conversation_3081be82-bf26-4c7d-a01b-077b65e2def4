<template>
  <el-dialog v-model="dialogVisible" :title="title" width="500px" :close-on-click-modal="false" append-to-body>
    <div class="p-4">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="申请单号" prop="applicationNo">
          <div>{{ applicationData.applicationNo }}</div>
        </el-form-item>
        <el-form-item label="客户名称" prop="customerName">
          <div>{{ customerName }}</div>
        </el-form-item>
        <el-form-item label="申请状态" prop="applicationStatus">
          <dict-tag :options="mall_application_status" :value="applicationData.applicationStatus" />
        </el-form-item>
        <el-form-item label="原备注" prop="originalRemark">
          <div class="text-primary">{{ applicationData.remark || '无' }}</div>
        </el-form-item>
        <el-form-item label="作废原因" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入作废原因" :rows="4" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="danger" @click="handleSubmit" :loading="submitLoading">确认作废</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, computed } from 'vue';
import { revokePhysicalSaleApplication } from '@/api/saleApplication/physicalSaleApplication';
import { PhysicalSaleApplicationVO } from '@/api/saleApplication/physicalSaleApplication/types';

const emit = defineEmits(['success', 'cancel']);

// 标题
const title = '作废销售申请';

// 对话框可见性
const dialogVisible = ref(false);


const submitLoading = ref(false);

// 表单引用
const formRef = ref();

// 字典数据
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_application_status } = toRefs<any>(proxy?.useDict('mall_application_status'));

// 初始数据
const applicationData = ref<Partial<PhysicalSaleApplicationVO> & { customerName?: string }>({});

// 计算客户名称
const customerName = computed(() => {
  return applicationData.value?.customerName || '';
});

// 表单数据
const form = reactive({
  id: '',
  remark: ''
});

// 表单验证规则
const rules = {
  remark: [
    { required: true, message: '请输入作废原因', trigger: 'blur' },
    { min: 2, max: 200, message: '作废原因长度应在2-200个字符之间', trigger: 'blur' }
  ]
};

// 打开对话框
const openDialog = async (row) => {
  if (!row) return;

  resetForm();
  applicationData.value = row;
  form.id = row.id as string;
  dialogVisible.value = true;
};

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
  emit('cancel');
};

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields();
  form.remark = '';
  applicationData.value = {};
};

// 表单提交
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate();

    // 二次确认
    await proxy?.$modal.confirm('确定要作废该销售申请吗？');

    // 提交数据
    submitLoading.value = true;
    await revokePhysicalSaleApplication(form.id, form.remark);

    // 提示成功
    proxy?.$modal.msgSuccess('作废成功');

    // 关闭对话框并通知父组件刷新数据
    dialogVisible.value = false;
    emit('success');
  } catch (error) {
    console.error('作废提交失败', error);
  } finally {
    submitLoading.value = false;
  }
};

// 暴露方法
defineExpose({
  openDialog
});
</script>

<style scoped>
.text-primary {
  color: var(--el-color-primary);
}

.dialog-footer {
  text-align: right;
}
</style>
