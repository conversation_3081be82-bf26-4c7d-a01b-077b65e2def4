<template>
  <el-dialog v-model="dialogVisible" :title="title" width="500px" :close-on-click-modal="false" append-to-body>
    <div class="p-4">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="申请单号" prop="applicationNo">
          <div>{{ applicationData.applicationNo }}</div>
        </el-form-item>
        <el-form-item label="客户" prop="customerName">
          <div>{{ applicationData.customerName }}</div>
        </el-form-item>
        <el-form-item label="申请状态" prop="applicationStatus">
          <dict-tag :options="mall_application_status" :value="applicationData.applicationStatus" />
        </el-form-item>
        <el-form-item label="原备注" prop="originalRemark">
          <div class="text-primary">{{ applicationData.remark || '无' }}</div>
        </el-form-item>
        <el-form-item label="审核结果" prop="auditStatus">
          <el-radio-group v-model="form.auditStatus">
            <el-radio label="21">审核通过(待发货)</el-radio>
            <el-radio label="02">审核不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入审核备注" rows="4" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs } from 'vue';
import { auditPhysicalSaleApplication, getPhysicalSaleApplication } from '@/api/saleApplication/physicalSaleApplication';
import { PhysicalSaleApplicationVO } from '@/api/saleApplication/physicalSaleApplication/types';

const emit = defineEmits(['success', 'cancel']);

// 标题
const title = '审核销售申请';

// 对话框可见性
const dialogVisible = ref(false);

const submitLoading = ref(false);

// 表单引用
const formRef = ref();

// 字典数据
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_application_status } = toRefs<any>(proxy?.useDict('mall_application_status'));

// 初始数据
const applicationData = ref<Partial<PhysicalSaleApplicationVO>>({});

// 表单数据
const form = reactive({
  id: '',
  auditStatus: '21', // 默认审核通过
  remark: ''
});

// 表单验证规则
const rules = {
  auditStatus: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
  remark: [
    { required: true, message: '请输入审核备注', trigger: 'blur' },
    { min: 2, max: 200, message: '审核备注长度应在2-200个字符之间', trigger: 'blur' }
  ]
};

// 打开对话框
const openDialog = async (row) => {
  if (!row) return;

  try {
    // 重置表单
    resetForm();
    // 加载数据
    applicationData.value = row;
    form.id = row.id as string;
    dialogVisible.value = true;
    // 打开对话框
  } catch (error) {
    console.error('加载销售申请数据失败', error);
  }
};

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
  emit('cancel');
};

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields();
  form.auditStatus = '21';
  form.remark = '';
  applicationData.value = {};
};

// 表单提交
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate();

    // 二次确认
    const actionText = form.auditStatus === '21' ? '审核通过' : '审核不通过';
    await proxy?.$modal.confirm(`确定要${actionText}该销售申请吗？`);

    // 提交数据
    submitLoading.value = true;
    await auditPhysicalSaleApplication(form.id, form.auditStatus, form.remark);

    // 提示成功
    proxy?.$modal.msgSuccess(actionText + '成功');

    // 关闭对话框并通知父组件刷新数据
    dialogVisible.value = false;
    emit('success');
  } catch (error) {
    console.error('审核提交失败', error);
  } finally {
    submitLoading.value = false;
  }
};

// 暴露方法
defineExpose({
  openDialog
});
</script>

<style scoped>
.text-primary {
  color: var(--el-color-primary);
}

.dialog-footer {
  text-align: right;
}
</style>
