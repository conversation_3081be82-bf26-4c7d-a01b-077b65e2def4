<template>
  <!-- 添加或修改销售申请（实物）对话框 -->
  <el-dialog :title="title" v-model="visible" width="85%" append-to-body @close="cancel" draggable destroy-on-close>
    <el-row :gutter="10">
      <!-- 左侧锚点导航 -->
      <el-col :span="4">
        <el-anchor>
          <el-anchor-link href="#basic" title="基本信息" />
          <el-anchor-link href="#preAmount" title="预收款信息" v-if="form.purpose === '1'" />
          <el-anchor-link href="#shipping" title="发货要求" />
          <el-anchor-link href="#goodInfo" title="商品信息" />
          <el-anchor-link href="#recipientAddress" title="收货地址" v-if="dialogStatus !== 'add'" />
          <el-anchor-link href="#finishInfo" title="收货记录" v-if="form.applicationStatus === '40'" />
        </el-anchor>
      </el-col>
      <!-- 表单内容 -->
      <el-col :span="20">
        <el-form ref="physicalSaleApplicationFormRef" :model="form" :rules="rules" label-width="120px" label-position="top">
          <!-- 基本信息 -->
          <div id="basic" class="form-section">
            <div class="section-title">
              <span class="title-text">基本信息</span>
            </div>
            <!-- 表单信息 -->
            <el-row :gutter="10">
              <!-- 第一行 -->
              <el-col :span="6">
                <el-form-item label="销售申请单号" prop="applicationNo">
                  <el-input v-model="form.applicationNo" placeholder="(创建后自动生成)" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="下订类型" prop="bookType">
                  <el-select v-model="form.bookType" placeholder="请选择下订类型" style="width: 100%" :disabled="dialogStatus === 'view'">
                    <el-option v-for="dict in mall_book_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="申请用途" prop="purpose">
                  <dict-select
                    dict-key="mall_sale_order_purpose"
                    v-model="form.purpose"
                    placeholder="请选择订单用途"
                    :disabled="dialogStatus === 'view'"
                    :show-footer="false"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="dialogStatus">
                <el-form-item label="申请状态" prop="applicationStatus">
                  <el-select v-model="form.applicationStatus" disabled style="width: 100%">
                    <el-option v-for="dict in mall_application_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <!-- 第二行 -->
              <el-col :span="6">
                <el-form-item label="承做部门" prop="ownerDeptId">
                  <el-tree-select
                    v-model="form.ownerDeptId"
                    :data="deptOptions"
                    :props="{ value: 'id', label: 'label', children: 'children' }"
                    value-key="id"
                    placeholder="请选择承做部门"
                    check-strictly
                    filterable
                    @change="handleDeptChangeInForm"
                    :disabled="dialogStatus === 'view'"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="承做人（A角）" prop="ownerId" :required="!!form.ownerDeptId">
                  <el-select
                    v-model="form.ownerId"
                    placeholder="请输入承做人"
                    clearable
                    @change="handleOwnerChange"
                    filterable
                    :disabled="!form.ownerDeptId || dialogStatus === 'view'"
                    style="width: 100%"
                  >
                    <el-option v-for="item in userOptionsByDeptId" :key="item.userId" :label="item.nickName" :value="item.userId" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item prop="customerId">
                  <template #label>
                    <div class="inline-flex items-center">
                      <span>客户档案</span>
                      <div class="ml-1 inline-flex">
                        <el-tooltip content="打开客户档案" placement="top" v-if="form.customerId">
                          <el-icon class="cursor-pointer text-primary" @click="openCustomerDetailInView(form.customerId)">
                            <Link />
                          </el-icon>
                        </el-tooltip>
                        <el-tooltip content="新增客户档案" placement="top" v-else>
                          <el-icon class="cursor-pointer text-primary" @click="openCustomerAddDialog()">
                            <Plus />
                          </el-icon>
                        </el-tooltip>
                      </div>
                    </div>
                  </template>
                  <customer-select v-model="form.customerId" @change="handleCustomerChange" style="width: 100%" :disabled="dialogStatus === 'view'" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item prop="invoiceTitleId">
                  <template #label>
                    <div class="inline-flex items-center">
                      <span>发票抬头</span>
                      <div class="ml-1 inline-flex" v-if="form.customerId">
                        <el-tooltip content="新增发票抬头" placement="top">
                          <el-icon class="cursor-pointer text-primary" @click="openTaxInvoiceTitleAddDialog()">
                            <Plus />
                          </el-icon>
                        </el-tooltip>
                      </div>
                    </div>
                  </template>
                  <el-select
                    v-model="form.invoiceTitleId"
                    placeholder="请选择开票抬头"
                    filterable
                    :loading="taxInvoiceTitleLoading"
                    style="width: 100%"
                    :disabled="!form.customerId || dialogStatus === 'view'"
                  >
                    <el-option v-for="item in taxInvoiceTitleOptions" :key="item.id" :label="item.companyName" :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>

              <!-- 第三行 -->
              <el-col :span="6">
                <el-form-item label="状态修改时间" prop="statusChangeTime">
                  <el-date-picker
                    clearable
                    v-model="form.statusChangeTime"
                    type="datetime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    placeholder="请选择状态修改时间"
                    disabled
                    style="width: 100%"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="dialogStatus !== 'add'">
                <el-form-item label="总价值" prop="totalValues">
                  <el-input :value="centToYuan(form.totalValues)" placeholder="请输入总值" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="dialogStatus !== 'add'">
                <el-form-item label="总明细金额" prop="totalAmount">
                  <el-input :value="centToYuan(form.totalAmount)" placeholder="请输入总明细金额" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="dialogStatus !== 'add'">
                <el-form-item label="抹零金额" prop="discountAmount">
                  <el-input-number
                    v-model="form.discountAmount"
                    placeholder="请输入取整优惠金额"
                    :disabled="dialogStatus === 'view'"
                    @change="calculateOrderAmounts"
                    :precision="2"
                    :controls="false"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>

              <!-- 第四行 -->
              <el-col :span="6" v-if="dialogStatus !== 'add'">
                <el-form-item label="订单金额" prop="orderAmount">
                  <template #label>
                    <span class="flex items-center">
                      订单金额(元)
                      <el-tooltip content="订单金额=总明细金额-抹零金额，即应收金额" placement="top">
                        <el-icon class="ml-1">
                          <InfoFilled />
                        </el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-input :value="centToYuan(form.orderAmount)" placeholder="请输入订单金额" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="dialogStatus !== 'add'">
                <el-form-item label="折扣率" prop="discountPercentage">
                  <template #label>
                    <span class="flex items-center">
                      折扣率(%)
                      <el-tooltip content="折扣率=订单金额/总明细金额" placement="top">
                        <el-icon class="ml-1">
                          <InfoFilled />
                        </el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <el-input :value="form.discountPercentage" placeholder="请输入折扣率" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="备注" prop="remark">
                  <el-input
                    v-model="form.remark"
                    placeholder="请输入备注"
                    type="textarea"
                    :rows="3"
                    :maxlength="200"
                    show-word-limit
                    :disabled="dialogStatus === 'view'"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 预收款信息 -->
          <div id="preAmount" class="form-section" v-if="form.purpose === '1'">
            <div class="section-title">
              <span class="title-text">预收款信息</span>
            </div>
            <!-- 表单信息 -->
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="是否预收款" prop="isAdvanceReceipt">
                  <el-radio-group v-model="form.isAdvanceReceipt" :disabled="props.dialogStatus === 'view'">
                    <el-radio v-for="dict in sys_yes_no" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="form.isAdvanceReceipt === 'Y'">
                <el-form-item label="预收金额" prop="receiptAmount">
                  <el-input v-model="form.receiptAmount" placeholder="请输入预收金额" :disabled="props.dialogStatus === 'view'" />
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="form.isAdvanceReceipt === 'Y'">
                <el-form-item label="收款日期" prop="receiptDate">
                  <el-date-picker
                    clearable
                    v-model="form.receiptDate"
                    type="datetime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    placeholder="请选择收款日期"
                    :disabled="props.dialogStatus === 'view'"
                    style="width: 100%"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="form.isAdvanceReceipt === 'Y'">
                <el-form-item label="收款备注" prop="receiptNote">
                  <el-input v-model="form.receiptNote" placeholder="请输入收款备注" :disabled="props.dialogStatus === 'view'" />
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="form.isAdvanceReceipt === 'Y'">
                <el-form-item label="收款附件" prop="receiptFiles">
                  <file-upload v-model="form.receiptFiles" :disabled="props.dialogStatus === 'view'" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 发货要求 -->
          <div id="shipping" class="form-section">
            <div class="section-title">
              <span class="title-text">发货要求</span>
            </div>
            <!-- 表单信息 -->
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="配送方式" prop="shippingMethod">
                  <el-select v-model="form.shippingMethod" placeholder="请选择配送方式" style="width: 100%" :disabled="props.dialogStatus === 'view'">
                    <el-option v-for="dict in mall_sale_shipping_method" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="期望发货日期" prop="shippingDate">
                  <el-date-picker
                    clearable
                    v-model="form.shippingDate"
                    type="date"
                    placeholder="请选择期望发货日期"
                    style="width: 100%"
                    :disabled="props.dialogStatus === 'view'"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="18">
                <el-form-item label="发货要求" prop="shippingRequirements">
                  <Editor v-model="form.shippingRequirements" :min-height="192" style="width: 95%" :readOnly="props.dialogStatus === 'view'" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 商品信息 -->
          <div id="goodInfo" class="form-section">
            <div class="section-title">
              <span class="title-text">商品信息</span>
            </div>
            <div class="flex gap-2 mb-4" v-if="props.dialogStatus !== 'view'">
              <el-button type="primary" plain @click="handleAddGoods">
                <el-icon class="el-icon--left"><Plus /></el-icon>添加商品
              </el-button>
            </div>
            <!-- 表单信息 -->
            <el-table :data="form.goodsList || []" style="width: 100%; height: 300px" border>
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="图片" prop="images" width="80" align="center">
                <template #default="scope">
                  <ImagePreview :src="scope.row.images" width="50px" height="50px" isId />
                </template>
              </el-table-column>
              <el-table-column prop="goodsName" label="商品名称" min-width="100" show-overflow-tooltip />
              <el-table-column label="数量" width="160" align="center">
                <template #default="scope">
                  <div class="custom-number-input">
                    <el-input-number
                      v-model="scope.row.qty"
                      :min="1"
                      :precision="0"
                      :disabled="props.dialogStatus === 'view'"
                      controls-position="right"
                      size="default"
                      @change="(val) => handleQtyChange(val, scope.row)"
                    />
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="销售单位" width="80" align="center">
                <template #default>
                  <el-tag size="small" type="info" effect="plain">箱</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="零售价" width="100" align="center">
                <template #default="scope">
                  <span>{{ centToYuan(scope.row.salePrice) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="销售单价" width="160" align="center">
                <template #default="scope">
                  <div class="custom-number-input">
                    <el-input-number
                      :model-value="Number(centToYuan(scope.row.unitPrice))"
                      :min="0"
                      :precision="2"
                      :disabled="props.dialogStatus === 'view'"
                      controls-position="right"
                      size="default"
                      @change="(val) => handleUnitPriceChange(val, scope.row)"
                    />
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="明细总价" width="100" align="center">
                <template #default="scope">
                  <span>{{ centToYuan(scope.row.detailTotal) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80" v-if="props.dialogStatus !== 'view'">
                <template #default="scope">
                  <el-button type="danger" link size="small" style="color: #f56c6c" @click="handleDeleteGoods(scope.row)">移出</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 收货地址 -->
          <div id="recipientAddress" class="form-section" v-if="dialogStatus !== 'add'">
            <div class="section-title">
              <span class="title-text">收货地址</span>
            </div>

            <!-- 搜索栏 -->
            <div class="mb-3 flex justify-between items-center">
              <div class="flex gap-2" v-if="props.dialogStatus !== 'view'">
                <el-button type="primary" plain @click="handleAddAddress">
                  <el-icon class="el-icon--left"><Plus /></el-icon>添加地址
                </el-button>
              </div>
              <div>
                <el-input
                  v-model="addressSearchKeyword"
                  placeholder="请输入手机号搜索"
                  clearable
                  style="width: 220px"
                  @keyup.enter="handleSearchAddress"
                  @change="handleSearchAddress"
                >
                  <template #prefix>
                    <el-icon class="el-input__icon"><Search /></el-icon>
                  </template>
                </el-input>
              </div>
            </div>

            <!-- 收货地址列表 -->
            <el-table :data="addressList || []" style="width: 100%" border>
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column prop="recipientName" label="收货人姓名" width="100" />
              <el-table-column prop="recipientPhone" label="手机号" width="120" />
              <el-table-column prop="recipientProvinceName" label="省份" width="80">
                <template #default="scope">
                  {{ codeToText[scope.row.recipientProvince] }}
                </template>
              </el-table-column>
              <el-table-column prop="recipientCityName" label="城市" width="80">
                <template #default="scope">
                  {{ codeToText[scope.row.recipientCity] }}
                </template>
              </el-table-column>
              <el-table-column prop="recipientAreaName" label="区县" width="80">
                <template #default="scope">
                  {{ codeToText[scope.row.recipientArea] }}
                </template>
              </el-table-column>
              <el-table-column prop="recipientAddress" label="详细地址" min-width="180" show-overflow-tooltip />
              <el-table-column prop="remark" label="收货备注" width="120" show-overflow-tooltip />
              <el-table-column label="操作" width="120" fixed="right" v-if="props.dialogStatus !== 'view'">
                <template #default="scope">
                  <div class="flex items-center gap-2">
                    <el-button type="primary" link size="small" @click="handleEditAddress(scope.row)">
                      <el-icon><Edit /></el-icon>
                    </el-button>
                    <el-button type="danger" link size="small" @click="handleDeleteAddress(scope.row)">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页控件 -->
            <div class="mt-3 flex justify-end">
              <el-pagination
                v-model:current-page="addressPagination.pageNum"
                v-model:page-size="addressPagination.pageSize"
                :page-sizes="[10, 20, 30, 50]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="addressPagination.total"
                @size-change="handleAddressSizeChange"
                @current-change="handleAddressCurrentChange"
              />
            </div>
          </div>

          <!-- 收货记录 -->
          <div id="finishInfo" class="form-section" v-if="form.applicationStatus === '40'">
            <div class="section-title">
              <span class="title-text">收货记录</span>
            </div>
            <!-- 表单信息 -->
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="收货时间" prop="finishTime">
                  <el-date-picker
                    clearable
                    v-model="form.finishTime"
                    type="date"
                    value-format="YYYY-MM-DD 00:00:00"
                    placeholder="请选择收货时间"
                    disabled
                    style="width: 100%"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="收货备注" prop="finishNote">
                  <el-input v-model="form.finishNote" placeholder="请输入收货备注" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="收货附件" prop="certificateFiles">
                  <file-upload v-model="form.certificateFiles" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6"></el-col>
            </el-row>
          </div>
        </el-form>
      </el-col>
    </el-row>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="buttonLoading" v-if="['add', 'edit'].includes(props.dialogStatus)" type="primary" @click="submitForm">保存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 使用商品选择组件 -->
  <GoodsSelector ref="goodsSelectorRef" :multiple="true" :excludeIds="getExistingGoodsIds" title="选择商品" @select="handleGoodsSelected" />

  <!-- 收货地址对话框 -->
  <ApplicationAddressDialog
    v-model:dialogVisible="addressDialog.visible"
    :dialogTitle="addressDialog.title"
    :dialogType="addressDialog.type"
    :applicationId="form.id"
    :addressInfo="selectedAddress"
    @success="handleAddressDialogSuccess"
    @cancel="handleAddressDialogCancel"
  />
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, defineProps, defineEmits, onMounted } from 'vue';
import { Link, Plus, InfoFilled, Edit, Delete, Search } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import { centToYuan, yuanToCent } from '@/utils/moneyUtils';
import { codeToText } from 'element-china-area-data';
// 导入商品选择器组件
import GoodsSelector from './GoodsSelector.vue';
// 导入收货地址对话框组件
import ApplicationAddressDialog from './ApplicationAddressDialog.vue';

// 导入API
import { getPhysicalSaleApplication, addPhysicalSaleApplication, updatePhysicalSaleApplication } from '@/api/saleApplication/physicalSaleApplication';
import { PhysicalSaleApplicationVO, PhysicalSaleApplicationForm } from '@/api/saleApplication/physicalSaleApplication/types';

// 导入收货地址相关API
import {
  listApplicationAddress,
  addApplicationAddress,
  updateApplicationAddress,
  delApplicationAddress
} from '@/api/saleApplication/applicationAddress';
import { ApplicationAddressVO } from '@/api/saleApplication/applicationAddress/types';

// 组件接收的属性
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  dialogTitle: {
    type: String,
    default: '添加销售申请（实物）'
  },
  dialogStatus: {
    type: String,
    default: 'add' // add: 新增，edit: 编辑, view: 查看
  },
  applicationId: {
    type: [String, Number],
    default: ''
  }
});

// 事件发射
const emit = defineEmits(['update:dialogVisible', 'success', 'cancel']);

// 获取全局代理对象
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_sale_shipping_method, mall_book_type, sys_yes_no, mall_sale_order_purpose, mall_application_status } = toRefs<any>(
  proxy?.useDict('mall_sale_shipping_method', 'mall_book_type', 'sys_yes_no', 'mall_sale_order_purpose', 'mall_application_status')
);

// 弹窗控制
const visible = ref(props.dialogVisible);
const title = ref(props.dialogTitle);
const buttonLoading = ref(false);

// 监听外部dialogVisible变化
watch(
  () => props.dialogVisible,
  (newVal) => {
    visible.value = newVal;
    if (newVal && props.applicationId) {
      // 如果打开弹窗且有应用ID，加载数据
      loadApplicationData(props.applicationId);
      // 重置地址分页
      addressPagination.pageNum = 1;
    }
  }
);

// 监听本地visible变化，同步到父组件
watch(
  () => visible.value,
  (newVal) => {
    emit('update:dialogVisible', newVal);
  }
);

// 监听外部dialogTitle变化
watch(
  () => props.dialogTitle,
  (newVal) => {
    title.value = newVal;
  }
);

// 商品选择相关
const goodsSelectorRef = ref(null);

// 表单引用
const physicalSaleApplicationFormRef = ref(null);

// 收货地址相关
const addressList = ref<ApplicationAddressVO[]>([]);
const addressSearchKeyword = ref('');
const selectedAddress = ref({});

// 收货地址分页
const addressPagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
});

// 收货地址对话框
const addressDialog = reactive({
  visible: false,
  title: '添加收货地址',
  type: 'add' // add或edit
});

// 表单数据初始值
const initFormData: PhysicalSaleApplicationForm = {
  id: undefined,
  applicationNo: undefined,
  purpose: undefined,
  ownerId: undefined,
  ownerDeptId: undefined,
  customerId: undefined,
  invoiceTitleId: undefined,
  bookType: undefined,
  shippingMethod: undefined,
  shippingWarehouse: undefined,
  shippingDate: dayjs().add(1, 'day').format('YYYY-MM-DD 23:59:59'),
  shippingRequirements: undefined,
  goodsList: undefined,
  totalValues: undefined,
  totalAmount: undefined,
  discountAmount: undefined,
  orderAmount: undefined,
  discountPercentage: undefined,
  remark: undefined,
  applicationStatus: '01',
  statusChangeTime: undefined,
  isAdvanceReceipt: 'N',
  receiptAmount: undefined,
  receiptDate: undefined,
  receiptNote: undefined,
  receiptFiles: undefined,
  finishTime: undefined,
  certificateFiles: undefined,
  finishNote: undefined
};

// 表单数据
const form = ref({ ...initFormData });

// 监听商品列表和地址列表变化，重新计算金额
watch(
  [() => form.value.goodsList, () => addressList.value],
  () => {
    if (props.dialogStatus !== 'add') {
      calculateOrderAmounts();
    }
  },
  { deep: true }
);

// 计算所有订单金额
const calculateOrderAmounts = () => {
  // 计算总价值：所有商品(零售价*数量)之和 * 地址数量
  const totalValues =
    Array.isArray(form.value.goodsList) && form.value.goodsList.length > 0
      ? form.value.goodsList.reduce((sum, item) => {
          return sum + item.salePrice * item.qty;
        }, 0) * addressList.value.length
      : 0;

  // 计算总明细金额：所有商品(销售单价*数量)之和 * 地址数量
  const totalAmount =
    Array.isArray(form.value.goodsList) && form.value.goodsList.length > 0
      ? form.value.goodsList.reduce((sum, item) => {
          return sum + item.unitPrice * item.qty;
        }, 0) * addressList.value.length
      : 0;

  // 抹零金额（可能由后端计算或用户输入）
  const discountAmount = form.value.discountAmount ? Number(yuanToCent(form.value.discountAmount)) : 0;

  // 订单金额 = 总明细金额 - 抹零金额
  const orderAmount = totalAmount - discountAmount;

  // 折扣率 = 订单金额/总明细金额 * 100，范围0-100
  let discountPercentage = 100;
  if (totalAmount > 0) {
    discountPercentage = ((orderAmount / totalAmount) * 100).toFixed(2) as any;
    // 确保折扣率在0-100范围内
    discountPercentage = Math.max(0, Math.min(100, discountPercentage)) as any;
  }

  // 更新表单数据 - 存储原始数字
  form.value.totalValues = totalValues;
  form.value.totalAmount = totalAmount;
  form.value.orderAmount = orderAmount;
  form.value.discountPercentage = discountPercentage;
};

// 表单验证规则
const rules = {
  purpose: [{ required: true, message: '申请用途不能为空', trigger: 'change' }],
  ownerId: [{ required: true, message: '承做人不能为空', trigger: 'change' }],
  ownerDeptId: [{ required: true, message: '承做部门不能为空', trigger: 'change' }],
  customerId: [{ required: true, message: '客户不能为空', trigger: 'change' }],
  invoiceTitleId: [{ required: true, message: '发票抬头不能为空', trigger: 'change' }],
  bookType: [{ required: true, message: '下订类型不能为空', trigger: 'change' }],
  shippingMethod: [{ required: true, message: '配送方式不能为空', trigger: 'change' }],
  shippingWarehouse: [{ required: true, message: '发货仓库不能为空', trigger: 'change' }],
  shippingDate: [{ required: true, message: '期望发货日期不能为空', trigger: 'blur' }],
  shippingRequirements: [{ required: true, message: '发货要求不能为空', trigger: 'blur' }],
  goodsList: [{ required: true, message: '商品列表不能为空', trigger: 'blur' }],
  isAdvanceReceipt: [{ required: true, message: '是否预收款不能为空', trigger: 'change' }]
};

// 表单中部门选择
import { useDeptSelect } from '@/hooks/useBusiness/deptSelect';
const { deptOptions, loadDeptTree } = useDeptSelect();

// 表单中用户（承做人和协做人等的）选择
import { useUserSelectByDeptId } from '@/views/system/user/detail/userSelectByDeptId';
const { userOptionsByDeptId, loadUserListByDeptId } = useUserSelectByDeptId();

// 发票抬头选择相关
import { useTaxInvoiceTitleSelect } from '@/views/crm/taxInvoiceTitle/detail/taxInvoiceTitleSelect';
import { TaxInvoiceTitleQuery } from '@/api/crm/taxInvoiceTitle/types';
const { taxInvoiceTitleOptions, taxInvoiceTitleLoading, loadTaxInvoiceTitleList } = useTaxInvoiceTitleSelect();

// 获取已存在的商品ID列表（用于选择器排除已选商品）
const getExistingGoodsIds = computed(() => {
  if (!form.value.goodsList) return [];
  return form.value.goodsList.map((item) => item.goodsId);
});

// 加载销售申请数据
const loadApplicationData = async (id: string | number) => {
  try {
    buttonLoading.value = true;
    // 获取销售申请详情
    const res = await getPhysicalSaleApplication(id);
    if (res.code === 200) {
      const formData = res.data as any;

      // 如果客户ID存在，加载该客户的发票抬头列表
      if (formData.customerId) {
        await loadTaxInvoiceTitleList({ customerId: formData.customerId, status: '0' } as TaxInvoiceTitleQuery);
      }

      // 如果部门ID存在，加载该部门的用户列表
      if (formData.ownerDeptId) {
        await loadUserListByDeptId(formData.ownerDeptId);
      }

      // 逐个赋值表单字段，避免额外数据影响
      // 将数据赋值给表单（逐个赋值避免额外数据影响）
      const fieldsToAssign = [
        'id',
        'applicationNo',
        'purpose',
        'ownerId',
        'ownerDeptId',
        'customerId',
        'invoiceTitleId',
        'bookType',
        'shippingMethod',
        'shippingWarehouse',
        'shippingDate',
        'shippingRequirements',
        'goodsList',
        'totalValues',
        'totalAmount',
        'discountAmount',
        'orderAmount',
        'discountPercentage',
        'remark',
        'applicationStatus',
        'statusChangeTime',
        'isAdvanceReceipt',
        'receiptAmount',
        'receiptDate',
        'receiptNote',
        'receiptFiles',
        'finishTime',
        'certificateFiles',
        'finishNote'
      ];
      // 逐个赋值避免额外数据的影响
      fieldsToAssign.forEach((field) => {
        if (field in formData) {
          form.value[field] = formData[field];
        }
      });

      // 将折扣金额转换为元
      form.value.discountAmount = centToYuan(formData.discountAmount * 1) as any;

      // 重新整理商品数据
      const goodsListVo = formData.goodsListVo;
      form.value.goodsList = form.value.goodsList.map((item: any) => {
        const good = goodsListVo.find((goods) => goods.goodsId === item.goodsId);
        const goods = good.goods;
        return {
          ...item,
          goodsName: goods.name,
          images: goods.images
        };
      });

      // 加载关联的收货地址列表
      await loadAddressList(id);
    } else {
      proxy?.$modal.msgError(res.msg || '获取详情失败');
    }
  } catch (error) {
    console.error('获取详情失败:', error);
    proxy?.$modal.msgError('获取详情失败，请稍后重试');
  } finally {
    buttonLoading.value = false;
  }
};

// 加载收货地址列表
const loadAddressList = async (applicationId: string | number, phone?: string) => {
  try {
    const params: any = {
      applicationId,
      pageNum: addressPagination.pageNum,
      pageSize: addressPagination.pageSize
    };

    if (phone) {
      params.recipientPhone = phone;
    }

    const res = await listApplicationAddress(params);
    if (res.rows && res.rows.length > 0) {
      addressList.value = res.rows;
      addressPagination.total = res.total;
      console.log(`成功加载${addressList.value.length}个收货地址，总计${addressPagination.total}条`);
    } else {
      addressList.value = [];
      addressPagination.total = 0;
      console.log('未找到相关收货地址');
    }

    if (phone && addressList.value.length === 0) {
      proxy?.$modal.msgWarning('未找到匹配的收货地址');
    }
  } catch (error) {
    console.error('加载收货地址失败:', error);
    proxy?.$modal.msgError('加载收货地址失败，请稍后重试');
    addressList.value = [];
    addressPagination.total = 0;
  }
};

// 取消按钮
const cancel = () => {
  reset();
  emit('cancel');
  visible.value = false;
};

// 表单重置
const reset = () => {
  form.value = { ...initFormData };
  addressList.value = [];
  // 重置地址分页
  addressPagination.pageNum = 1;
  addressPagination.total = 0;
  physicalSaleApplicationFormRef.value?.resetFields();
  addressSearchKeyword.value = '';
};

// 提交表单
const submitForm = () => {
  physicalSaleApplicationFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      // 检查是否已添加商品
      if (!form.value.goodsList || form.value.goodsList.length === 0) {
        proxy?.$modal.msgError('请至少添加一项商品');
        return;
      }

      // 如果是编辑模式，检查是否已添加收货地址
      if (form.value.id && (!addressList.value || addressList.value.length === 0)) {
        proxy?.$modal.msgError('请至少添加一个收货地址');
        return;
      }

      // 准备提交的数据
      const submitData = { ...form.value, 'shippingWarehouse': '1898928410902204417' } as any;

      // 将折扣金额转换为分
      submitData.discountAmount = yuanToCent(submitData.discountAmount);

      buttonLoading.value = true;
      try {
        let result;
        // 提交销售申请
        if (form.value.id) {
          // 修改操作
          result = await updatePhysicalSaleApplication(submitData);
          proxy?.$modal.msgSuccess('修改成功');
        } else {
          // 新增操作
          result = await addPhysicalSaleApplication(submitData);
          // 新增成功后，设置表单ID，以便后续添加收货地址
          form.value.id = result.data;
          proxy?.$modal.msgSuccess('新增成功，现在您可以添加收货地址了');
        }

        // 通知父组件成功并关闭对话框
        emit('success', form.value.id);
        visible.value = false;
      } catch (error) {
        console.error('提交失败:', error);
        proxy?.$modal.msgError('操作失败，请稍后重试');
      } finally {
        buttonLoading.value = false;
      }
    }
  });
};

// 处理承做人变更，自动更新承做部门
const handleDeptChangeInForm = async (deptId: string) => {
  form.value.ownerId = undefined;
  loadUserListByDeptId(deptId);
};

// 处理承做人变更，自动更新承做部门
const handleOwnerChange = async (userId) => {
  if (!userId) return;

  // 从当前部门用户列表中找到选择的用户
  const selectedUser = userOptionsByDeptId.value.find((item) => item.userId === userId);
  if (!selectedUser) return;

  // 如果选择的用户所在部门与当前选择的部门不一致
  if (selectedUser.deptId && selectedUser.deptId !== form.value.ownerDeptId) {
    try {
      await proxy?.$modal.confirm('指派承做人后，承做部门需要自动更新为该用户所在部门，是否确认？');
      form.value.ownerDeptId = selectedUser.deptId;
      loadUserListByDeptId(selectedUser.deptId);
    } catch (error) {
      // 用户取消则恢复之前的选择
      form.value.ownerId = undefined;
    }
  }
};

// 打开客户详情(查看页面)
const openCustomerDetailInView = (id: string | number) => {
  const routeUrl = `/crm/bizMg/customer/?id=${id}&openView=true`;
  window.open(routeUrl, '_blank');
};

// 打开客户弹窗（新建）
const openCustomerAddDialog = () => {
  const routeUrl = `/crm/bizMg/customer/?openAdd=true`;
  window.open(routeUrl, '_blank');
};

// 处理客户选择变更
const handleCustomerChange = (customerId) => {
  if (customerId) {
    // 客户变更后，加载该客户的开票抬头列表,状态为启用(0)的开票抬头
    loadTaxInvoiceTitleList({ customerId, status: '0' } as TaxInvoiceTitleQuery);
  } else {
    // 清空开票抬头列表+清空已选择的开票抬头
    taxInvoiceTitleOptions.value = [];
    form.value.invoiceTitleId = undefined;
  }
};

// 打开发票抬头弹窗(新建)
const openTaxInvoiceTitleAddDialog = () => {
  const routeUrl = `/crm/bizMg/taxInvoiceTitle/?customerId=${form.value.customerId}&openAdd=true`;
  window.open(routeUrl, '_blank');
};

// 打开商品选择器
const handleAddGoods = () => {
  if (!form.value.goodsList) {
    form.value.goodsList = [];
  }

  goodsSelectorRef.value.open();
};

// 处理商品选择回调
const handleGoodsSelected = (goods) => {
  if (!form.value.goodsList) {
    form.value.goodsList = [];
  }
  // 批量添加所有选择的商品
  const goodsToAdd = goods.map((item) => ({
    goodsId: item.id,
    goodsName: item.name,
    images: item.images,
    salePrice: item.currentSalePrice || item.salePrice, // 零售价
    unitPrice: item.currentSalePrice || item.salePrice, // 销售单价默认等于零售价
    discount: 100, // 默认折扣率
    qty: 1, // 默认数量为1
    saleUnit: item.saleUnit, // 销售单位
    detailTotal: item.currentSalePrice || item.salePrice, // 初始小计等于单价
    remark: item.remark || '' // 备注
  }));

  // 添加到商品明细
  form.value.goodsList = [...form.value.goodsList, ...goodsToAdd];

  // 重新计算订单金额
  calculateOrderAmounts();
};

// 商品删除操作
const handleDeleteGoods = (row) => {
  proxy?.$modal
    .confirm('是否确认删除该商品？')
    .then(() => {
      // 从商品列表中删除该商品
      const index = form.value.goodsList.findIndex((item) => item === row);
      if (index !== -1) {
        form.value.goodsList.splice(index, 1);
        proxy?.$modal.msgSuccess('删除成功');
        // 重新计算订单金额
        calculateOrderAmounts();
      }
    })
    .catch(() => {});
};

// 处理数量变化
const handleQtyChange = (val, row) => {
  // 更新明细总价 = 数量 * 销售单价
  row.detailTotal = row.qty * row.unitPrice;
  // 重新计算订单相关金额
  calculateOrderAmounts();
};

// 处理销售单价变化
const handleUnitPriceChange = (val, row) => {
  // 更新销售单价（将元转换为分）
  row.unitPrice = yuanToCent(val);
  // 更新明细总价 = 数量 * 销售单价
  row.detailTotal = row.qty * row.unitPrice;
  // 重新计算订单相关金额
  calculateOrderAmounts();
};

// 添加收货地址
const handleAddAddress = () => {
  if (!form.value.id) {
    proxy?.$modal.msgError('请先保存销售申请，再添加收货地址');
    return;
  }
  selectedAddress.value = {};
  addressDialog.visible = true;
  addressDialog.title = '添加收货地址';
  addressDialog.type = 'add';
};

// 编辑收货地址
const handleEditAddress = (row) => {
  addressDialog.visible = true;
  addressDialog.title = '编辑收货地址';
  addressDialog.type = 'edit';
  selectedAddress.value = row;
};

// 删除收货地址
const handleDeleteAddress = (row) => {
  proxy?.$modal
    .confirm('是否确认删除该收货地址？')
    .then(async () => {
      try {
        await delApplicationAddress(row.id);
        proxy?.$modal.msgSuccess('删除成功');
        // 重新加载地址列表
        await loadAddressList(form.value.id);
        // 重新计算订单金额
        calculateOrderAmounts();
      } catch (error) {
        console.error('删除地址失败:', error);
        proxy?.$modal.msgError('删除失败，请稍后重试');
      }
    })
    .catch(() => {});
};

// 处理收货地址搜索
const handleSearchAddress = () => {
  if (!form.value.id) {
    proxy?.$modal.msgError('请先保存销售申请');
    return;
  }

  // 重置分页为第一页
  addressPagination.pageNum = 1;
  // 加载地址列表
  loadAddressList(form.value.id, addressSearchKeyword.value);
};

// 处理地址弹窗成功事件
const handleAddressDialogSuccess = async (addressData) => {
  try {
    if (addressDialog.type === 'add') {
      // 添加模式
      await addApplicationAddress({
        ...addressData,
        applicationId: form.value.id
      });
      proxy?.$modal.msgSuccess('添加成功');
    } else {
      // 编辑模式
      await updateApplicationAddress(addressData);
      proxy?.$modal.msgSuccess('修改成功');
    }

    // 重新加载地址列表
    await loadAddressList(form.value.id, addressSearchKeyword.value);

    // 重新计算订单金额
    calculateOrderAmounts();
  } catch (error) {
    console.error('保存地址失败:', error);
    proxy?.$modal.msgError('操作失败，请稍后重试');
  }
};

// 处理地址弹窗取消事件
const handleAddressDialogCancel = () => {
  selectedAddress.value = {};
};

// 处理收货地址分页大小变化
const handleAddressSizeChange = (size: number) => {
  addressPagination.pageSize = size;
  loadAddressList(form.value.id, addressSearchKeyword.value);
};

// 处理收货地址分页当前页变化
const handleAddressCurrentChange = (page: number) => {
  addressPagination.pageNum = page;
  loadAddressList(form.value.id, addressSearchKeyword.value);
};

onMounted(() => {
  loadDeptTree();
  // 初始化计算金额
  calculateOrderAmounts();
});

// 暴露方法给父组件
defineExpose({
  loadApplicationData
});
</script>

<style lang="scss" scoped>
.form-section {
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 16px;
}

.title-text {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  position: relative;
  padding-left: 10px;
}

.title-text::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: var(--el-color-primary);
  border-radius: 2px;
}

.text-primary {
  color: var(--el-color-primary);
}

.font-bold {
  font-weight: bold;
}

.text-success {
  color: var(--el-color-success);
}

.text-danger {
  color: var(--el-color-danger);
}

.cursor-pointer {
  cursor: pointer;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.items-center {
  align-items: center;
}

.justify-end {
  justify-content: flex-end;
}

.gap-1 {
  gap: 4px;
}

.gap-2 {
  gap: 8px;
}

.mt-3 {
  margin-top: 12px;
}

.mb-3 {
  margin-bottom: 12px;
}

.mr-8 {
  margin-right: 32px;
}

.ml-1 {
  margin-left: 4px;
}

.ml-2 {
  margin-left: 8px;
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}

/* 自定义数字输入框样式 */
.custom-number-input {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.custom-number-input :deep(.el-input-number) {
  width: 130px;
}

.custom-number-input :deep(.el-input-number .el-input__wrapper) {
  padding-right: 28px !important;
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

.custom-number-input :deep(.el-input-number .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

.custom-number-input :deep(.el-input-number .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

.custom-number-input :deep(.el-input-number__decrease),
.custom-number-input :deep(.el-input-number__increase) {
  border: none;
  background-color: transparent;
}

/* 表格输入框样式 */
.table-input-number {
  width: 100%;
}

/* 编辑表单中的通用样式 */
:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-dialog__body) {
  padding: 20px 30px;
}

:deep(.el-input__inner) {
  height: 36px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-textarea__inner) {
  min-height: 60px;
  padding: 8px 12px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

/* 解决内容溢出问题 */
:deep(.el-dialog__body) {
  max-height: 75vh;
  overflow-y: auto;
}
</style>
