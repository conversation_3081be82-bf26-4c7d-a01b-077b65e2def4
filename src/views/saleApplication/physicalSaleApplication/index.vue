<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
      :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="销售申请单号" prop="applicationNo">
              <el-input v-model="queryParams.applicationNo" placeholder="请输入销售申请单号" clearable @keyup.enter="handleQuery"
                style="width: 220px" />
            </el-form-item>
            <el-form-item label="申请用途" prop="purpose">
              <el-select v-model="queryParams.purpose" placeholder="请选择申请用途" clearable @change="handleQuery"
                style="width: 220px" popper-class="select-dropdown">
                <el-option v-for="dict in mall_sale_order_purpose" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="下订类型" prop="bookType">
              <el-select v-model="queryParams.bookType" placeholder="请选择下订类型" clearable @change="handleQuery"
                style="width: 220px" popper-class="select-dropdown">
                <el-option v-for="dict in mall_book_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="配送方式" prop="shippingMethod">
              <el-select v-model="queryParams.shippingMethod" placeholder="请选择配送方式" clearable @change="handleQuery"
                style="width: 220px" popper-class="select-dropdown">
                <el-option v-for="dict in mall_sale_shipping_method" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="期望发货日期" prop="shippingDate" v-if="showMoreCondition">
              <el-date-picker value-format="YYYY-MM-DD HH:mm:ss" type="daterange" range-separator="-"
                start-placeholder="开始日期" end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]" clearable
                v-model="dateRangeShippingDate" placeholder="请选择期望发货日期" @change="handleQuery" />
            </el-form-item>
            <el-form-item label="申请状态" prop="applicationStatus" v-if="showMoreCondition">
              <el-select v-model="queryParams.applicationStatus" placeholder="请选择申请状态" clearable @change="handleQuery"
                style="width: 220px">
                <el-option v-for="dict in mall_application_status" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="状态修改时间" prop="statusChangeTime" v-if="showMoreCondition">
              <el-date-picker value-format="YYYY-MM-DD HH:mm:ss" type="daterange" range-separator="-"
                start-placeholder="开始日期" end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]" clearable
                v-model="dateRangeStatusChangeTime" placeholder="请选择状态修改时间" @change="handleQuery" />
            </el-form-item>
            <el-form-item label="是否预收款" prop="isAdvanceReceipt" v-if="showMoreCondition">
              <el-select v-model="queryParams.isAdvanceReceipt" placeholder="请选择是否预收款" clearable @change="handleQuery"
                style="width: 220px">
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="收款日期" prop="receiptDate" v-if="showMoreCondition">
              <el-date-picker value-format="YYYY-MM-DD HH:mm:ss" type="daterange" range-separator="-"
                start-placeholder="开始日期" end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]" clearable
                v-model="dateRangeReceiptDate" placeholder="请选择收款日期" @change="handleQuery" />
            </el-form-item>
            <el-form-item label="收货时间" prop="openCardTime" v-if="showMoreCondition">
              <el-date-picker value-format="YYYY-MM-DD HH:mm:ss" type="daterange" range-separator="-"
                start-placeholder="开始日期" end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]" clearable
                v-model="dateRangeOpenCardTime" placeholder="请选择收货时间" @change="handleQuery" />
            </el-form-item>
            <el-form-item label="更新者" prop="updateBy" v-if="showMoreCondition">
              <el-select v-model="queryParams.updateBy" placeholder="请输入更新者" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px" v-if="showMoreCondition">
              <el-date-picker v-model="dateRangeUpdateTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
                range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
              v-hasPermi="['saleApplication:physicalSaleApplication:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              v-hasPermi="['saleApplication:physicalSaleApplication:remove']">批量删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport"
              v-hasPermi="['saleApplication:physicalSaleApplication:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="physicalSaleApplicationList" @selection-change="handleSelectionChange"
        border>
        <el-table-column type="selection" width="55" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="销售申请单号" prop="applicationNo" width="180" fixed="left" />
        <el-table-column label="申请状态" prop="applicationStatus" width="120">
          <template #default="scope">
            <dict-tag :options="mall_application_status" :value="scope.row.applicationStatus" />
          </template>
        </el-table-column>
        <el-table-column label="状态修改时间" prop="statusChangeTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.statusChangeTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="客户" prop="customerName" width="120" />
        <el-table-column label="发票抬头" prop="invoiceTitleName" width="120" />
        <el-table-column label="承做人" prop="ownerNickname" width="120" />
        <el-table-column label="承做部门" prop="ownerDeptName" width="120" />
        <el-table-column label="申请用途" prop="purpose" width="120">
          <template #default="scope">
            <dict-tag :options="mall_sale_order_purpose" :value="scope.row.purpose" />
          </template>
        </el-table-column>
        <el-table-column label="下订类型" prop="bookType" width="120">
          <template #default="scope">
            <dict-tag :options="mall_book_type" :value="scope.row.bookType" />
          </template>
        </el-table-column>
        <el-table-column label="配送方式" prop="shippingMethod" width="120">
          <template #default="scope">
            <dict-tag :options="mall_sale_shipping_method" :value="scope.row.shippingMethod" />
          </template>
        </el-table-column>
        <el-table-column label="发货仓库" prop="shippingWarehouseName" width="120" />
        <el-table-column label="期望发货日期" prop="shippingDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.shippingDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否预收款" prop="isAdvanceReceipt" width="120">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isAdvanceReceipt" />
          </template>
        </el-table-column>
        <el-table-column label="预收金额" prop="receiptAmount" width="120" />
        <el-table-column label="收款日期" prop="receiptDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.receiptDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="收货时间" prop="openCardTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.openCardTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新者" prop="updateByNickname" width="120" />
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" width="180" fixed="right">
          <template #default="scope">
            <div class="flex items-center gap-2">
              <el-tooltip content="查看" placement="top">
                <el-button link type="primary" icon="View" @click="handleView(scope.row)"></el-button>
              </el-tooltip>
              <el-tooltip content="修改" placement="top" v-if="['01', '02'].includes(scope.row.applicationStatus)">
                <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                  v-hasPermi="['saleApplication:physicalSaleApplication:edit']"></el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top" v-if="['01', '02'].includes(scope.row.applicationStatus)">
                <el-button link type="primary" icon="Close" @click="handleDelete(scope.row)"
                  v-hasPermi="['saleApplication:physicalSaleApplication:remove']"></el-button>
              </el-tooltip>
              <el-tooltip content="提交审核" placement="top" v-if="['01', '02'].includes(scope.row.applicationStatus)">
                <el-button link type="primary" icon="Check" @click="handleSubmit(scope.row)"
                  v-hasPermi="['saleApplication:physicalSaleApplication:submit']"></el-button>
              </el-tooltip>
              <el-tooltip content="作废" placement="top" v-if="['21'].includes(scope.row.applicationStatus)">
                <el-button link type="primary" icon="Delete" @click="handleRevoke(scope.row)"
                  v-hasPermi="['saleApplication:physicalSaleApplication:revoke']"></el-button>
              </el-tooltip>
              <el-tooltip content="审核" placement="top" v-if="['11'].includes(scope.row.applicationStatus)">
                <el-button link type="primary" icon="Check" @click="handleAudit(scope.row)"
                  v-hasPermi="['saleApplication:physicalSaleApplication:audit']"></el-button>
              </el-tooltip>
              <el-tooltip content="发货" placement="top"
                v-if="['21'].includes(scope.row.applicationStatus) && scope.row.shippingMethod !== '01'">
                <el-button link type="primary" icon="Sell" @click="handleShip(scope.row)"
                  v-hasPermi="['saleApplication:physicalSaleApplication:ship']"></el-button>
              </el-tooltip>
              <el-tooltip content="完成" placement="top" v-if="['31'].includes(scope.row.applicationStatus)">
                <el-button link type="primary" icon="Finished" @click="handleFinish(scope.row)"
                  v-hasPermi="['saleApplication:physicalSaleApplication:finish']"></el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 使用抽离的组件 -->
    <PhysicalSaleApplicationDialog v-model:dialogVisible="dialog.visible" :dialogTitle="dialog.title"
      :dialogStatus="dialogStatus" :applicationId="selectedApplicationId" @success="handleDialogSuccess"
      @cancel="handleDialogSuccess" />

    <!-- 审核对话框 -->
    <AuditApplicationDialog ref="auditDialogRef" @success="handleDialogSuccess" @cancel="handleDialogSuccess" />

    <!-- 作废对话框 -->
    <RevokeApplicationDialog ref="revokeDialogRef" @success="handleDialogSuccess" @cancel="handleDialogSuccess" />

    <!-- 发货对话框 -->
    <ShipApplicationDialog ref="shipDialogRef" @success="handleDialogSuccess" @cancel="handleDialogSuccess" />

    <!-- 完成对话框 -->
    <FinishApplicationDialog ref="finishDialogRef" @success="handleDialogSuccess" @cancel="handleDialogSuccess" />
  </div>
</template>

<script setup name="PhysicalSaleApplication" lang="ts">
import {
  listPhysicalSaleApplication,
  delPhysicalSaleApplication,
  submitPhysicalSaleApplication
} from '@/api/saleApplication/physicalSaleApplication';
import { PhysicalSaleApplicationVO, PhysicalSaleApplicationQuery } from '@/api/saleApplication/physicalSaleApplication/types';
import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
import { parseTime } from '@/utils/ruoyi';

// 导入抽离的组件
import PhysicalSaleApplicationDialog from './components/PhysicalSaleApplicationDialog.vue';
import AuditApplicationDialog from './components/AuditApplicationDialog.vue';
import RevokeApplicationDialog from './components/RevokeApplicationDialog.vue';
import ShipApplicationDialog from './components/ShipApplicationDialog.vue';
import FinishApplicationDialog from './components/FinishApplicationDialog.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_sale_shipping_method, mall_book_type, sys_yes_no, mall_sale_order_purpose, mall_application_status } = toRefs<any>(
  proxy?.useDict('mall_sale_shipping_method', 'mall_book_type', 'sys_yes_no', 'mall_sale_order_purpose', 'mall_application_status')
);

const physicalSaleApplicationList = ref<PhysicalSaleApplicationVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogStatus = ref('add'); // add: 新增，edit: 编辑,view: 查看
const selectedApplicationId = ref<string | number>(''); // 当前选中的应用ID
const { loadUserList, userOptions } = useSysUserSelect();

const dateRangeShippingDate = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeStatusChangeTime = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeReceiptDate = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeOpenCardTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();

// 对话框引用
const auditDialogRef = ref();
const revokeDialogRef = ref();
const shipDialogRef = ref();
const finishDialogRef = ref();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const queryParams = ref<PhysicalSaleApplicationQuery>({
  pageNum: 1,
  pageSize: 10,
  applicationNo: undefined,
  purpose: undefined,
  ownerId: undefined,
  ownerDeptId: undefined,
  customerId: undefined,
  invoiceTitleId: undefined,
  bookType: undefined,
  shippingMethod: undefined,
  shippingWarehouse: undefined,
  shippingDate: undefined,
  applicationStatus: undefined,
  statusChangeTime: undefined,
  isAdvanceReceipt: undefined,
  receiptDate: undefined,
  openCardTime: undefined,
  updateBy: undefined,
  isAsc: 'desc',
  orderByColumn: 'updateTime'
});

/** 查询销售申请（实物）列表 */
const getList = async () => {
  loading.value = true;
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  proxy?.addDateRange(queryParams.value, dateRangeShippingDate.value, 'ShippingDate');
  proxy?.addDateRange(queryParams.value, dateRangeStatusChangeTime.value, 'StatusChangeTime');
  proxy?.addDateRange(queryParams.value, dateRangeReceiptDate.value, 'ReceiptDate');
  proxy?.addDateRange(queryParams.value, dateRangeOpenCardTime.value, 'OpenCardTime');
  const res = await listPhysicalSaleApplication(queryParams.value);
  physicalSaleApplicationList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  dateRangeShippingDate.value = ['', ''];
  dateRangeStatusChangeTime.value = ['', ''];
  dateRangeReceiptDate.value = ['', ''];
  dateRangeOpenCardTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: PhysicalSaleApplicationVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  dialogStatus.value = 'add';
  selectedApplicationId.value = '';
  dialog.visible = true;
  dialog.title = '添加销售申请（实物）';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: PhysicalSaleApplicationVO) => {
  dialogStatus.value = 'edit';
  const _id = row?.id || ids.value[0];
  selectedApplicationId.value = _id;
  dialog.visible = true;
  dialog.title = '修改销售申请（实物）';
};

/** 查看按钮操作 */
const handleView = async (row?: PhysicalSaleApplicationVO) => {
  dialogStatus.value = 'view';
  const _id = row?.id || ids.value[0];
  selectedApplicationId.value = _id;
  dialog.visible = true;
  dialog.title = '查看销售申请（实物）';
};

/** 处理弹窗成功事件 */
const handleDialogSuccess = () => {
  getList();
};

/** 删除按钮操作 */
const handleDelete = async (row?: PhysicalSaleApplicationVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除销售申请（实物）编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delPhysicalSaleApplication(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'saleApplication/physicalSaleApplication/export',
    {
      ...queryParams.value
    },
    `physicalSaleApplication_${new Date().getTime()}.xlsx`
  );
};

/** 提交审核按钮操作 */
const handleSubmit = async (row: PhysicalSaleApplicationVO) => {
  const _id = row.id;

  // 检查申请状态是否为待提交或审核不通过
  if (row.applicationStatus !== '01' && row.applicationStatus !== '02') {
    proxy?.$modal.msgError('只有待提交或审核不通过的申请才能提交审核');
    return;
  }

  try {
    // 二次确认
    await proxy?.$modal.confirm('是否确认提交审核销售申请（实物）编号为"' + _id + '"的数据项？');
    loading.value = true;
    await submitPhysicalSaleApplication(_id);
    proxy?.$modal.msgSuccess('提交审核成功');
    await getList();
  } catch (error) {
    console.error('提交审核失败', error);
  } finally {
    loading.value = false;
  }
};

/** 审核按钮操作 */
const handleAudit = async (row: PhysicalSaleApplicationVO) => {
  // 检查申请状态是否为待审核
  if (row.applicationStatus !== '11') {
    proxy?.$modal.msgError('只有待审核的申请才能审核');
    return;
  }

  // 打开审核对话框
  auditDialogRef.value?.openDialog(row);
};

/** 作废按钮操作 */
const handleRevoke = async (row?: PhysicalSaleApplicationVO) => {
  revokeDialogRef.value?.openDialog(row);
};

/** 发货按钮操作 */
const handleShip = async (row: PhysicalSaleApplicationVO) => {
  // 检查申请状态是否为待发货，并且不是一件代发
  if (row.applicationStatus !== '21' || row.shippingMethod === '01') {
    proxy?.$modal.msgError('只有待发货且非一件代发的申请才能操作发货');
    return;
  }

  // 打开发货对话框
  shipDialogRef.value?.openDialog(row);
};

/** 完成按钮操作 */
const handleFinish = async (row: PhysicalSaleApplicationVO) => {
  // 检查申请状态是否为发货中
  if (row.applicationStatus !== '31') {
    proxy?.$modal.msgError('只有发货中的申请才能操作完成');
    return;
  }

  // 打开完成对话框
  finishDialogRef.value?.openDialog(row);
};

onMounted(() => {
  getList();
  loadUserList();
});
</script>

<style lang="scss" scoped>
.search-form-container {
  display: flex;
  flex-wrap: wrap;
}

.text-primary {
  color: var(--el-color-primary);
}

.font-bold {
  font-weight: bold;
}

.text-success {
  color: var(--el-color-success);
}

.text-danger {
  color: var(--el-color-danger);
}

.cursor-pointer {
  cursor: pointer;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.items-center {
  align-items: center;
}

.justify-end {
  justify-content: flex-end;
}

.gap-1 {
  gap: 4px;
}

.gap-2 {
  gap: 8px;
}

.mt-3 {
  margin-top: 12px;
}

.mr-8 {
  margin-right: 32px;
}

.ml-1 {
  margin-left: 4px;
}

.ml-2 {
  margin-left: 8px;
}
</style>
