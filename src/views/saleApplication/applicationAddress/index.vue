<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="水果销售申请单id" prop="applicationId">
              <el-input v-model="queryParams.applicationId" placeholder="请输入水果销售申请单id" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="份数" prop="portion">
              <el-input v-model="queryParams.portion" placeholder="请输入份数" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="收件人姓名" prop="recipientName">
              <el-input v-model="queryParams.recipientName" placeholder="请输入收件人姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="收件人电话" prop="recipientPhone">
              <el-input v-model="queryParams.recipientPhone" placeholder="请输入收件人电话" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="收件人省份" prop="recipientProvince">
              <el-input v-model="queryParams.recipientProvince" placeholder="请输入收件人省份" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="收件人城市" prop="recipientCity">
              <el-input v-model="queryParams.recipientCity" placeholder="请输入收件人城市" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="收件人区县" prop="recipientArea">
              <el-input v-model="queryParams.recipientArea" placeholder="请输入收件人区县" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="收件人地址" prop="recipientAddress">
              <el-input v-model="queryParams.recipientAddress" placeholder="请输入收件人地址" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="收货备注" prop="recipientNote">
              <el-input v-model="queryParams.recipientNote" placeholder="请输入收货备注" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="商品订单id" prop="goodsOrderId">
              <el-input v-model="queryParams.goodsOrderId" placeholder="请输入商品订单id" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="更新者" prop="updateBy">
              <el-input v-model="queryParams.updateBy" placeholder="请输入更新者" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                <el-button link @click="showMoreCondition = !showMoreCondition">
                    {{ showMoreCondition ? '收起' : '展开' }}
                    <el-icon class="el-icon--right">
                        <arrow-up v-if="showMoreCondition" />
                        <arrow-down v-else />
                    </el-icon>
                </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['saleApplication:applicationAddress:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['saleApplication:applicationAddress:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['saleApplication:applicationAddress:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['saleApplication:applicationAddress:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="applicationAddressList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="id" align="center" prop="id" v-if="true" />
        <el-table-column label="水果销售申请单id" align="center" prop="applicationId" />
        <el-table-column label="份数" align="center" prop="portion" />
        <el-table-column label="收件人姓名" align="center" prop="recipientName" />
        <el-table-column label="收件人电话" align="center" prop="recipientPhone" />
        <el-table-column label="收件人省份" align="center" prop="recipientProvince" />
        <el-table-column label="收件人城市" align="center" prop="recipientCity" />
        <el-table-column label="收件人区县" align="center" prop="recipientArea" />
        <el-table-column label="收件人地址" align="center" prop="recipientAddress" />
        <el-table-column label="收货备注" align="center" prop="recipientNote" />
        <el-table-column label="商品订单id" align="center" prop="goodsOrderId" />
        <el-table-column label="B端备注" align="center" prop="remark" />
        <el-table-column label="更新者" align="center" prop="updateBy" />
        <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['saleApplication:applicationAddress:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['saleApplication:applicationAddress:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改销售申请发货地址对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body @close="cancel" draggable>
      <el-form ref="applicationAddressFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="水果销售申请单id" prop="applicationId">
          <el-input v-model="form.applicationId" placeholder="请输入水果销售申请单id" />
        </el-form-item>
        <el-form-item label="份数" prop="portion">
          <el-input v-model="form.portion" placeholder="请输入份数" />
        </el-form-item>
        <el-form-item label="收件人姓名" prop="recipientName">
          <el-input v-model="form.recipientName" placeholder="请输入收件人姓名" />
        </el-form-item>
        <el-form-item label="收件人电话" prop="recipientPhone">
          <el-input v-model="form.recipientPhone" placeholder="请输入收件人电话" />
        </el-form-item>
        <el-form-item label="收件人省份" prop="recipientProvince">
          <el-input v-model="form.recipientProvince" placeholder="请输入收件人省份" />
        </el-form-item>
        <el-form-item label="收件人城市" prop="recipientCity">
          <el-input v-model="form.recipientCity" placeholder="请输入收件人城市" />
        </el-form-item>
        <el-form-item label="收件人区县" prop="recipientArea">
          <el-input v-model="form.recipientArea" placeholder="请输入收件人区县" />
        </el-form-item>
        <el-form-item label="收件人地址" prop="recipientAddress">
          <el-input v-model="form.recipientAddress" placeholder="请输入收件人地址" />
        </el-form-item>
        <el-form-item label="收货备注" prop="recipientNote">
          <el-input v-model="form.recipientNote" placeholder="请输入收货备注" />
        </el-form-item>
        <el-form-item label="商品订单id" prop="goodsOrderId">
          <el-input v-model="form.goodsOrderId" placeholder="请输入商品订单id" />
        </el-form-item>
        <el-form-item label="B端备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入B端备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ApplicationAddress" lang="ts">
import { listApplicationAddress, getApplicationAddress, delApplicationAddress, addApplicationAddress, updateApplicationAddress } from '@/api/saleApplication/applicationAddress';
import { ApplicationAddressVO, ApplicationAddressQuery, ApplicationAddressForm } from '@/api/saleApplication/applicationAddress/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const applicationAddressList = ref<ApplicationAddressVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑

const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const applicationAddressFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ApplicationAddressForm = {
  id: undefined,
  applicationId: undefined,
  portion: undefined,
  recipientName: undefined,
  recipientPhone: undefined,
  recipientProvince: undefined,
  recipientCity: undefined,
  recipientArea: undefined,
  recipientAddress: undefined,
  recipientNote: undefined,
  goodsOrderId: undefined,
  remark: undefined,
}
const data = reactive<PageData<ApplicationAddressForm, ApplicationAddressQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    applicationId: undefined,
    portion: undefined,
    recipientName: undefined,
    recipientPhone: undefined,
    recipientProvince: undefined,
    recipientCity: undefined,
    recipientArea: undefined,
    recipientAddress: undefined,
    recipientNote: undefined,
    goodsOrderId: undefined,
    updateBy: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [
      { required: true, message: "id不能为空", trigger: "blur" }
    ],
    applicationId: [
      { required: true, message: "水果销售申请单id不能为空", trigger: "blur" }
    ],
    portion: [
      { required: true, message: "份数不能为空", trigger: "blur" }
    ],
    recipientName: [
      { required: true, message: "收件人姓名不能为空", trigger: "blur" }
    ],
    recipientPhone: [
      { required: true, message: "收件人电话不能为空", trigger: "blur" }
    ],
    recipientProvince: [
      { required: true, message: "收件人省份不能为空", trigger: "blur" }
    ],
    recipientCity: [
      { required: true, message: "收件人城市不能为空", trigger: "blur" }
    ],
    recipientArea: [
      { required: true, message: "收件人区县不能为空", trigger: "blur" }
    ],
    recipientAddress: [
      { required: true, message: "收件人地址不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询销售申请发货地址列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listApplicationAddress(queryParams.value);
  applicationAddressList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  applicationAddressFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ApplicationAddressVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加销售申请发货地址";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ApplicationAddressVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getApplicationAddress(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改销售申请发货地址";
}

/** 提交按钮 */
const submitForm = () => {
  applicationAddressFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateApplicationAddress(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addApplicationAddress(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: ApplicationAddressVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除销售申请发货地址编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delApplicationAddress(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('saleApplication/applicationAddress/export', {
    ...queryParams.value
  }, `applicationAddress_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
