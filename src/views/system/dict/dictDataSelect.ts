// 字典数据列表，支持下拉选项
import { listData } from '@/api/system/dict/data';
import { DictDataVO } from '@/api/system/dict/data/types';

export const useDictDataSelect = () => {
  const dictDataList = ref<DictDataVO[]>([]);
  const dictDataLoading = ref(false);

  const getDictDataList = async (dictType: string) => {
    dictDataLoading.value = true;
    try {
      const res = await listData({
        dictType,
        pageNum: 1,
        pageSize: 30,
        dictName: undefined,
        dictLabel: undefined
      });
      dictDataList.value = res.rows;
    } finally {
      dictDataLoading.value = false;
    }
  };

  return {
    dictDataList,
    dictDataLoading,
    getDictDataList
  };
};
