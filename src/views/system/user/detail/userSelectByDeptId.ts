// 根据部门id查询系统用户（支持设置：承办人、协办人等）

import { ref } from 'vue';
import { listUser } from '@/api/system/user';
import type { UserVO } from '@/api/system/user/types';

export const useUserSelectByDeptId = () => {
  const userOptionsByDeptId = ref<UserVO[]>([]);
  const userLoadingByDeptId = ref(false);
  const loadUserListByDeptId = async (deptId?: string | number) => {
    try {
      userLoadingByDeptId.value = true;
      const res = await listUser({ deptId: deptId, pageNum: 1, pageSize: 20 });
      userOptionsByDeptId.value = res.rows;
    } finally {
      userLoadingByDeptId.value = false;
    }
  };

  return {
    userOptionsByDeptId,
    userLoadingByDeptId,
    loadUserListByDeptId
  };
};
