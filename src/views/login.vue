<template>
  <div class="login">
    <!-- 登录表单 -->
    <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
      <div class="title-box">
        <h3 class="title">百果园 CSO</h3>
        <!-- <lang-select /> -->
      </div>
      <!-- <el-form-item v-if="tenantEnabled" prop="tenantId">
        <el-select v-model="loginForm.tenantId" filterable :placeholder="proxy.$t('login.selectPlaceholder')" style="width: 100%">
          <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.companyName" :value="item.tenantId"></el-option>
          <template #prefix><svg-icon icon-class="company" class="el-input__icon input-icon" /></template>
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item prop="username">
        <el-select v-model="loginForm.username" :placeholder="proxy.$t('login.username')">
          <template #prefix><svg-icon icon-class="company" class="el-input__icon input-icon" /></template>
        </el-select>
      </el-form-item> -->
      <el-form-item prop="loginPhone">
        <el-input
          v-model="loginForm.loginPhone"
          type="text"
          auto-complete="off"
          placeholder="输入登录手机号后回车..."
          @keyup.enter="handleOrgList"
          style="width: 70%"
          :disabled="!beforeLoginStatus"
        >
          <template #prefix><svg-icon icon-class="phone" class="el-input__icon input-icon" /></template>
        </el-input>
        <el-button
          v-if="beforeLoginStatus"
          :loading="loading"
          size="large"
          type="primary"
          style="width: 25%; margin-left: 5%"
          @click.prevent="handleOrgList"
          @keyup.enter="handleOrgList"
        >
          查询组织
        </el-button>
        <el-button v-else size="large" @click="handleChangeLoginPhone" style="width: 25%; margin-left: 5%">更换手机</el-button>
      </el-form-item>
      <!-- <el-form-item prop="loginPhone">
        <el-input v-model="loginForm.loginPhone" type="text" placeholder="输入登录手机号后回车..." disabled style="flex: 1">
          <template #prefix><svg-icon icon-class="phone" class="el-input__icon input-icon" /></template>
        </el-input>
        <el-tooltip content="重新获取登录组织？" placement="top">
          <el-button type="text" @click="beforeLoginStatus = true">
            <el-icon style="flex-shrink: 0"><RefreshLeft /></el-icon>
          </el-button>
        </el-tooltip>
      </el-form-item> -->
      <el-form-item prop="orgId">
        <el-select v-model="loginForm.orgId" placeholder="请选择组织" :disabled="beforeLoginStatus">
          <template #prefix><svg-icon icon-class="company" class="el-input__icon input-icon" /></template>
          <el-option v-for="item in orgList" :key="item.id" :label="item.orgName" :value="item.id">
            <div class="org-option">
              <el-tooltip content="组织名称" placement="top">
                <span class="org-name">{{ item.orgName }}</span>
              </el-tooltip>
              <el-tooltip content="登录手机对应的用户在该组织的昵称" placement="top">
                <span class="org-phone">{{ item.nickName }}</span>
              </el-tooltip>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          size="large"
          auto-complete="off"
          :placeholder="proxy.$t('login.password')"
          @keyup.enter="handleLogin"
          :disabled="beforeLoginStatus"
        >
          <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
        </el-input>
      </el-form-item>
      <el-form-item v-if="captchaEnabled" prop="code">
        <el-input
          v-model="loginForm.code"
          size="large"
          auto-complete="off"
          :placeholder="proxy.$t('login.code')"
          style="width: 63%"
          @keyup.enter="handleLogin"
          :disabled="beforeLoginStatus"
        >
          <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" class="login-code-img" @click="getCode" />
        </div>
      </el-form-item>
      <el-checkbox v-model="loginForm.rememberMe" style="margin: 0 0 25px 0">{{ proxy.$t('login.rememberPassword') }}</el-checkbox>
      <el-form-item style="float: right">
        <el-button circle :title="proxy.$t('login.social.wechat')" @click="doSocialLogin('wechat')">
          <svg-icon icon-class="wechat" />
        </el-button>
      </el-form-item>
      <el-form-item style="width: 100%" v-if="!beforeLoginStatus">
        <el-button :loading="loading" size="large" type="primary" style="width: 100%" @click.prevent="handleLogin">
          <span v-if="!loading">{{ proxy.$t('login.login') }}</span>
          <span v-else>{{ proxy.$t('login.logging') }}</span>
        </el-button>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © from 2022 YL 丨 小马过河 & 趣味丛生</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getCodeImg, getTenantList, login, getOrgList } from '@/api/login';
import { authBinding } from '@/api/system/social/auth';
import { useUserStore } from '@/store/modules/user';
import { LoginData, TenantVO, OrgVO } from '@/api/types';
import { to } from 'await-to-js';
import { HttpStatus } from '@/enums/RespEnum';
import { useI18n } from 'vue-i18n';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const userStore = useUserStore();
const router = useRouter();
const { t } = useI18n();

const loginRef = ref<ElFormInstance>();
const codeUrl = ref('');
const loading = ref(false);
const orgLoading = ref(false);
const beforeLoginStatus = ref(true); // true:尚未获得orgId；
const captchaEnabled = ref(true);
const tenantEnabled = ref(true);
const register = ref(false);
const redirect = ref('/');
const tenantList = ref<TenantVO[]>([]);
const orgList = ref<OrgVO[]>([]);

const loginForm = ref<LoginData>({
  tenantId: '000000',
  loginPhone: '',
  orgId: undefined,
  baseUserId: undefined,
  username: 'admin',
  password: 'admin123',
  rememberMe: false,
  code: '',
  uuid: '',
  clientId: 'web',
  grantType: 'password'
});

const loginRules: ElFormRules = {
  tenantId: [{ required: true, trigger: 'blur', message: t('login.rule.tenantId.required') }],
  loginPhone: [
    { required: true, trigger: 'blur', message: '登录手机号必填' },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur'
    }
  ],
  username: [{ required: true, trigger: 'blur', message: t('login.rule.username.required') }],
  password: [{ required: true, trigger: 'blur', message: t('login.rule.password.required') }],
  code: [{ required: true, trigger: 'change', message: t('login.rule.code.required') }]
};

watch(
  () => router.currentRoute.value,
  (newRoute: any) => {
    redirect.value = newRoute.query && newRoute.query.redirect && decodeURIComponent(newRoute.query.redirect);
  },
  { immediate: true }
);

onMounted(() => {
  getCode();
  initTenantList();
  getLoginData();
  handleOrgList();
});

const handleLogin = () => {
  loginRef.value?.validate(async (valid: boolean, fields: any) => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 localStorage 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        localStorage.setItem('tenantId', String(loginForm.value.tenantId));
        localStorage.setItem('username', String(loginForm.value.username));
        localStorage.setItem('password', String(loginForm.value.password));
        localStorage.setItem('rememberMe', String(loginForm.value.rememberMe));
        localStorage.setItem('loginPhone', String(loginForm.value.loginPhone));
        localStorage.setItem('baseUserId', String(loginForm.value.baseUserId));
        localStorage.setItem('orgId', String(loginForm.value.orgId));
      } else {
        // 否则移除
        localStorage.removeItem('tenantId');
        localStorage.removeItem('username');
        localStorage.removeItem('password');
        localStorage.removeItem('rememberMe');
        localStorage.removeItem('loginPhone');
        localStorage.removeItem('baseUserId');
        localStorage.removeItem('orgId');
      }
      console.log(loginForm.value)
      // 调用action的登录方法
      const [err] = await to(userStore.login(loginForm.value));
      if (!err) {
        const redirectUrl = redirect.value || '/';
        await router.push(redirectUrl);
        loading.value = false;
      } else {
        loading.value = false;
        // 重新获取验证码
        if (captchaEnabled.value) {
          await getCode();
        }
      }
    } else {
      console.log('提交信息有误!', fields);
    }
  });
};

/**
 * 获取验证码
 */
const getCode = async () => {
  const res = await getCodeImg();
  const { data } = res;
  captchaEnabled.value = data.captchaEnabled === undefined ? true : data.captchaEnabled;
  if (captchaEnabled.value) {
    codeUrl.value = 'data:image/gif;base64,' + data.img;
    loginForm.value.uuid = data.uuid;
  }
};

const getLoginData = () => {
  const tenantId = localStorage.getItem('tenantId');
  const username = localStorage.getItem('username');
  const password = localStorage.getItem('password');
  const rememberMe = localStorage.getItem('rememberMe');
  const loginPhone = localStorage.getItem('loginPhone');
  const baseUserId = localStorage.getItem('baseUserId');
  const orgId = localStorage.getItem('orgId');

  // dev环境
  const devPhone = import.meta.env.VITE_APP_ENV === 'development' ? '18929391040' : loginPhone;

  loginForm.value = {
    tenantId: tenantId === null ? String(loginForm.value.tenantId) : tenantId,
    username: username === null ? String(loginForm.value.username) : username,
    password: password === null ? String(loginForm.value.password) : String(password),
    rememberMe: rememberMe === null ? false : Boolean(rememberMe),
    loginPhone: loginPhone === null ? devPhone : loginPhone,
    baseUserId: baseUserId === null ? undefined : Number(baseUserId),
    orgId: orgId === null ? undefined : orgId
  } as LoginData;
};

/**
 * 获取租户列表
 */
const initTenantList = async () => {
  const { data } = await getTenantList(false);
  tenantEnabled.value = data.tenantEnabled === undefined ? true : data.tenantEnabled;
  if (tenantEnabled.value) {
    tenantList.value = data.voList;
    if (tenantList.value != null && tenantList.value.length !== 0) {
      loginForm.value.tenantId = tenantList.value[0].tenantId;
    }
  }
};

/**
 * 第三方登录
 * @param type
 */
const doSocialLogin = (type: string) => {
  authBinding(type, loginForm.value.tenantId).then((res: any) => {
    if (res.code === HttpStatus.SUCCESS) {
      // 获取授权地址跳转
      window.location.href = res.data;
    } else {
      ElMessage.error(res.msg);
    }
  });
};

/**
 * 获取组织列表
 */
const handleOrgList = async () => {
  const { data } = await getOrgList(loginForm.value.loginPhone);
  loginForm.value.baseUserId = data.baseUserId;
  orgList.value = data.orgList;
  loginForm.value.orgId = orgList.value[0].id;
  beforeLoginStatus.value = false;
  console.log('orgList', orgList.value);
};

// 更换手机号
const handleChangeLoginPhone = () => {
  beforeLoginStatus.value = !beforeLoginStatus.value;
  getCode();
};
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url('../assets/images/login-background.jpg');
  background-size: cover;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
  }
}

.title-box {
  display: flex;

  .title {
    margin: 0px auto 30px auto;
    text-align: center;
    color: #707070;
  }

  :deep(.lang-select--style) {
    line-height: 0;
    color: #7483a3;
  }
}

.org-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;
  position: relative;
  z-index: 1;

  .el-input {
    height: 40px;

    input {
      height: 40px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 40px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial, serif;
  font-size: 12px;
  letter-spacing: 1px;
  z-index: 1;
}

.login-code-img {
  height: 40px;
  padding-left: 12px;
}
</style>
