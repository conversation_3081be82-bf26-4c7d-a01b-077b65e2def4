<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container" label-width="100px">
            <!-- <el-form-item label="父级编号" prop="parentId">
              <el-input v-model="queryParams.parentId" placeholder="请输入父级编号" clearable @keyup.enter="handleQuery" />
            </el-form-item> -->
            <el-form-item label="后台分类名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入后台分类名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" clearable @change="handleQuery">
                <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新人" prop="updateBy">
              <el-select v-model="queryParams.updateBy" placeholder="请输入更新人" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd()" v-hasPermi="['mall:backCategory:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="Sort" @click="handleToggleExpandAll">展开/折叠</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>
      <el-table
        ref="backCategoryTableRef"
        v-loading="loading"
        :data="backCategoryList"
        row-key="id"
        :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        border
      >
        <el-table-column label="后台分类名称" prop="name" width="200" />
        <!-- <el-table-column label="父级编号" prop="parentId" width="150" /> -->
        <el-table-column label="层级" prop="level" width="60">
          <template #default="scope">
            <span>{{ getCategoryLevel(scope.row.ancestors) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="分类图片" prop="imageUrl" width="100">
          <template #default="scope">
            <image-preview :src="scope.row.imageUrl" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="100">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="排序" prop="sort" width="100" />
        <el-table-column label="备注" prop="remark" show-overflow-tooltip min-width="200" />
        <el-table-column label="更新人" prop="updateNickName" width="150" />
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="150">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mall:backCategory:edit']" />
            </el-tooltip>
            <el-tooltip content="新增" placement="top" v-if="getCategoryLevel(scope.row.ancestors) < 3">
              <el-button link type="primary" icon="Plus" @click="handleAdd(scope.row)" v-hasPermi="['mall:backCategory:add']" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mall:backCategory:remove']" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 添加或修改后台分类对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="50%" append-to-body>
      <el-form ref="backCategoryFormRef" :model="form" :rules="rules" label-width="80px" label-position="top">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="父级编号" prop="parentId">
              <el-tree-select
                v-model="form.parentId"
                :data="backCategoryOptions"
                :props="{ value: 'id', label: 'name', children: 'children' }"
                value-key="id"
                placeholder="请选择父级编号"
                check-strictly
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="后台分类名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入后台分类名称" maxlength="20" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model="form.sort" placeholder="请输入排序" :min="1" :max="999" />
              <el-tooltip content="当前分类的子类数量" placement="top">
                <el-button link type="primary" icon="MagicStick" @click="getChildrenCount(form.parentId)" />
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="分类图片" prop="image">
          <image-upload v-model="form.image" :limit="1" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" :rows="5" maxlength="200" show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-checkbox v-if="!dialogEditStatus" v-model="continueCreate" style="float: left; margin-top: 8px">继续新建</el-checkbox>
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="BackCategory" lang="ts">
import {
  listBackCategory,
  getBackCategory,
  delBackCategory,
  addBackCategory,
  updateBackCategory,
  countBackCategoryByParentId
} from '@/api/mall/backCategory';
import { BackCategoryVO, BackCategoryQuery, BackCategoryForm } from '@/api/mall/backCategory/types';

type BackCategoryOption = {
  id: number;
  name: string;
  children?: BackCategoryOption[];
};

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { sys_normal_disable } = toRefs<any>(proxy?.useDict('sys_normal_disable'));

const backCategoryList = ref<BackCategoryVO[]>([]);
const backCategoryOptions = ref<BackCategoryOption[]>([]);
const buttonLoading = ref(false);
const showSearch = ref(true);
const isExpandAll = ref(true);
const loading = ref(false);

const queryFormRef = ref<ElFormInstance>();
const backCategoryFormRef = ref<ElFormInstance>();
const backCategoryTableRef = ref<ElTableInstance>();

const dialogEditStatus = ref(false); // 是否编辑状态, true为编辑, false为新增
const continueCreate = ref(true); // 是否继续创建，对应复选框的值

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const initFormData: BackCategoryForm = {
  id: undefined,
  parentId: undefined,
  name: undefined,
  image: undefined,
  status: '0',
  sort: 999,
  remark: undefined
};

const data = reactive<PageData<BackCategoryForm, BackCategoryQuery>>({
  form: { ...initFormData },
  queryParams: {
    parentId: undefined,
    name: undefined,
    status: undefined,
    updateBy: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    parentId: [{ required: true, message: '父级编号不能为空', trigger: 'blur' }],
    name: [{ required: true, message: '后台分类名称不能为空', trigger: 'blur' }],
    image: [{ required: true, message: '分类图片不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '状态不能为空', trigger: 'change' }],
    sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
const { loadUserList, userOptions } = useSysUserSelect();

/** 查询后台分类列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listBackCategory(queryParams.value);
  const data = proxy?.handleTree<BackCategoryVO>(res.data, 'id', 'parentId');
  if (data) {
    backCategoryList.value = data;
    loading.value = false;
  }
};

/** 查询后台分类下拉树结构 */
const getTreeselect = async () => {
  const res = await listBackCategory();
  backCategoryOptions.value = [];
  const data: BackCategoryOption = { id: 0, name: '顶级节点', children: [] };
  data.children = proxy?.handleTree<BackCategoryOption>(res.data, 'id', 'parentId');
  backCategoryOptions.value.push(data);
};

// 取消按钮
const cancel = () => {
  reset();
  dialog.visible = false;
};

// 表单重置
const reset = () => {
  form.value = { ...initFormData };
  backCategoryFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 新增按钮操作 */
const handleAdd = (row?: BackCategoryVO) => {
  dialogEditStatus.value = false;
  reset();
  getTreeselect();
  if (row != null && row.id) {
    form.value.parentId = row.id;
  } else {
    form.value.parentId = 0;
  }
  dialog.visible = true;
  dialog.title = '添加后台分类';
  getChildrenCount(form.value.parentId);
};

/** 展开/折叠操作 */
const handleToggleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value;
  toggleExpandAll(backCategoryList.value, isExpandAll.value);
};

/** 展开/折叠操作 */
const toggleExpandAll = (data: BackCategoryVO[], status: boolean) => {
  data.forEach((item) => {
    backCategoryTableRef.value?.toggleRowExpansion(item, status);
    if (item.children && item.children.length > 0) toggleExpandAll(item.children, status);
  });
};

/** 修改按钮操作 */
const handleUpdate = async (row: BackCategoryVO) => {
  dialogEditStatus.value = true;
  reset();
  await getTreeselect();
  if (row != null) {
    form.value.parentId = row.parentId;
  }
  const res = await getBackCategory(row.id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改后台分类';
};

/** 提交按钮 */
const submitForm = () => {
  backCategoryFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateBackCategory(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addBackCategory(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      if (continueCreate.value && !dialogEditStatus.value) {
        const currentForm = { ...form.value };
        reset();
        dialogEditStatus.value = false;
        dialog.title = '添加后台分类';
        getList();
        form.value.parentId = currentForm.parentId;
        form.value.status = currentForm.status;
        getChildrenCount(form.value.parentId);
      } else {
        dialog.visible = false;
        dialogEditStatus.value = false;
      }
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row: BackCategoryVO) => {
  await proxy?.$modal.confirm('是否确认删除后台分类编号为"' + row.id + '"的数据项？');
  loading.value = true;
  await delBackCategory(row.id).finally(() => (loading.value = false));
  await getList();
  proxy?.$modal.msgSuccess('删除成功');
};

/** 获取后台分类的子类数量 */
const getChildrenCount = async (parentId: string | number) => {
  const res = await countBackCategoryByParentId(parentId);
  if (res) {
    initFormData.sort = res.data + (dialogEditStatus.value ? 0 : 1);
  }
  form.value.sort = initFormData.sort;
};

/**
 * 获取分类的层级
 */
const getCategoryLevel = (ancestors: string | null) => {
  if (!ancestors) return 1; // 如果 ancestors 为空，返回默认层级 1
  return ancestors.split(',').length;
};

const route = useRoute();
const router = useRouter();
// 监听要打开哪个弹窗
const handleOpenDialog = () => {
  // 1. 打开新增弹窗
  const openAdd = route.query.openAdd;
  if (openAdd === 'true') {
    nextTick(() => {
      handleAdd();
      router.replace({ query: {} });
    });
  }
  // 2. 打开修改弹窗
  const id = route.query.id;
  const openEdit = route.query.openEdit;
  if (id && openEdit === 'true') {
    nextTick(() => {
      handleUpdate({ id: id } as BackCategoryVO);
      router.replace({ query: {} });
    });
  }
};

onMounted(() => {
  getList();
  loadUserList();
  handleOpenDialog();
});
</script>
