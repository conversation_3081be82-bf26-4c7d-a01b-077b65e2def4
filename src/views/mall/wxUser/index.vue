<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="OpenID" prop="openid">
              <el-input v-model="queryParams.openid" placeholder="请输入微信OpenID" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="AppID" prop="appid">
              <dict-select v-model="queryParams.appid" dict-key="mall_wx_client" placeholder="请选择小程序AppID" clearable @change="handleQuery" />
            </el-form-item>
            <el-form-item label="昵称" prop="nickname">
              <el-input v-model="queryParams.nickname" placeholder="请输入昵称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="手机号" prop="phoneNumber">
              <el-input v-model="queryParams.phoneNumber" placeholder="请输入手机号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <!-- <el-form-item label="国家" prop="country">
                <el-input v-model="queryParams.country" placeholder="请输入国家" clearable @keyup.enter="handleQuery" />
              </el-form-item>
              <el-form-item label="语言" prop="language">
                <el-input v-model="queryParams.language" placeholder="请输入语言" clearable @keyup.enter="handleQuery" />
              </el-form-item> -->
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker clearable v-model="queryParams.createTime" type="date" value-format="YYYY-MM-DD" placeholder="请选择创建时间" />
            </el-form-item>
            <!-- <el-form-item label="更新时间" prop="updateTime">
                <el-date-picker clearable
                  v-model="queryParams.updateTime"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择更新时间"
                />
              </el-form-item> -->
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <!-- <el-col :span="1.5">
              <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mall:wxUser:add']">新增</el-button>
            </el-col> -->
          <!-- <el-col :span="1.5">
              <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['mall:wxUser:edit']">修改</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['mall:wxUser:remove']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['mall:wxUser:export']">导出</el-button>
            </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="wxUserList" @selection-change="handleSelectionChange" border>
        <!-- <el-table-column type="selection" width="55" align="center" /> -->
        <!-- <el-table-column label="id" align="center" prop="id" v-if="true" /> -->
        <el-table-column label="头像" align="center" prop="avatarUrl" width="80px">
          <template #default="scope">
            <el-image :src="scope.row.avatarUrl" style="width: 60px; height: 60px; border-radius: 50%" />
          </template>
        </el-table-column>
        <el-table-column label="OpenID" align="center" prop="openid" width="260px" />
        <el-table-column label="AppID" align="center" prop="appid" width="180px">
          <template #default="scope">
            <dict-tag :value="scope.row.appid" :options="mall_wx_client" />
          </template>
        </el-table-column>
        <el-table-column label="昵称" align="center" prop="nickname" width="180px" />
        <el-table-column label="性别" align="center" prop="gender" />
        <el-table-column label="手机号" align="center" prop="phoneNumber" width="120px" />
        <el-table-column label="城市" align="center" prop="city" />
        <el-table-column label="省份" align="center" prop="province" />
        <el-table-column label="国家" align="center" prop="country" />
        <el-table-column label="语言" align="center" prop="language" />
        <el-table-column label="状态" align="center" prop="status" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-tooltip content="修改" placement="top">
                <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mall:wxUser:edit']"></el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mall:wxUser:remove']"></el-button>
              </el-tooltip>
            </template>
          </el-table-column> -->
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改用户列表对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body>
      <el-form ref="wxUserFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="UnionID" prop="unionid">
          <el-input v-model="form.unionid" placeholder="请输入微信 UnionID" readonly />
        </el-form-item>
        <el-form-item label="AppID" prop="appid">
          <el-input v-model="form.appid" placeholder="请输入小程序appid" readonly />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="form.nickname" placeholder="请输入昵称" />
        </el-form-item>
        <!-- <el-form-item label="头像" prop="avatarUrl">
            <el-input v-model="form.avatarUrl" placeholder="请输入头像" />
          </el-form-item> -->
        <el-form-item label="性别" prop="gender">
          <el-input v-model="form.gender" placeholder="请输入性别" />
        </el-form-item>
        <el-form-item label="手机号" prop="phoneNumber">
          <el-input v-model="form.phoneNumber" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="城市" prop="city">
          <el-input v-model="form.city" placeholder="请输入城市" />
        </el-form-item>
        <el-form-item label="省份" prop="province">
          <el-input v-model="form.province" placeholder="请输入省份" />
        </el-form-item>
        <el-form-item label="国家" prop="country">
          <el-input v-model="form.country" placeholder="请输入国家" />
        </el-form-item>
        <el-form-item label="语言" prop="language">
          <el-input v-model="form.language" placeholder="请输入语言" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="创建者" prop="createBy">
          <el-input v-model="form.createBy" placeholder="请输入创建者" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker clearable v-model="form.createTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择创建时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="更新人" prop="updateBy">
          <el-input v-model="form.updateBy" placeholder="请输入更新人" />
        </el-form-item>
        <el-form-item label="更新时间" prop="updateTime">
          <el-date-picker clearable v-model="form.updateTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择更新时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="WxUser" lang="ts">
import { listWxUser, getWxUser, delWxUser, addWxUser, updateWxUser } from '@/api/mall/wxUser';
import { WxUserVO, WxUserQuery, WxUserForm } from '@/api/mall/wxUser/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const wxUserList = ref<WxUserVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑

const queryFormRef = ref<ElFormInstance>();
const wxUserFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: WxUserForm = {
  id: undefined,
  openid: undefined,
  unionid: undefined,
  appid: undefined,
  nickname: undefined,
  avatarUrl: undefined,
  gender: undefined,
  phoneNumber: undefined,
  email: undefined,
  city: undefined,
  province: undefined,
  country: undefined,
  language: undefined,
  status: undefined,
  remark: undefined,
  createBy: undefined,
  createTime: undefined,
  updateBy: undefined,
  updateTime: undefined
};
const data = reactive<PageData<WxUserForm, WxUserQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    openid: undefined,
    appid: undefined,
    nickname: undefined,
    phoneNumber: undefined,
    country: undefined,
    language: undefined,
    status: undefined,
    createTime: undefined,
    updateTime: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    openid: [{ required: true, message: '微信 OpenID不能为空', trigger: 'blur' }],
    unionid: [{ required: true, message: '微信 UnionID不能为空', trigger: 'blur' }],
    appid: [{ required: true, message: '小程序appid不能为空', trigger: 'blur' }],
    nickname: [{ required: true, message: '昵称不能为空', trigger: 'blur' }],
    avatarUrl: [{ required: true, message: '头像不能为空', trigger: 'blur' }],
    gender: [{ required: true, message: '性别(0-未知, 1-男, 2-女)不能为空', trigger: 'blur' }],
    phoneNumber: [{ required: true, message: '手机号不能为空', trigger: 'blur' }],
    email: [{ required: true, message: '邮箱不能为空', trigger: 'blur' }],
    city: [{ required: true, message: '城市不能为空', trigger: 'blur' }],
    province: [{ required: true, message: '省份不能为空', trigger: 'blur' }],
    country: [{ required: true, message: '国家不能为空', trigger: 'blur' }],
    language: [{ required: true, message: '语言不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '0-正常；1-禁用不能为空', trigger: 'change' }],
    remark: [{ required: true, message: '备注不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

const { mall_wx_client } = toRefs<any>(proxy?.useDict('mall_wx_client'));

/** 查询用户列表列表 */
const getList = async () => {
  loading.value = true;
  const res = await listWxUser(queryParams.value);
  wxUserList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  wxUserFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: WxUserVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加用户列表';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: WxUserVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getWxUser(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改用户列表';
};

/** 提交按钮 */
const submitForm = () => {
  wxUserFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateWxUser(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addWxUser(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: WxUserVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除用户列表编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delWxUser(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'mall/wxUser/export',
    {
      ...queryParams.value
    },
    `wxUser_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
