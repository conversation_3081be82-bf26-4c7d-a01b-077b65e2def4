<template>
  <div class="goods-list-wrapper">
    <div class="section-title">
      <div class="title-text">商品信息(制卡批次)</div>
      <div class="title-extra">
        <el-button type="primary" @click="handleAddItem">添加</el-button>
      </div>
    </div>

    <el-table :data="list" border>
      <el-table-column label="卡批次" prop="batchNumber" min-width="180">
        <template #default="scope">
          <el-tooltip content="查看该批次可售的卡" placement="top">
            <el-link type="primary" @click="queryCardNumberByBatch(scope.row.batchNumber)">{{ scope.row.batchNumber }}</el-link>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column label="面值" prop="value" width="120">
        <template #default="scope">
          <span>{{ parseAmount(scope.row.value) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="折扣" prop="discount" width="120">
        <template #default="scope">
          <span>{{ calculateDiscount(scope.row) }}%</span>
        </template>
      </el-table-column>

      <el-table-column label="售价" prop="unitPrice" width="120">
        <template #default="scope">
          <span>{{ parseAmount(scope.row.unitPrice) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="需求数量" prop="qty" width="120">
        <template #default="scope">
          <span>{{ scope.row.qty }}</span>
        </template>
      </el-table-column>

      <!-- 出卡数量 -->
      <el-table-column v-if="['21', '31', '40'].includes(props.orderStatus)" label="出卡数量" prop="authQty" width="120">
        <template #default="scope">
          <span>{{ scope.row.authQty || 0 }}</span>
        </template>
      </el-table-column>

      <el-table-column label="小计" prop="detailTotal" width="120">
        <template #default="scope">
          <span>{{ centToYuan(scope.row.totalAmount) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="120">
        <template #default="scope">
          <el-button link type="success" @click="handleEditItem(scope.row)"> 编辑 </el-button>
          <el-button link type="danger" @click="handleRemoveItem(scope.row)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="table-footer">
      <el-form label-width="100px" class="footer-form">
        <el-row :gutter="20">
          <el-col :span="5">
            <el-form-item label="总数量">
              <span>{{ totalQty }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="总面值">
              <span>{{ centToYuan(totalValues) }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="优惠金额">
              <el-input-number
                style="width: 200px"
                v-model="discountAmount"
                :min="0"
                :precision="2"
                :step="1"
                :disabled="isView"
                @change="calculateTotals"
              >
                <template #append>元</template>
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="应收金额">
              <span>{{ centToYuan(totalAmount) }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <!-- 添加出卡需求对话框 -->
      <AddChuCardDialog ref="AddChuCardDialogRef" @success="handleAddCardSuccess" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, defineProps, defineEmits, watch, getCurrentInstance } from 'vue';
import { parseAmount } from '@/utils/ruoyi';

import { updatePresaleOrderDetailList, delPresaleOrderDetailList, addPresaleOrderDetailList } from '@/api/mall/presaleOrder/presaleOrderDetail';
import { yuanToCent, centToYuan } from '@/utils/moneyUtils';
import AddChuCardDialog from './AddChuCardDialog.vue';

const { proxy } = getCurrentInstance();
const emits = defineEmits(['change']);

const props = defineProps({
  value: {
    type: Array,
    default: () => []
  },
  isView: {
    type: Boolean,
    default: false
  },
  orderStatus: {
    type: String,
    default: '01'
  },
  orderId: {
    type: String,
    default: ''
  }
});

watch(
  () => props.orderStatus,
  (val) => {
    proxy.$nextTick(() => {
      calculateTotals();
    });
  }
);

const AddChuCardDialogRef = ref(null);
const itemList = ref(new Map());
const discountAmount = ref(0);
const totalQty = ref(0);
const totalValues = ref(0);
const totalAmount = ref(0);

// 安全的数字转换，避免NaN问题
const safeNumber = (value, defaultValue = 0) => {
  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
};

// 计算所有总计值
const calculateTotals = () => {
  const isShowAuthQty = ['21', '31', '40'].includes(String(props.orderStatus));
  const items = Array.from(itemList.value.values());
  totalAmount.value = 0;
  for (const item of items) {
    if (isShowAuthQty) {
      item.totalAmount = item.unitPrice * item.authQty;
    } else {
      item.totalAmount = item.unitPrice * item.qty;
    }
    totalAmount.value += safeNumber(item.totalAmount);
  }

  totalAmount.value = totalAmount.value - yuanToCent(discountAmount.value);

  if (isShowAuthQty) {
    totalQty.value = items.reduce((sum, item) => sum + safeNumber(item.authQty), 0);
    totalValues.value = items.reduce((sum, item) => {
      const value = safeNumber(item.value);
      const qty = safeNumber(item.authQty);
      return sum + value * qty;
    }, 0);
  } else {
    totalQty.value = items.reduce((sum, item) => sum + safeNumber(item.qty), 0);
    totalValues.value = items.reduce((sum, item) => {
      const value = safeNumber(item.value);
      const qty = safeNumber(item.qty);
      return sum + value * qty;
    }, 0);
  }
  emits('change', getData());
};

// 计算折扣
const calculateDiscount = (item) => {
  if (item.discount) {
    return item.discount;
  }
  const unitPrice = safeNumber(item.unitPrice);
  const value = safeNumber(item.value);
  // 计算折扣 = 售价 / 面值 * 100%
  return value > 0 ? Math.round((unitPrice / value) * 100) : 100;
};

// 商品列表计算属性
const list = computed(() => {
  return Array.from(itemList.value.values());
});

// 添加方法
const handleAddItem = () => {
  AddChuCardDialogRef.value?.open(null);
};

const handleEditItem = (row) => {
  AddChuCardDialogRef.value?.open(row, true);
};

const handleRemoveItem = async (row) => {
  if (['01', '02'].includes(props.orderStatus) && props.orderId) {
    await delCardItem(row);
  }
  itemList.value.delete(row.batchNumber);
  calculateTotals(); // 删除商品后重新计算总计
};

const delCardItem = async (row) => {
  try {
    await delPresaleOrderDetailList(row.id);
  } catch (error) {
    proxy.message.error('删除失败');
  }
};

// 商品数据初始化
const initData = (data = [], discount = 0) => {
  itemList.value.clear();
  discountAmount.value = centToYuan(discount);
  if (data && data.length > 0) {
    data.forEach((item) => {
      itemList.value.set(item.batchNumber, { ...item });
    });
  }
  calculateTotals(); // 初始化数据后计算总计
};

// 处理添加商品成功事件
const handleAddCardSuccess = async (cardItem) => {
  // 01:待提交 02:审核不通过，再次编辑的时候，需要单独更新
  if (['01', '02'].includes(props.orderStatus) && props.orderId) {
    const oldItem = itemList.value.get(cardItem.batchNumber);
    if (oldItem) {
      await updateCardItem({ ...cardItem, id: oldItem.id, orderId: oldItem.orderId }); // 出卡
    } else {
      const newId = await addCardItem({ ...cardItem, orderId: props.orderId }); // 添加
      cardItem.id = newId;
    }
  }
  itemList.value.set(cardItem.batchNumber, { ...cardItem });
  calculateTotals(); // 添加或编辑商品后重新计算总计
};

// 处理编辑商品成功事件
const updateCardItem = async (cardItem) => {
  // 01:待提交 02:审核不通过，再次编辑的时候，需要单独更新
  try {
    await updatePresaleOrderDetailList(cardItem);
  } catch (error) {
    proxy.message.error('更新失败');
  }
};

// 添加商品
const addCardItem = async (cardItem) => {
  try {
    const res = await addPresaleOrderDetailList(cardItem);
    if (res.code === 200) {
      return res.data;
    }
  } catch (error) {
    console.log(error);
    proxy.message.error('添加失败');
  }
};
// 获取商品数据
const getData = () => {
  return {
    detailList: Array.from(itemList.value.values()),
    discountAmount: yuanToCent(discountAmount.value),
    orderAmount: totalAmount.value,
    totalValues: totalValues.value
  };
};

/** 查询该批次可售的卡 */
const queryCardNumberByBatch = (batchNumber) => {
  const routeUrl = `/mall/card/number?batchNumber=${batchNumber}`;
  window.open(routeUrl, '_blank');
};

// 暴露方法
defineExpose({
  initData,
  getData
});
</script>

<style lang="scss" scoped>
.goods-list-wrapper {
  margin-bottom: 20px;

  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dcdfe6;

    .title-text {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
    }
  }

  .table-footer {
    margin-top: 20px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;

    .footer-form {
      :deep(.el-form-item) {
        margin-bottom: 0;

        .el-form-item__label {
          color: #606266;
          font-weight: normal;
        }
      }
    }
  }
}
</style>
