<template>
  <el-drawer v-model="visible" title="添加要销售的制卡批次" direction="rtl" size="600px" :close-on-click-modal="false" destroy-on-close>
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="140px" class="drawer-form">
      <el-form-item label="制卡批次" prop="batchNumber" required>
        <template #label>
          <div>
            <span>制卡批次</span>
            <el-tooltip content="前往查看可售卡号" placement="top">
              <el-icon :size="14" @click="goCardNumberList">
                <Link />
              </el-icon>
            </el-tooltip>
          </div>
        </template>
        <el-select
          v-model="formData.batchNumber"
          placeholder="输入卡次名称，选择制卡批次"
          filterable
          remote
          :remote-method="searchBatchByName"
          :loading="batchLoading"
          @change="handleBatchChange"
        >
          <el-option v-for="item in filteredBatchOptions" :key="item.id" :label="item.name" :value="item.batchNumber">
            <div class="batch-option">
              <span>{{ item.name }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="批次号" prop="batchNumber">
        <el-input v-model="formData.batchNumber" disabled />
      </el-form-item>

      <el-form-item label="面值" prop="value">
        <el-input v-model="formData.value" disabled>
          <template #prepend>¥</template>
        </el-input>
      </el-form-item>

      <el-form-item label="制卡数量" prop="madeQty">
        <el-input v-model="formData.madeQty" disabled />
      </el-form-item>

      <el-form-item label="可售数量" prop="availableQty">
        <el-input v-model="formData.availableQty" disabled />
      </el-form-item>

      <el-form-item label="需求数量" prop="qty" required>
        <template #label>
          <div>
            <span>需求数量</span>
            <el-tooltip content="需求数量不能超过可售数量" placement="top">
              <el-icon :size="14">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </div>
        </template>
        <el-input-number
          v-model="formData.qty"
          placeholder="不应超过可售数量"
          :min="0"
          :max="formData.availableQty"
          :precision="0"
          :step="1"
          style="width: 100%"
          @change="handleQtyChange"
        />
      </el-form-item>

      <el-form-item label="销售折扣" prop="discount">
        <template #label>
          <div>
            <span>销售折扣(%) </span>
            <el-tooltip content="销售折扣不能超过100%" placement="top">
              <el-icon :size="14">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </div>
        </template>
        <el-input-number
          v-model="formData.discount"
          placeholder="举例：如果是9.8折，则输入98"
          :min="0"
          :max="100"
          :precision="2"
          :step="10"
          style="width: 100%"
          @change="handleDiscountChange"
        >
          <template #suffix>%</template>
        </el-input-number>
      </el-form-item>

      <el-form-item label="销售价格(元)" prop="unitPrice">
        <el-input-number
          v-model="formData.unitPrice"
          placeholder="不宜超过面值"
          :min="0"
          :precision="2"
          :step="0.01"
          style="width: 100%"
          @change="handleSalePriceChange"
        >
        </el-input-number>
      </el-form-item>

      <el-form-item label="金额小计(元)">
        <el-statistic :value="totalAmount" :precision="2">
          <template #prefix>¥</template>
        </el-statistic>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { listExchangeBatch } from '@/api/mall/exchangeBatch';
import { centToYuan, yuanToCent } from '@/utils/moneyUtils';
import { Position } from '@element-plus/icons-vue';

const props = defineProps({
  selectedBatchIds: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['success']);

const visible = ref(false);
const formRef = ref();
const submitting = ref(false);
const currentCardItem = ref(null);

// 表单数据
const formData = reactive({
  batchNumber: '',
  qty: undefined,
  discount: null,
  unitPrice: undefined
});

// 批次选项
const batchLoading = ref(false);
const batchOptions = ref([]);

// 过滤批次选项
const filteredBatchOptions = computed(() => {
  if (props.selectedBatchIds.length === 0) {
    return batchOptions.value;
  }
  return batchOptions.value.filter((item) => !props.selectedBatchIds.includes(item.id));
});

// 根据名称搜索批次
const searchBatchByName = async (query) => {
  if (query === '') {
    batchOptions.value = [];
    return;
  }

  batchLoading.value = true;
  try {
    const res = await listExchangeBatch({
      pageNum: 1,
      pageSize: 20, // 限制返回数量
      name: query // 根据名称搜索
    });

    // 检查返回的数据结构
    if (res && res.code === 200) {
      // 处理不同的数据结构情况
      batchOptions.value = res.data?.rows || res.rows || [];
    } else {
      console.error('获取批次列表接口返回异常', res);
      batchOptions.value = [];
    }
  } catch (error) {
    console.error('搜索批次失败', error);
    batchOptions.value = [];
  } finally {
    batchLoading.value = false;
  }
};

// 事件处理
const handleBatchChange = (batchNumber) => {
  const batch = batchOptions.value.find((item) => item.batchNumber === batchNumber);
  if (batch) {
    if (batch.availableQty > 0) {
      formData.batchNumber = batch.batchNumber;
      formData.name = batch.name;
      formData.value = centToYuan(batch.value);
      formData.madeQty = batch.qty;
      formData.availableQty = batch.availableQty;
      formData.availableQty = batch.availableQty;
      formData.qty = '';
    } else {
      ElMessage.warning('该批次已售完');
    }
  }
};

const handleDiscountChange = () => {
  // 更新销售价格
  if (formData.value > 0) {
    // 使用精度控制，避免浮点数计算问题
    formData.unitPrice = parseFloat((formData.value * (formData.discount / 100)).toFixed(2));
  }
};

const handleSalePriceChange = () => {
  // 根据销售价格反向计算折扣
  if (formData.value > 0) {
    // 计算折扣百分比并保留两位小数
    formData.discount = parseFloat(((formData.unitPrice / formData.value) * 100).toFixed(2));
  }
};

const totalAmount = computed(() => {
  return parseFloat((formData.qty * formData.unitPrice).toFixed(2));
});

const resetForm = () => {
  formData.batchNumber = '';
  formData.unitPrice = '';
  formData.qty = '';
  formData.discount = '';
};

const handleClose = () => {
  visible.value = false;
  resetForm();
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitting.value = true;

    // 构造返回数据
    const cardItem = {
      ...currentCardItem.value,
      batchNumber: formData.batchNumber,
      qty: formData.qty,
      discount: formData.discount,
      unitPrice: formData.unitPrice * 100,
      value: formData.value * 100,
      madeQty: formData.madeQty,
      availableQty: formData.availableQty
    };

    emit('success', cardItem);
    handleClose();
  } catch (error) {
    console.error('表单验证失败', error);
  } finally {
    submitting.value = false;
  }
};

// 表单校验规则
const rules = {
  batchNumber: [{ required: true, message: '请选择制卡批次', trigger: 'change' }],
  qty: [
    { required: true, message: '请输入需求数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '需求数量必须大于0', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入需求数量'));
        } else if (value > formData.availableQty) {
          callback(new Error(`可用库存不足，最大可选${formData.availableQty}张`));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  discount: [
    { required: true, message: '请输入销售折扣', trigger: ['blur', 'change'] },
    { type: 'number', min: 0, max: 100, message: '折扣必须在0-100之间', trigger: ['blur', 'change'] }
  ]
};

const open = async (row = null) => {
  visible.value = true;
  console.log(row);
  currentCardItem.value = row;
  if (row?.batchNumber) {
    const res = await listExchangeBatch({
      pageNum: 1,
      pageSize: 1,
      batchNumber: row.batchNumber
    });
    if (res && res.code === 200) {
      const batch = res?.rows[0];
      batchOptions.value = res?.rows || [];
      if (batch) {
        formData.batchNumber = batch.id;
        formData.batchNumber = batch.batchNumber;
        formData.value = centToYuan(batch.value);
        formData.qty = row.qty;
        formData.madeQty = batch.qty;
        formData.availableQty = batch.availableQty;
      }
    }
    // 销售金额 反算折扣
    formData.unitPrice = centToYuan(row.unitPrice);
    handleSalePriceChange();
  }
};

/**
 * 前往制卡批次列表
 */
const goCardNumberList = () => {
  window.open('/mall/card/number', '_blank');
};

defineExpose({
  open
});
</script>

<style lang="scss" scoped>
.drawer-form {
  padding: 20px;

  .form-section {
    margin-bottom: 24px;
    padding: 16px;
    background-color: var(--el-fill-color-light);
    border-radius: 4px;

    .section-title {
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: bold;
      color: var(--el-text-color-primary);
    }
  }

  .batch-option {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .batch-info {
      display: flex;
      gap: 8px;
    }
  }
}

.drawer-footer {
  padding: 16px;
  text-align: right;
}

:deep(.el-drawer__header) {
  margin-bottom: 0;
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}
</style>
