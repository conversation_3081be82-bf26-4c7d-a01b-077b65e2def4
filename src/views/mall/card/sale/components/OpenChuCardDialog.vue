<template>
  <el-dialog v-model="visible" title="添加出卡" direction="rtl" width="60%" :close-on-click-modal="false" :destroy-on-close="true" draggable>
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="150px" append-to-body label-position="top">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="制卡批次" prop="batchNumber" required>
            <template #label>
              <span>制卡批次</span>
              <el-tooltip content="查看该批次可售的卡" placement="top">
                <el-icon style="margin-left: 5px" @click="queryCardNumberByBatch(formData.batchNumber)"><Link /></el-icon>
              </el-tooltip>
            </template>
            <el-select
              v-model="formData.batchNumber"
              placeholder="请选择制卡批次"
              filterable
              disabled
              remote
              :remote-method="searchBatchByName"
              :loading="batchLoading"
            >
              <el-option v-for="item in filteredBatchOptions" :key="item.batchNumber" :label="item.name" :value="item.batchNumber">
                <div class="batch-option">
                  <span>{{ item.name }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="批次号" prop="batchNumber">
            <el-input v-model="formData.batchNumber" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="制卡数量" prop="qty">
            <el-input v-model="formData.madeQty" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="可售数量" prop="availableQty">
            <template #label>
              <span>批次仍可售数量</span>
              <el-tooltip content="指尚未关联销售订单的卡数量" placement="top">
                <el-icon style="margin-left: 5px"><QuestionFilled /></el-icon>
              </el-tooltip>
            </template>
            <el-input v-model="formData.availableQty" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="需求数量" prop="qty" required>
            <el-input-number v-model="formData.qty" :min="1" :precision="0" :step="1" style="width: 100%" @change="handleQtyChange" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="面值" prop="value">
            <el-input v-model="formData.value" disabled>
              <template #prepend>¥</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销售折扣" prop="discount" required>
            <el-input-number
              v-model="formData.discount"
              :min="0"
              :max="100"
              :precision="2"
              :step="10"
              style="width: 100%"
              @change="handleDiscountChange"
              disabled
            >
              <template #suffix>%</template>
            </el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销售价格" prop="unitPrice">
            <el-input-number
              v-model="formData.unitPrice"
              :min="0"
              :precision="2"
              :step="0.01"
              style="width: 100%"
              @change="handleSalePriceChange"
              disabled
            >
              <template #prepend>¥</template>
            </el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="需求金额小计">
            <el-input v-model="formData.detailTotal" disabled>
              <template #prepend>¥</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="已出卡数量" prop="authQty" disabled>
            <el-input v-model="formData.authQty" placeholder="输入卡号后自动计算" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="卡流水号-开始" prop="startCardNo" required>
            <el-input v-model="formData.startCardNo" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="卡流水号-结束" prop="endCardNo" required>
            <el-input @blur="verifyCards" v-model="formData.endCardNo" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="此次出卡张数">
            <el-input v-model="formData.cardCount" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" placeholder="请输入" :maxlength="200" show-word-limit />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { listExchangeBatch } from '@/api/mall/exchangeBatch';
import { centToYuan, yuanToCent } from '@/utils/moneyUtils';
import { getAvaliable, addCard } from '@/api/mall/presaleOrder/presaleOrderDetail';

import { ElMessage } from 'element-plus';
import { fa } from 'element-plus/es/locale/index.mjs';

const props = defineProps({
  selectedBatchIds: {
    type: Array,
    default: () => []
  },
  detailId: {
    type: Number,
    required: true
  },
  orderId: {
    type: Number,
    required: true
  }
});

const emit = defineEmits(['success']);

const visible = ref(false);
const formRef = ref();
const submitting = ref(false);
const currentBatch = ref(null);

// 表单数据
const formData = reactive({
  batchNumber: '',
  qty: '',
  discount: '',
  unitPrice: '',
  detailTotal: '',
  startCardNo: '',
  endCardNo: '',
  cardCount: '',
  remark: ''
});

// 批次选项
const batchLoading = ref(false);
const batchOptions = ref([]);
const filteredBatchOptions = ref([]);
const handleQtyChange = () => {
  calculateTotal();
};

const handleDiscountChange = () => {
  // 更新销售价格
  if (formData.value > 0) {
    // 使用精度控制，避免浮点数计算问题
    formData.unitPrice = parseFloat((formData.value * (formData.discount / 100)).toFixed(2));
  }
  calculateTotal();
};

const handleSalePriceChange = () => {
  // 根据销售价格反向计算折扣
  if (formData.value > 0) {
    // 计算折扣百分比并保留两位小数
    formData.discount = parseFloat(((formData.unitPrice / formData.value) * 100).toFixed(2));
  }
  calculateTotal();
};

const calculateTotal = () => {
  // 使用精度控制，避免浮点数计算问题
  formData.detailTotal = parseFloat((formData.qty * formData.unitPrice).toFixed(2));
};

const resetForm = () => {
  formData.batchNumber = '';
  formData.unitPrice = '';
  formData.qty = '';
  formData.discount = '';
  formData.detailTotal = '';
  formData.startCardNo = '';
  formData.endCardNo = '';
  formData.cardCount = '';
  formData.remark = '';
};

const handleClose = () => {
  resetForm();
  visible.value = false;
};

// 卡号验证状态
const cardVerifying = ref(false);
const cardValid = ref(false);
const cardErrorMsg = ref('');

// 验证卡号
const verifyCards = async () => {
  // 重置验证状态
  cardValid.value = true;
  cardErrorMsg.value = '';
  submitting.value = true;

  if (!formData.startCardNo || !formData.endCardNo) {
    cardValid.value = false;
    cardErrorMsg.value = '请输入开始和结束卡号';
    return { isValid: false, errorMsg: cardErrorMsg.value };
  }

  cardVerifying.value = true;

  try {
    const res = await getAvaliable({
      startNo: formData.startCardNo,
      endNo: formData.endCardNo,
      id: props.detailId
    });

    if (res && res.code === 200) {
      // 更新卡数量
      formData.cardCount = res.data >= 0 ? res.data : 0;

      // 检查卡数量是否为0
      if (formData.cardCount === 0) {
        cardValid.value = false;
        cardErrorMsg.value = '卡号范围内无可用卡';
        return { isValid: false, errorMsg: cardErrorMsg.value };
      }

      // 校验出卡张数是否小于等于需求卡数
      const totalCardCount = formData.cardCount + formData.authQty;
      if (totalCardCount > formData.qty) {
        cardValid.value = false;
        cardErrorMsg.value = `出卡张数(${totalCardCount})大于需求数量(${formData.qty}),请调整卡号范围`;
        return { isValid: false, errorMsg: cardErrorMsg.value };
      }
    } else {
      cardValid.value = false;
      cardErrorMsg.value = res?.msg || '卡号验证失败';
      return { isValid: false, errorMsg: cardErrorMsg.value };
    }

    return { isValid: cardValid.value, errorMsg: cardErrorMsg.value };
  } catch (error) {
    console.error('卡号验证失败', error);
    cardValid.value = false;
    cardErrorMsg.value = '卡号验证异常，请重试';
    return { isValid: false, errorMsg: cardErrorMsg.value };
  } finally {
    cardVerifying.value = false;
    setTimeout(() => {
      submitting.value = false;
    }, 500);
  }
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitting.value = true;

    // 先校验卡号
    const { isValid, errorMsg } = await verifyCards();
    if (!isValid) {
      submitting.value = false;
      ElMessage.error(errorMsg || '出卡数量和需求数量不匹配');
      return;
    }

    // 调用添加卡API
    const res = await addCard({
      id: props.detailId,
      orderId: props.orderId,
      startNo: formData.startCardNo,
      endNo: formData.endCardNo
    });

    if (res && res.code === 200) {
      emit('success');
      handleClose();
    } else {
      console.error('添加卡失败', res);
      ElMessage.error(res.msg || '添加卡失败');
    }
  } catch (error) {
    console.error('表单验证失败', error);
  } finally {
    submitting.value = false;
  }
};

// 表单校验规则
const rules = {
  startCardNo: [{ required: true, message: '请输入开始卡号', trigger: 'blur' }],
  endCardNo: [{ required: true, message: '请输入结束卡号', trigger: 'blur' }]
};

const open = async (row = null) => {
  resetForm();
  visible.value = true;
  if (row?.batchNumber) {
    const res = await listExchangeBatch({
      pageNum: 1,
      pageSize: 1,
      batchNumber: row.batchNumber
    });
    if (res && res.code === 200) {
      const batch = res?.rows[0];
      filteredBatchOptions.value = res?.rows || [];
      batchOptions.value = res?.rows || [];
      if (batch) {
        formData.batchNumber = batch.id;
        formData.batchNumber = batch.batchNumber;
        formData.value = centToYuan(batch.value);
        formData.madeQty = batch.qty;
        formData.availableQty = batch.availableQty;
        formData.authQty = row.authQty || 0; // 已出卡数量
        formData.qty = row.qty || 0;
      }
    }
    // 销售金额 反算折扣
    formData.unitPrice = centToYuan(row.unitPrice);
    formData.detailTotal = centToYuan(row.detailTotal);
    handleSalePriceChange();
  }
};

/** 查询该批次可售的卡 */
const queryCardNumberByBatch = (batchNumber) => {
  const routeUrl = `/mall/card/number?batchNumber=${batchNumber}`;
  window.open(routeUrl, '_blank');
};

defineExpose({
  open
});
</script>

<style lang="scss" scoped>
.drawer-form {
  padding: 20px;

  .form-section {
    margin-bottom: 24px;
    padding: 16px;
    background-color: var(--el-fill-color-light);
    border-radius: 4px;

    .section-title {
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: bold;
      color: var(--el-text-color-primary);
    }
  }

  .batch-option {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .batch-info {
      display: flex;
      gap: 8px;
    }
  }
}

.drawer-footer {
  padding: 16px;
  text-align: right;
}

:deep(.el-drawer__header) {
  margin-bottom: 0;
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}
</style>
