<template>
  <el-dialog title="开卡" destroy-on-close v-model="visible" width="500px" append-to-body>
    <div class="tips-info mb-[16px]">
      <span>为了避免我方或者客户的资产损失，目前订单中的{{ cardCount }}张卡都处于锁定状态,</span>
      <span style="color: red">需要开卡，客户才能激活绑定卡券。</span>
      <p>作为业务方，你应该确保客户已经收到了相应数量卡券。</p>
      <p>请上传可以证明客户已经收到货了截图凭证，以便在系统做相应的记录。</p>
    </div>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="开卡凭证" prop="certificateFiles">
        <FileUpload v-model="form.certificateFiles" :limit="5" />
      </el-form-item>
      <el-form-item label="开卡备注" prop="openCardNote">
        <el-input
          v-model="form.openCardNote"
          type="textarea"
          placeholder="请输入备注，如客户微信确认收货"
          :rows="4"
          maxlength="250"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue';
import { openCard, getPresaleOrder } from '@/api/mall/presaleOrder/presaleOrder';
const { proxy } = getCurrentInstance();

const emit = defineEmits(['submit', 'cancel']);

const visible = ref(false);
const loading = ref(false);
const cardCount = ref(0);

const formRef = ref();
const form = ref({
  id: undefined,
  certificateFiles: [], // 开卡凭证
  openCardNote: '' // 开卡备注
});

const rules = {
  certificateFiles: [{ required: true, message: '请上传开卡凭证', trigger: 'change' }],
  openCardNote: [{ required: true, message: '请输入备注', trigger: 'blur' }]
};

// 获取所有出卡的数量
const getCardCount = async () => {
  const res = await getPresaleOrder(form.value.id);
  if (res.code === 200) {
    const detailList = res?.data?.detailList;
    if (detailList) {
      cardCount.value = detailList.reduce((acc, item) => acc + item.authQty, 0);
    }
  }
};

// 取消按钮
const cancel = () => {
  reset();
  emit('cancel');
};

// 表单重置
const reset = () => {
  form.value = {
    id: undefined,
    certificateFiles: [],
    openCardNote: ''
  };
  formRef.value?.resetFields();
  visible.value = false;
};

// 弹窗打开
const open = (row) => {
  visible.value = true;
  form.value.id = row.id;
  getCardCount();
};

// 确定按钮
const handleSubmit = async () => {
  await formRef.value.validate();
  loading.value = true;
  try {
    const res = await openCard(form.value);
    if (res.code === 200) {
      proxy.$modal.msgSuccess('开卡成功');
      emit('success', form.value);
      reset();
    }
  } catch (error) {
    console.error('开卡失败', error);
  } finally {
    loading.value = false;
  }
};

defineExpose({
  open
});
</script>

<style scoped>
.tips-info {
  padding: 12px;
  background-color: #f4f4f5;
  border-radius: 4px;
}
.tips-info p {
  margin: 0;
  line-height: 1.6;
  color: #909399;
  font-size: 13px;
}
</style>
