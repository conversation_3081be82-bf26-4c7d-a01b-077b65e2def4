<template>
  <el-dialog title="提交审核" destroy-on-close v-model="visible" width="500px" append-to-body>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-text type="warning" style="margin-bottom: 10px"> 审核通过将进入到出卡环节；不通过则需要修改后重新提交。 </el-text>
      <el-form-item label="审核结果" prop="orderStatus">
        <el-radio-group v-model="form.orderStatus">
          <el-radio label="21">审核通过</el-radio>
          <el-radio label="02">审核不通过</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审核备注" prop="remark">
        <el-input v-model="form.remark" type="textarea" placeholder="请输入审核备注" :rows="4" maxlength="500" show-word-limit />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue';

const emit = defineEmits(['submit', 'cancel']);
const visible = ref(false);
const loading = ref(false);

const formRef = ref();
const form = ref({
  id: undefined,
  remark: undefined,
  orderStatus: '' // 默认审核通过
});

const rules = {
  orderStatus: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
  remark: [{ required: true, message: '请输入审核备注', trigger: 'blur' }]
};

// 取消按钮
const cancel = () => {
  reset();
  emit('cancel');
};

// 表单重置
const reset = () => {
  form.value = {
    id: undefined,
    remark: undefined,
    orderStatus: '04'
  };
  formRef.value?.resetFields();
  visible.value = false;
};

// 弹窗打开
const open = (row) => {
  visible.value = true;
  form.value.id = row.id;
};

// 确定按钮
const handleSubmit = async () => {
  await formRef.value.validate();
  loading.value = true;
  try {
    emit('submit', form.value);
    reset();
  } finally {
    loading.value = false;
  }
};

defineExpose({
  open
});
</script>
