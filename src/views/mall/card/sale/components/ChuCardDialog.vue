<template>
  <el-dialog v-model="visible" title="出卡操作" width="1200px" :close-on-click-modal="false" :destroy-on-close="true">
    <div class="chu-card-dialog">
      <el-table :data="itemList" border>
        <el-table-column label="卡批次" prop="batchNumber" min-width="180">
          <template #default="scope">
            <el-tooltip content="快速查询该批次的可售卡的流水号" placement="">
              <el-link type="primary" @click="queryCardNumberByBatch(scope.row)">{{ scope.row.batchNumber }}</el-link>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column label="面值" prop="unitPrice" width="120">
          <template #default="scope">
            <span>{{ parseAmount(scope.row.value) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="售价" prop="unitPrice" width="120">
          <template #default="scope">
            <span>{{ parseAmount(scope.row.unitPrice) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="需求数量" prop="qty" width="120">
          <template #default="scope">
            <span>{{ scope.row.qty }}</span>
          </template>
        </el-table-column>

        <!-- 待出卡状态下显示已出卡数量列 -->
        <el-table-column label="已出卡数量" prop="authQty" width="120">
          <template #default="scope">
            <span>{{ scope.row.authQty || 0 }}</span>
          </template>
        </el-table-column>

        <el-table-column label="小计" prop="detailTotal" width="120">
          <template #default="scope">
            <span>{{ centToYuan(scope.row.totalAmount) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="160">
          <template #default="scope">
            <el-button v-if="scope.row.authQty < scope.row.qty" link type="success" @click="handleEditItem(scope.row)"> 添加 </el-button>
            <el-button v-if="scope.row.authQty > 0" link type="danger" @click="handleEditItem(scope.row, true)"> 撤销 </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="table-footer">
        <el-form :model="formData" label-width="100px" class="footer-form">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="总数量">
                <span>{{ totalQty }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="总面值">
                <span>{{ centToYuan(totalValues) }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="优惠金额">
                <el-input-number v-model="discountAmount" :min="0" :precision="2" :step="1" :disabled="isView" @change="handleDiscountAmountChange">
                  <template #append>元</template>
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="应收金额">
                <span>{{ centToYuan(orderAmount) }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting" :disabled="totalQty === 0">确认出卡</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 添加卡片对话框 -->
  <OpenChuCardDialog
    ref="openChuCardDialogRef"
    @success="handleCardSuccess"
    :detail-id="currentDetail.id"
    :order-id="currentDetail.orderId"
    :selected-batch-ids="selectedBatchIds"
  />
</template>

<script setup>
import { ref, reactive, computed, defineProps, defineEmits, watch } from 'vue';
import { parseAmount } from '@/utils/ruoyi';
import { yuanToCent, centToYuan } from '@/utils/moneyUtils';
import OpenChuCardDialog from './OpenChuCardDialog.vue';
import { confirmCard, getPresaleOrder } from '@/api/mall/presaleOrder/presaleOrder';
import { cancelAddedCard } from '@/api/mall/presaleOrder/presaleOrderDetail';
import { ElMessage, ElMessageBox } from 'element-plus';

const props = defineProps({
  orderId: {
    type: String,
    default: null
  }
});

const emit = defineEmits(['success']);

const openChuCardDialogRef = ref(null);
const itemList = ref([]);
const visible = ref(false);
const discountAmount = ref(0);

// 计算总数量
const totalQty = computed(() => {
  return itemList.value.reduce((sum, item) => sum + (item.authQty || 0), 0);
});

// 计算总面值
const totalValues = computed(() => {
  return itemList.value.reduce((sum, item) => sum + item.value * (item.authQty || 0), 0);
});

// 计算应收金额
const orderAmount = computed(() => {
  let total = 0;
  itemList.value.forEach((item) => {
    total += item.totalAmount;
  });

  return total - yuanToCent(discountAmount.value);
});

// 计算每个项目的总金额
const calculateItemtotalAmount = (itmes) => {
  itmes.forEach((item) => {
    item.totalAmount = item.unitPrice * (item.authQty || 0);
  });
  return itmes;
};

const currentDetail = ref({ id: null, orderId: null });
const selectedBatchIds = computed(() => {
  return itemList.value.map((item) => item.batchNumber);
});

const handleEditItem = (row, withdraw = false) => {
  // 二次确认 取消撤销之前添加的卡
  currentDetail.value = {
    id: row.id,
    orderId: row.orderId
  };

  if (withdraw) {
    // 当已出卡数量等于需求数量时，需要先撤销之前添加的卡
    ElMessageBox.confirm('当前批次已完成出卡，是否确认撤销之前添加的卡？', '撤销确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          const res = await cancelAddedCard({
            id: row.id,
            orderId: row.orderId
          });

          if (res.code === 200) {
            ElMessage.success('撤销成功');
            // 重新获取数据
            initData();
          } else {
            ElMessage.error(res.msg || '撤销失败');
          }
        } catch (error) {
          console.error('撤销操作失败:', error);
          ElMessage.error('撤销操作失败');
        }
      })
      .catch(() => {
        // 用户取消操作
      });
  } else {
    // 直接打开添加卡对话框
    openChuCardDialogRef.value?.open(row);
  }
};

const initData = async () => {
  visible.value = true;

  try {
    const res = await getPresaleOrder(props.orderId);
    if (res.code === 200) {
      // 添加到商品列表中
      itemList.value = calculateItemtotalAmount(res.data?.detailList || []);
      discountAmount.value = centToYuan(res.data.discountAmount);
      // 计算每个项目的总金额
    }
  } catch (error) {
    console.error('获取销售订单明细失败:', error);
  }
};

const submitting = ref(false);

const handleSubmit = async () => {
  // 需要判断每个批次 需求数量是否等于已出卡数量，不等于需要弹窗提醒，是否确认出卡
  const list = itemList.value;
  let index = 0;
  const isAllEqual = list.every((item) => {
    if (item.authQty !== item.qty) {
      index++;
    }
    return item.authQty === item.qty;
  });

  try {
    submitting.value = true;

    if (!isAllEqual) {
      // 当有卡未按需求数量出卡时显示此弹窗
      await ElMessageBox.confirm(
        `系统检测到还有${index}种卡没有按需求数量出卡，你是否确定要完成出卡？<br><br>单击确定，则订单将变成已出卡，请谨慎操作。`,
        '确认完成出卡?',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true
        }
      );
    } else {
      // 当所有卡都按需求数量出卡时显示此弹窗
      await ElMessageBox.confirm(`你已按需求数量完成出卡。<br><br>单击确定，则订单将变成已出卡，请谨慎操作。`, '确认完成出卡?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      });
    }

    const res = await confirmCard(props.orderId);
    if (res.code === 200) {
      ElMessage.success('出卡成功');
      emit('success');
      handleClose();
    }
  } catch (error) {
    console.error('出卡操作被取消或发生错误:', error);
    if (error !== 'cancel' && error !== 'close') {
      ElMessage.error('出卡失败');
    }
  } finally {
    submitting.value = false;
  }
};

const handleClose = () => {
  visible.value = false;
};

const formData = reactive({});

// 重新获取数据
const handleCardSuccess = () => {
  initData();
};

/** 查询该批次的可售卡的流水号 */
const queryCardNumberByBatch = (row) => {
  const routeUrl = `/mall/card/number?batchNumber=${row.batchNumber}`;
  window.open(routeUrl, '_blank');
};

defineExpose({
  initData
});
</script>

<style lang="scss" scoped>
.chu-card-dialog {
  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dcdfe6;

    .title-text {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
    }
  }

  .table-footer {
    margin-top: 20px;
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;

    .footer-form {
      :deep(.el-form-item) {
        margin-bottom: 0;

        .el-form-item__label {
          color: #606266;
          font-weight: normal;
        }
      }
    }
  }
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}
</style>
