<template>
  <el-dialog :title="title" v-model="visible" @close="handleClose" width="80%" destroy-on-close :close-on-click-modal="false">
    <el-row :gutter="10">
      <!-- 左侧锚点导航 -->
      <el-col :span="4">
        <el-anchor>
          <el-anchor-link href="#basic" title="客户信息" />
          <el-anchor-link href="#preAmount" title="预收款信息" v-if="formData.purpose === '1'" />
          <el-anchor-link href="#goodInfo" title="商品信息" />
          <el-anchor-link href="#openCardInfo" title="开卡记录" v-if="orderStatus === '40'" />
        </el-anchor>
      </el-col>
      <!-- 表单内容 -->
      <el-col :span="20">
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" :disabled="isView" label-position="top">
          <div id="basic" class="form-section">
            <div class="section-title">
              <span class="title-text">客户信息</span>
            </div>
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="订单编号" prop="orderNo">
                  <el-input v-model="formData.orderNo" placeholder="系统自动生成" disabled style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="用途" prop="purpose">
                  <el-select v-model="formData.purpose" placeholder="请选择用途" style="width: 100%">
                    <el-option v-for="dict in mall_sale_order_purpose" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="承做部门" prop="ownerDeptId">
                  <el-tree-select
                    v-model="formData.ownerDeptId"
                    :data="deptOptions"
                    :props="{ label: 'label', value: 'id' }"
                    placeholder="请选择承做部门"
                    check-strictly
                    filterable
                    clearable
                    @change="handleDeptChange"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="承做人（A角）" prop="ownerId">
                  <el-select
                    v-model="formData.ownerId"
                    placeholder="请选择承做人"
                    filterable
                    :loading="userLoadingByDeptId"
                    :disabled="!formData.ownerDeptId || isView"
                    @change="handleOwnerChange"
                    style="width: 100%"
                  >
                    <el-option v-for="item in userOptionsByDeptId" :key="item.userId" :label="item.nickName" :value="item.userId" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="客户" prop="customerId">
                  <template #label>
                    <span>
                      客户档案
                      <el-tooltip content="打开客户档案" placement="top" v-if="formData.customerId">
                        <el-icon @click="openCustomerDetailInView(formData.customerId)">
                          <Link />
                        </el-icon>
                      </el-tooltip>
                      <el-tooltip content="新增客户档案" placement="top" v-else>
                        <el-icon @click="openCustomerAddDialog()">
                          <Plus />
                        </el-icon>
                      </el-tooltip>
                    </span>
                  </template>
                  <customer-select v-model="formData.customerId" @change="handleCustomerChange" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="开票抬头" prop="invoiceTitleId">
                  <el-select
                    v-model="formData.invoiceTitleId"
                    placeholder="请选择开票抬头"
                    filterable
                    :loading="taxInvoiceTitleLoading"
                    style="width: 100%"
                    :disabled="!formData.customerId"
                  >
                    <el-option v-for="item in taxInvoiceTitleOptions" :key="item.id" :label="item.companyName" :value="item.id" />
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="6">
                <el-form-item label="销售折扣 (%)" prop="discountPercentage">
                  <el-input-number
                    v-model="formData.discountPercentage"
                    :min="0"
                    :max="100"
                    :precision="2"
                    :step="1"
                    placeholder="请输入销售折扣"
                    style="width: 100%"
                    disabled
                  >
                    <template #append>%</template>
                  </el-input-number>
                </el-form-item>
              </el-col> -->
              <el-col :span="6">
                <el-form-item label="客户需求" prop="customerDemand">
                  <el-input
                    type="textarea"
                    :rows="3"
                    maxlength="250"
                    show-word-limit
                    v-model="formData.customerDemand"
                    placeholder="请输入客户需求"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="备注" prop="remark">
                  <el-input
                    type="textarea"
                    :rows="3"
                    maxlength="250"
                    show-word-limit
                    v-model="formData.remark"
                    placeholder="请输入备注"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <!-- 预收款 -->
          <div id="preAmount" class="form-section" v-if="formData.purpose === '1'">
            <div class="section-title">
              <span class="title-text">预收款信息</span>
            </div>
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="是否预收款" prop="isAdvanceReceipt">
                  <el-radio-group v-model="formData.isAdvanceReceipt">
                    <el-radio v-for="dict in sys_yes_no" :key="dict.value" :label="dict.value">{{ dict.label }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="formData.isAdvanceReceipt === 'Y'">
                <el-form-item label="收款日期" prop="receiptDate">
                  <el-date-picker v-model="formData.receiptDate" type="date" placeholder="请选择收款日期" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="formData.isAdvanceReceipt === 'Y'">
                <el-form-item label="收款金额" prop="receiptAmount">
                  <el-input-number
                    v-model="formData.receiptAmount"
                    :min="0"
                    :precision="2"
                    :step="100"
                    placeholder="请输入收款金额"
                    style="width: 100%"
                  >
                    <template #append>元</template>
                  </el-input-number>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="10">
              <el-col :span="12" v-if="formData.isAdvanceReceipt === 'Y'">
                <el-form-item label="收款截图" prop="receiptFiles">
                  <ImageUpload v-model="formData.receiptFiles" :limit="5" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="formData.isAdvanceReceipt === 'Y'">
                <el-form-item label="收款说明" prop="receiptNote">
                  <el-input
                    type="textarea"
                    :rows="4"
                    maxlength="250"
                    show-word-limit
                    v-model="formData.receiptNote"
                    placeholder="请输入收款说明"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <!-- 商品信息 -->
          <div id="goodInfo" class="form-section">
            <goods-list ref="goodsListRef" :order-status="orderStatus" :is-view="isView" :order-id="formData.id" @change="handleGoodsChange" />
          </div>
          <div id="openCardInfo" class="form-section" v-if="orderStatus === '40'">
            <div class="section-title">
              <span class="title-text">开卡记录</span>
            </div>
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="开卡时间" prop="openCardTime">
                  {{ formData.openCardTime }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开卡凭证" prop="certificateFiles">
                  <FileUpload v-model="formData.certificateFiles" :limit="5" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="开卡备注" prop="openCardNote">
                  {{ formData.openCardNote }}
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </el-col>
    </el-row>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ isView ? '关闭' : '取消' }}</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="!isView">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, getCurrentInstance } from 'vue';
import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
import { useDeptSelect } from '@/hooks/useBusiness/deptSelect';
import { addPresaleOrder, updatePresaleOrder, getPresaleOrder } from '@/api/mall/presaleOrder/presaleOrder';
import dayjs from 'dayjs';

import GoodsList from './components/GoodsList.vue';
import { useUserSelectByDeptId } from '@/views/system/user/detail/userSelectByDeptId';
import CustomerSelect from '@/components/biz/CustomerSelect/index.vue';
import { useTaxInvoiceTitleSelect } from '@/views/crm/taxInvoiceTitle/detail/taxInvoiceTitleSelect';

const { proxy } = getCurrentInstance();
const goodsListRef = ref(null);
const emit = defineEmits(['success']);
// 字典数据
const { mall_sale_order_purpose, sys_yes_no } = toRefs(proxy.useDict('mall_sale_order_purpose', 'sys_yes_no'));

const formRef = ref();
const visible = ref(false);
const discountPercentage = ref(0);
const orderStatus = ref('01'); // 01:待提交 02:审核不通过 11:待审核 21:待出卡 31:已出卡 40:已回款

// 定义默认表单数据
const defaultFormData = {
  itemList: [],
  orderStatus: '01', // 01:待提交 02:审核不通过 11:待审核 21:待出卡 31:已出卡 40:已回款
  isAdvanceReceipt: 'N', // 是否预收款
  discountPercentage: '', // 销售折扣
  customerDemand: '', // 客户需求
  remark: '', // 备注
  receiptAmount: '', // 收款金额
  receiptFiles: [], // 收款截图
  receiptNote: '', // 收款说明
  openCardTime: '', // 开卡时间
  certificateFiles: '', // 开卡凭证
  openCardNote: '', // 开卡备注
  discountAmount: 0, // 折扣金额
  orderAmount: 0 // 订单金额
};
const formData = ref({ ...defaultFormData });

const title = computed(() => {
  if (orderStatus.value === '500') return '新增销售订单';
  if (orderStatus.value === '00' || isView.value) return '查看销售订单';
  return '编辑销售订单';
});

// 用户选择相关
const { loadUserList, userOptions, loading: userLoading } = useSysUserSelect();

// 部门树选择相关
const { deptOptions, loadDeptTree } = useDeptSelect();

// 添加部门下用户选择功能
const { userOptionsByDeptId, userLoadingByDeptId, loadUserListByDeptId } = useUserSelectByDeptId();

// 开票抬头选择相关
const { taxInvoiceTitleOptions, taxInvoiceTitleLoading, loadTaxInvoiceTitleList } = useTaxInvoiceTitleSelect();

// 表单校验规则
const rules = {
  purpose: [{ required: true, message: '请选择用途', trigger: 'change' }],
  ownerId: [{ required: true, message: '请选择承做人', trigger: 'change' }],
  ownerDeptId: [{ required: true, message: '请选择承做部门', trigger: 'change' }],
  customerId: [{ required: true, message: '请选择客户', trigger: 'change' }],
  invoiceTitleId: [{ required: true, message: '请选择开票抬头', trigger: 'change' }],
  isAdvanceReceipt: [{ required: true, message: '请选择是否预收款', trigger: 'change' }],
  itemList: [{ required: true, type: 'array', min: 1, message: '请至少添加一个商品', trigger: 'change' }]
};

const isView = computed(() => {
  // 500: 新增 00 查看 01: 待提交 02: 审核不通过 11: 待审核 21: 待出卡 31: 已出卡 40: 已回款

  //  只能查看 待出卡 已出卡 已回款
  if (orderStatus.value === '21' || orderStatus.value === '31' || orderStatus.value === '40' || orderStatus.value === '00') return true;
  return false;
});

const handleGoodsChange = (data) => {
  const { totalValues, orderAmount } = data;
  // 销售折扣 = 应收金额 / 总面值 * 100%
  formData.value.discountPercentage = totalValues > 0 ? Math.round((orderAmount / totalValues) * 100) : 100;
  discountPercentage.value = formData.value.discountPercentage;
};
// 方法
const dialogOpen = async (id, status = '500') => {
  let processedData = { ...defaultFormData };
  visible.value = true;
  orderStatus.value = status;

  if (id) {
    const { data, code } = await getPresaleOrder(id);
    if (code === 200) {
      orderStatus.value = data.orderStatus;
      processedData = {
        ...data
      };
    }
  }

  formData.value = processedData;

  // 加载 商品数据
  if (formData.value.detailList && formData.value.detailList.length > 0) {
    goodsListRef.value.initData(formData.value.detailList, formData.value.discountAmount);
  }

  // 加载相关数据
  loadDeptTree();
  loadUserList(); // 加载所有用户

  // 如果有部门ID，则加载该部门下的用户
  if (formData.value.ownerDeptId) {
    loadUserListByDeptId(formData.value.ownerDeptId);
  }

  // 如果有客户ID，则加载该客户的开票抬头
  if (formData.value.customerId) {
    loadTaxInvoiceTitleList({ customerId: formData.value.customerId, status: '0' });
  }
};

const handleClose = () => {
  visible.value = false;
  formRef.value?.resetFields();
  formData.value = { ...defaultFormData };
  emit('success');
};

// 格式化日期字段
const formatDateFields = (data) => {
  return {
    receiptDate: data.receiptDate && data.isAdvanceReceipt === 'Y' ? dayjs(data.receiptDate).format('YYYY-MM-DD HH:mm:ss') : undefined,
    openCardTime: data.openCardTime ? dayjs(data.openCardTime).format('YYYY-MM-DD HH:mm:ss') : undefined
  };
};

// 准备提交数据
const prepareSubmitData = (formData, goodsData) => {
  // 处理文件数组
  const files = {
    receiptFiles:
      Array.isArray(formData.receiptFiles) && formData.receiptFiles.length > 0
        ? formData.receiptFiles.join(',')
        : typeof formData.receiptFiles === 'string' && formData.receiptFiles.trim() !== ''
          ? formData.receiptFiles
          : null
  };

  return {
    ...formData,
    ...files,
    ...formatDateFields(formData),
    ...goodsData
  };
};

const handleSubmit = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    const goodsData = goodsListRef.value.getData();

    if (goodsData.detailList.length === 0) {
      proxy?.$modal.msgError('请至少添加一个商品');
      return;
    }

    // 处理提交数据
    const submitData = prepareSubmitData(formData.value, goodsData);

    if (submitData.id) {
      await updatePresaleOrder(submitData);
    } else {
      submitData.orderStatus = '01';
      await addPresaleOrder(submitData);
    }

    proxy?.$modal.msgSuccess('操作成功');
    emit('success');
    handleClose();
  } catch (error) {
    console.error('表单校验失败', error);
  }
};

// 处理部门变更，加载该部门下的用户
const handleDeptChange = (deptId) => {
  formData.value.ownerId = undefined; // 清空已选择的承做人
  loadUserListByDeptId(deptId); // 加载部门下的用户列表
};

// 处理承做人变更，自动更新承做部门
const handleOwnerChange = async (userId) => {
  if (!userId) return;

  // 从所有用户中找到当前选择的用户
  const selectedUser = userOptions.value.find((item) => item.userId === userId);
  if (!selectedUser) return;

  // 如果选择的用户所在部门与当前选择的部门不一致
  if (selectedUser.deptId && selectedUser.deptId !== formData.value.ownerDeptId) {
    try {
      await proxy?.$modal.confirm('指派承做人后，承做部门需要自动更新为该用户所在部门，是否确认？');
      formData.value.ownerDeptId = selectedUser.deptId;
      loadUserListByDeptId(selectedUser.deptId);
    } catch (error) {
      // 用户取消则恢复之前的选择
      formData.value.ownerId = undefined;
    }
  }
};

// 处理客户选择变更
const handleCustomerChange = (customerId) => {
  // 客户变更后，加载该客户的开票抬头列表
  if (customerId) {
    // 只加载状态为启用(0)的开票抬头
    loadTaxInvoiceTitleList({ customerId, status: '0' });
  } else {
    // 清空开票抬头列表
    taxInvoiceTitleOptions.value = [];
    // 清空已选择的开票抬头
    formData.value.invoiceTitleId = undefined;
  }
};

// 打开客户详情(查看页面)
const openCustomerDetailInView = (id) => {
  const routeUrl = `/crm/bizMg/customer/?id=${id}&openView=true`;
  window.open(routeUrl, '_blank');
};

// 打开客户弹窗
const openCustomerAddDialog = () => {
  const routeUrl = `/crm/bizMg/customer/?openAdd=true`;
  window.open(routeUrl, '_blank');
};

defineExpose({
  dialogOpen
});
</script>

<style lang="scss" scoped>
@import '@/assets/styles/anchorform.scss';
</style>
