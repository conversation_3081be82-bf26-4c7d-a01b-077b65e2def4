<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="订单号" prop="orderNo">
              <el-input v-model="queryParams.orderNo" placeholder="请输入订单号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户" prop="customerId">
              <customer-select v-model="queryParams.customerId" placeholder="请选择客户档案" @change="handleQuery" />
            </el-form-item>
            <el-form-item label="订单状态" prop="orderStatus">
              <el-select v-model="queryParams.orderStatus" placeholder="请选择订单状态" clearable filterable @change="handleQuery">
                <el-option v-for="dict in mall_sale_order_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="承做人（A角）" prop="ownerId">
              <el-select v-model="queryParams.ownerId" placeholder="请选择承做人" clearable filterable @change="handleQuery">
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
            <template v-if="showMoreCondition">
              <el-form-item label="承做部门" prop="ownerDeptId">
                <el-tree-select
                  v-model="queryParams.ownerDeptId"
                  :data="deptOptions"
                  :props="{ label: 'label', value: 'id' }"
                  placeholder="请选择承做部门"
                  check-strictly
                  filterable
                  clearable
                  @change="handleDeptChange"
                />
              </el-form-item>
              <el-form-item label="是否预收款" prop="isAdvanceReceipt">
                <el-select v-model="queryParams.isAdvanceReceipt" placeholder="请选择是否预收款" clearable filterable @change="handleQuery">
                  <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="收款日期" prop="receiptDate">
                <el-date-picker
                  v-model="dateRangeReceiptDate"
                  type="daterange"
                  :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  clearable
                  @change="handleQuery"
                />
              </el-form-item>
            </template>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mall:presaleOrderDetail:add']">新建</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="generalDetailList" border>
        <!-- <el-table-column type="index" width="55" label="序号" /> -->
        <el-table-column label="订单号" prop="orderNo" min-width="180" fixed="left">
          <template #default="scope">
            <el-tooltip :content="`订单id: ${scope.row.id}`" placement="top">
              {{ scope.row.orderNo }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="客户" prop="customerName" min-width="200" show-overflow-tooltip>
          <template #default="scope">
            <el-tooltip :content="`客户开票抬头: ${scope.row.invoiceTitle}`" placement="top">
              {{ scope.row.customerName }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="承做人（A角）" prop="ownerName" min-width="120" show-overflow-tooltip />
        <el-table-column label="承做部门" prop="ownerDeptName" min-width="120" show-overflow-tooltip />
        <el-table-column label="状态" prop="orderStatus" width="100">
          <template #default="scope">
            <dict-tag :options="mall_sale_order_status" :value="scope.row.orderStatus" />
          </template>
        </el-table-column>
        <!-- <el-table-column label="开票抬头" prop="invoiceTitle" min-width="300" show-overflow-tooltip>
          <template #default="scope">
            <div class="invoice-title-cell" :title="scope.row.invoiceTitle">
              {{ scope.row.invoiceTitle }}
            </div>
          </template>
        </el-table-column> -->
        <el-table-column label="销售用途" prop="purpose" width="100">
          <template #default="scope">
            <dict-tag :options="mall_sale_order_purpose" :value="scope.row.purpose" />
          </template>
        </el-table-column>
        <el-table-column label="需求卡数量" prop="totalQty" width="100">
          <template #default="scope">
            <span>{{ scope.row.totalQty || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="订单金额" prop="orderAmount" width="120">
          <template #header>
            <span>订单金额(元)</span>
            <el-tooltip content="订单金额=总明细金额-抹零金额，即应收金额" placement="top">
              <el-icon class="el-icon--right">
                <InfoFilled />
              </el-icon>
            </el-tooltip>
          </template>
          <template #default="scope">
            <span style="color: #f56c6c; font-weight: bold">{{ proxy.parseAmount(scope.row.orderAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="销售折扣" prop="discountPercentage" width="100">
          <template #header>
            销售折扣
            <el-tooltip content="销售折扣=应收金额 ÷ 面值合计" placement="top">
              <el-icon class="el-icon--right"><InfoFilled /></el-icon>
            </el-tooltip>
          </template>
          <template #default="scope">
            <span>{{ scope.row.discountPercentage }}%</span>
          </template>
        </el-table-column>
        <el-table-column label="面值合计" prop="totalValues" width="120">
          <template #default="scope">
            <span>{{ proxy.parseAmount(scope.row.totalValues) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否预收款" prop="isAdvanceReceipt" width="100">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isAdvanceReceipt" />
          </template>
        </el-table-column>
        <el-table-column label="收款日期" prop="receiptDate" width="180">
          <template #default="scope">
            <span v-if="scope.row.isAdvanceReceipt === 'Y'">{{ proxy.parseTime(scope.row.receiptDate, '{y}-{m}-{d}') }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="收款截图" prop="receiptFiles" width="100">
          <template #default="scope">
            <ImagePreview :src="scope.row.receiptFiles" width="50px" height="50px" isId v-if="scope.row.receiptFiles" />
            <span v-else>- -</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="250" />
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ proxy.parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新人" prop="updateByName" width="120" show-overflow-tooltip />
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="200">
          <template #default="scope">
            <div class="flex items-center gap-2">
              <el-tooltip content="编辑" placement="top">
                <el-button
                  v-if="['01', '02'].includes(scope.row.orderStatus)"
                  link
                  icon="Edit"
                  type="primary"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['mall:presaleOrder:edit']"
                />
              </el-tooltip>
              <el-tooltip content="查看" placement="top">
                <el-button link icon="View" type="primary" @click="handleView(scope.row)" v-hasPermi="['mall:presaleOrder:query']" />
              </el-tooltip>
              <!-- 提交,让具有审核权限的人进行审核 -->
              <el-tooltip content="提交订单，让具有审核权限的人进行审核" placement="top" v-if="['01', '02'].includes(scope.row.orderStatus)">
                <el-button link icon="Position" type="primary" @click="handleSubmitItem(scope.row)" v-hasPermi="['mall:presaleOrder:edit']" />
              </el-tooltip>
              <!-- 审核订单 -->
              <el-tooltip content="审核订单" placement="top" v-if="['11'].includes(scope.row.orderStatus)">
                <el-button link icon="Position" type="primary" @click="handleSubmitAudit(scope.row)" v-hasPermi="['mall:presaleOrder:audit']" />
              </el-tooltip>
              <!-- 出卡 -->
              <el-tooltip content="你准备好了出卡吗？" placement="top" v-if="['21'].includes(scope.row.orderStatus)">
                <el-button link icon="Position" type="primary" @click="handleChuCard(scope.row)" v-hasPermi="['mall:presaleOrder:active']" />
              </el-tooltip>
              <!-- 开卡 -->
              <el-tooltip content="如果你确定客户已经收到了卡片，就可以进行开卡操作" placement="top" v-if="['31'].includes(scope.row.orderStatus)">
                <el-button link icon="Position" type="primary" @click="handleOpenCard(scope.row)" v-hasPermi="['mall:presaleOrder:active']" />
              </el-tooltip>
              <!-- 删除 -->
              <el-tooltip content="删除订单" placement="top" v-if="['01'].includes(scope.row.orderStatus)">
                <el-button link icon="Delete" type="primary" @click="handleDelete(scope.row)" v-hasPermi="['mall:presaleOrder:remove']" />
              </el-tooltip>

              <!-- 作废订单 -->
              <el-tooltip content="将订单作废，订单内的卡券也将无法使用" placement="top" v-if="['21', '31'].includes(scope.row.orderStatus)">
                <el-button link icon="Delete" type="danger" @click="handleVoid(scope.row)" v-hasPermi="['mall:presaleOrder:void']" />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 添加销售订单 -->
    <CardModelDialog ref="cardModelDialogRef" @success="getList" />
    <!-- 审核 -->
    <AuditDialog ref="auditDialogRef" @submit="handleAuditSubmit" />
    <!-- 出卡弹窗 -->
    <ChuCardDialog :orderId="orderId" ref="chuCardDialogRef" @success="getList" />
    <!-- 开卡弹窗 -->
    <OpenCardDialog ref="openCardDialogRef" @success="getList" />

    <!-- 作废订单弹窗 -->
    <el-dialog v-model="voidDialog.visible" title="作废订单" width="500px" append-to-body>
      <div class="void-dialog-content">
        <div class="dialog-body-text">
          <p>你确定要作废这个订单吗？</p>
          <p>
            <span class="text-danger font-bold">订单号：{{ voidDialog.orderNo }}</span>
          </p>
          <p>作废后，订单状态将变为<span class="text-danger font-bold">已作废</span>，且订单内的卡券将无法使用。</p>
          <p>请输入作废原因：</p>
        </div>
        <el-input v-model="voidDialog.form.remark" type="textarea" placeholder="请输入作废原因" :rows="3" maxlength="200" show-word-limit />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="voidDialog.visible = false">取消</el-button>
          <el-button type="danger" @click="confirmVoid" :loading="voidDialog.loading">确定作废</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="modelList">
import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
import CardModelDialog from './Dialog.vue';
import AuditDialog from './components/AuditDialog.vue';
import OpenCardDialog from './components/OpenCardDialog.vue';
import ChuCardDialog from './components/ChuCardDialog.vue';
import { listPresaleOrder, delPresaleOrder, updatePresaleOrder, auditPresaleOrder, voidPresaleOrder } from '@/api/mall/presaleOrder/presaleOrder';
import { parseAmount } from '@/utils/ruoyi';
import { useDeptSelect } from '@/hooks/useBusiness/deptSelect';

const { deptOptions, loadDeptTree } = useDeptSelect();

const { proxy } = getCurrentInstance();
proxy.parseAmount = parseAmount; // 将金额格式化函数挂载到proxy上
const { loadUserList, userOptions } = useSysUserSelect();
const showSearch = ref(true);
const loading = ref(false);
let cardModelDialogRef = ref(null);
let openCardDialogRef = ref(null);
let chuCardDialogRef = ref(null);
const orderId = ref(null);
const { sys_yes_no, mall_sale_order_status, mall_sale_order_purpose } = toRefs(
  proxy?.useDict('sys_yes_no', 'mall_sale_order_status', 'mall_sale_order_purpose')
);
const total = ref(0);
const generalDetailList = ref([]);
const showMoreCondition = ref(false);
const dateRangeReceiptDate = ref(['', '']);

/** 作废订单弹窗 */
const voidDialog = reactive({
  visible: false,
  loading: false,
  orderNo: '',
  orderId: undefined,
  form: {
    remark: ''
  }
});

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  orderNo: undefined,
  customerId: undefined,
  orderStatus: undefined,
  ownerId: undefined,
  ownerDeptId: undefined,
  isAdvanceReceipt: undefined,
  openCardTime: undefined,
  purpose: undefined
});

// 搜索
const handleQuery = () => {
  loading.value = true;
  // 添加日期范围参数
  proxy?.addDateRange(queryParams.value, dateRangeReceiptDate.value, 'ReceiptDate');
  listPresaleOrder(queryParams.value).then((response) => {
    generalDetailList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
};

// 提交审核
const handleAuditSubmit = (row) => {
  loading.value = true;
  auditPresaleOrder(row)
    .then(() => {
      proxy.$modal.msgSuccess('提交审核成功');
      getList();
    })
    .finally(() => {
      loading.value = false;
    });
};

// 获取列表
const getList = async () => {
  handleQuery();
};

// 重置查询
const resetQuery = () => {
  dateRangeReceiptDate.value = ['', ''];
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    orderNo: undefined,
    customerId: undefined,
    orderStatus: undefined,
    ownerId: undefined,
    ownerDeptId: undefined,
    isAdvanceReceipt: undefined,
    openCardTime: undefined,
    purpose: undefined
  };

  handleQuery();
};

// 新增
const handleAdd = () => {
  cardModelDialogRef.value.dialogOpen(null, '500');
};

// 编辑
const handleUpdate = (row) => {
  cardModelDialogRef.value.dialogOpen(row.id, row.orderStatus);
};

// 提交审核
const auditDialogRef = ref(null);
const handleSubmitAudit = (row) => {
  auditDialogRef.value.open(row);
};

// 提交
const handleSubmitItem = (row) => {
  proxy.$modal
    .confirm('确定提交此销售订单？提交之后需要具有审核权限的同事进行审核。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      message: h('div', null, [
        h('p', null, '确定提交此销售订单？'),
        h(
          'p',
          { style: 'color: #909399; font-size: 13px; margin-top: 10px;' },
          '提交之后，需要等待相关同事审核。你如果知道是谁审核，请在企微中联系他吧。'
        )
      ])
    })
    .then(() => {
      loading.value = true;
      updatePresaleOrder({
        id: row.id,
        orderStatus: '11'
      })
        .then(() => {
          proxy.$modal.msgSuccess('提交成功');
          getList();
        })
        .finally(() => {
          loading.value = false;
        });
    });
};

// 查看详情
const handleView = (row) => {
  loading.value = true;
  cardModelDialogRef.value.dialogOpen(row.id, '00');
};

// 删除
const handleDelete = async (row) => {
  const ids = row.id || generalDetailList.value.map((item) => item.id);
  await proxy.$modal.confirm('是否确认删除销售订单编号为"' + (row.orderNo || ids) + '"的数据项？');
  loading.value = true;
  try {
    await delPresaleOrder(ids);
    proxy.$modal.msgSuccess('删除成功');
    await getList();
  } catch (error) {
    console.error('删除失败', error);
  } finally {
    loading.value = false;
  }
};

// 出卡
const handleChuCard = (row) => {
  orderId.value = row.id;
  proxy.$nextTick(() => {
    chuCardDialogRef.value?.initData();
  });
};

// 开卡提交
const handleOpenCard = (row) => {
  openCardDialogRef.value?.open(row);
};

/** 作废订单 */
const handleVoid = (row) => {
  voidDialog.orderNo = row.orderNo;
  voidDialog.orderId = row.id;
  voidDialog.form.remark = '';
  voidDialog.visible = true;
};

/** 确认作废 */
const confirmVoid = async () => {
  if (!voidDialog.form.remark) {
    proxy.$modal.msgError('请输入作废原因');
    return;
  }

  try {
    voidDialog.loading = true;
    const res = await voidPresaleOrder({ id: voidDialog.orderId, remark: voidDialog.form.remark });
    voidDialog.loading = false;

    if (res.code === 200) {
      proxy.$modal.msgSuccess('作废成功，该订单及内部的卡券已无法使用');
      voidDialog.visible = false;
      getList();
    }
  } catch (error) {
    voidDialog.loading = false;
    console.error('作废订单失败', error);
    proxy.$modal.msgError('作废订单失败');
  }
};

/** 处理部门选择变化 */
const handleDeptChange = (value) => {
  queryParams.value.ownerDeptId = value;
  handleQuery();
};

// 页面加载时获取列表数据
onMounted(async () => {
  await Promise.all([loadDeptTree(), loadUserList(), getList()]);
});
</script>

<style scoped>
.invoice-title-cell {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-break: break-all;
  line-height: 1.5;
}

.void-dialog-content {
  padding: 0 10px;

  p {
    margin-bottom: 15px;
    font-size: 14px;
  }
}

.dialog-body-text {
  margin-bottom: 20px;

  p {
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 1.5;
  }

  .text-danger {
    color: #f56c6c;
  }

  .font-bold {
    font-weight: bold;
  }
}
</style>
