<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="开始卡流水号" prop="cardNoStart">
              <el-input v-model="queryParams.cardNoStart" placeholder="请输入开始卡流水号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="结束卡流水号" prop="cardNoEnd">
              <el-input v-model="queryParams.cardNoEnd" placeholder="请输入结束卡流水号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="卡号" prop="cardCode">
              <el-input v-model="queryParams.cardCode" placeholder="请输入卡号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="批次号" prop="batchNumber">
              <el-input v-model="queryParams.batchNumber" placeholder="请输入批次号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="B端客户" prop="customerId" v-if="showMoreCondition">
              <customer-select v-model="queryParams.customerId" placeholder="请选择客户" clearable @change="handleQuery" />
            </el-form-item>
            <el-form-item label="绑定手机" prop="bindPhone" v-if="showMoreCondition">
              <el-input v-model="queryParams.bindPhone" placeholder="请输入绑定手机" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="销售状态" prop="saleStatus" v-if="showMoreCondition">
              <el-select v-model="queryParams.saleStatus" placeholder="请选择销售状态" clearable @change="handleQuery">
                <el-option v-for="dict in mall_card_sale_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="销售订单号">
              <pre-sale-card-select v-model="queryParams.saleOrderId" placeholder="请选择完整销售订单号" clearable @change="handleQuery" />
            </el-form-item>
            <el-form-item label="可用状态" prop="availableStatus" v-if="showMoreCondition">
              <el-select v-model="queryParams.availableStatus" placeholder="请选择可用状态" clearable @change="handleQuery">
                <el-option v-for="dict in mall_card_available_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="使用状态" prop="usageStatus" v-if="showMoreCondition">
              <el-select v-model="queryParams.usageStatus" placeholder="请选择使用状态" clearable @change="handleQuery">
                <el-option v-for="dict in mall_card_usage_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="销售时间" prop="saleTime" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeSaleTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD HH:mm:ss"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="开卡时间" prop="issueCardTime" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeIssueCardTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD HH:mm:ss"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="绑定时间" prop="bindTime" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeBindTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD HH:mm:ss"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="使用时间" prop="useTime" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeUseTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD HH:mm:ss"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="生效时间" prop="effectiveTime" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeEffectiveTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD HH:mm:ss"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="失效时间" prop="expireTime" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeExpireTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD HH:mm:ss"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              :disabled="selectedRows.length === 0"
              type="primary"
              plain
              icon="Calendar"
              @click="handleBatchAdjustExpireDate(selectedRows)"
              v-hasPermi="['mall:exchangeCard:adjust']"
            >
              批量延期
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              :disabled="selectedRows.length === 0 || !selectedRows.some((row) => canCardBeLost(row).canLost)"
              type="primary"
              plain
              icon="Lock"
              @click="handleBatchLossCard(selectedRows)"
              v-hasPermi="['mall:exchangeCard:lossCard']"
            >
              批量挂失
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              :disabled="
                selectedRows.length === 0 || !selectedRows.some((row) => row.availableStatus === '30' && ['10', '11', '21'].includes(row.usageStatus))
              "
              type="primary"
              plain
              icon="Unlock"
              @click="handleBatchUnlockCard(selectedRows)"
              v-hasPermi="['mall:exchangeCard:unlockCard']"
            >
              批量解除挂失
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              :disabled="selectedRows.length === 0"
              type="primary"
              plain
              icon="Delete"
              @click="handleBatchVoidCard(selectedRows)"
              v-hasPermi="['mall:exchangeCard:voidCard']"
            >
              批量作废
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button plain icon="Download" @click="handleExportCardInfo" v-hasPermi="['mall:exchangeCard:exportSimple']"> 导出卡信息 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button plain icon="Download" @click="handleExportCardSecret" v-hasPermi="['mall:exchangeCard:exportFull']"> 导出卡密 </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="handleQuery"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="cardNumberList" border @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column label="卡流水号" width="220" prop="cardNo" fixed="left" />
        <el-table-column label="批次号" width="160" prop="batchNumber" />
        <el-table-column label="卡号" width="150" prop="cardCode" />
        <el-table-column label="面值（元）" width="100">
          <template #default="scope">
            {{ centToYuan(scope.row.batch.value) }}
          </template>
        </el-table-column>
        <el-table-column label="销售状态" prop="saleStatus">
          <template #default="scope">
            <dict-tag :options="mall_card_sale_status" :value="scope.row.saleStatus" />
          </template>
        </el-table-column>
        <el-table-column label="可用状态" prop="availableStatus">
          <template #default="scope">
            <dict-tag :options="mall_card_available_status" :value="scope.row.availableStatus" />
          </template>
        </el-table-column>
        <el-table-column label="使用状态" prop="usageStatus">
          <template #default="scope">
            <dict-tag :options="mall_card_usage_status" :value="scope.row.usageStatus" />
          </template>
        </el-table-column>
        <el-table-column label="卡次" width="100">
          <template #default="scope">
            {{ scope.row.batch.times }}
          </template>
        </el-table-column>
        <el-table-column label="剩余次数" prop="avaNum" />
        <el-table-column label="已用次数" prop="usedNum" />
        <el-table-column label="销售订单号" prop="saleOrderNo" width="160px" show-overflow-tooltip />
        <el-table-column label="销售时间" prop="saleTime" width="180">
          <template #default="scope">
            <span>{{ proxy.parseTime(scope.row.saleTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="开卡时间" prop="issueCardTime" width="180">
          <template #default="scope">
            <span>{{ proxy.parseTime(scope.row.issueCardTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="使用状态变化时间" prop="usageStatusUpdateTime" width="180">
          <template #default="scope">
            <span>{{ proxy.parseTime(scope.row.usageStatusUpdateTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="B端客户" prop="buyerName" />
        <el-table-column label="C端客户手机" prop="bindPhone" width="150px" />
        <el-table-column label="绑定时间" prop="bindTime" width="180">
          <template #default="scope">
            <span>{{ proxy.parseTime(scope.row.bindTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="使用时间" prop="useTime" width="180">
          <template #default="scope">
            <span>{{ proxy.parseTime(scope.row.useTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="250" show-overflow-tooltip />
        <el-table-column label="生效日期" prop="effectiveTime" width="180">
          <template #default="scope">
            <span>{{ proxy.parseTime(scope.row.effectiveTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="失效日期" prop="expireTime" width="180">
          <template #default="scope">
            <div class="flex items-center relative group">
              <span>{{ proxy.parseTime(scope.row.expireTime) }}</span>
              <el-button
                v-if="scope.row.availableStatus === '20' && scope.row.usageStatus !== '91' && scope.row.usageStatus !== '92'"
                class="opacity-0 group-hover:opacity-100 ml-2 absolute right-0"
                link
                type="primary"
                icon="Edit"
                @click.stop="handleSingleAdjustExpireDate(scope.row)"
                v-hasPermi="['mall:exchangeCard:adjust']"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ proxy.parseTime(scope.row.updateTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" class-name="small-padding fixed-width" width="180">
          <template #default="scope">
            <div class="flex items-center gap-2">
              <el-tooltip content="查看" placement="top">
                <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['mall:exchangeCard:query']" />
              </el-tooltip>

              <!-- 可用状态为正常的卡显示延期、挂失和作废按钮 -->
              <template v-if="scope.row.availableStatus === '20' && scope.row.usageStatus !== '91' && scope.row.usageStatus !== '92'">
                <el-tooltip content="延期" placement="top">
                  <el-button
                    link
                    type="primary"
                    icon="Calendar"
                    @click="handleSingleAdjustExpireDate(scope.row)"
                    v-hasPermi="['mall:exchangeCard:adjust']"
                  />
                </el-tooltip>

                <el-tooltip content="挂失" placement="top" v-if="canCardBeLost(scope.row).canLost">
                  <el-button link type="primary" icon="Lock" @click="handleSingleLossCard(scope.row)" v-hasPermi="['mall:exchangeCard:lossCard']" />
                </el-tooltip>

                <el-tooltip content="作废" placement="top" v-if="['10', '11', '21'].includes(scope.row.usageStatus)">
                  <el-button link type="danger" icon="Delete" @click="handleSingleVoidCard(scope.row)" v-hasPermi="['mall:exchangeCard:voidCard']" />
                </el-tooltip>
              </template>

              <!-- 挂失状态的卡显示解除挂失按钮 -->
              <template v-else-if="scope.row.availableStatus === '30' && ['10', '11', '21'].includes(scope.row.usageStatus)">
                <el-tooltip content="解除挂失" placement="top">
                  <el-button
                    link
                    type="success"
                    icon="Unlock"
                    @click="handleSingleUnlockCard(scope.row)"
                    v-hasPermi="['mall:exchangeCard:unlockCard']"
                  />
                </el-tooltip>
              </template>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>

    <!-- 导出卡信息确认对话框 -->
    <el-dialog v-model="exportCardInfoDialogVisible" title="导出卡信息" width="500px" append-to-body>
      <div class="dialog-body-text">确定要导出不含卡密的卡信息列表吗？</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="exportCardInfoDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmExportCardInfo">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导出卡密确认对话框 -->
    <el-dialog v-model="exportCardSecretDialogVisible" title="导出卡密" width="500px" append-to-body>
      <div class="dialog-body-text">确定要导出包含卡密的卡列表吗？此操作可能存在权益资产安全风险，请确认您有相应的业务权限。</div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="exportCardSecretDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmExportCardSecret">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 卡流水号详情对话框 -->
    <el-dialog v-model="viewDialogVisible" title="卡流水号详情" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="卡流水号">{{ cardDetail.cardNo }}</el-descriptions-item>
        <el-descriptions-item label="卡号">{{ cardDetail.cardCode }}</el-descriptions-item>
        <el-descriptions-item label="批次号">{{ cardDetail.batchNumber }}</el-descriptions-item>
        <el-descriptions-item label="销售状态">
          <dict-tag :options="mall_card_sale_status" :value="cardDetail.saleStatus" />
        </el-descriptions-item>
        <el-descriptions-item label="可用状态">
          <dict-tag :options="mall_card_available_status" :value="cardDetail.availableStatus" />
        </el-descriptions-item>
        <el-descriptions-item label="使用状态">
          <dict-tag :options="mall_card_usage_status" :value="cardDetail.usageStatus" />
        </el-descriptions-item>
        <el-descriptions-item label="剩余次数">{{ cardDetail.avaNum }}</el-descriptions-item>
        <el-descriptions-item label="已用次数">{{ cardDetail.usedNum }}</el-descriptions-item>
        <el-descriptions-item label="销售订单">{{ cardDetail.saleOrderId }}</el-descriptions-item>
        <el-descriptions-item label="销售时间">{{ proxy.parseTime(cardDetail.saleTime) }}</el-descriptions-item>
        <el-descriptions-item label="开卡时间">{{ proxy.parseTime(cardDetail.issueCardTime) }}</el-descriptions-item>
        <el-descriptions-item label="使用状态变化时间">{{ proxy.parseTime(cardDetail.usageStatusUpdateTime) }}</el-descriptions-item>
        <el-descriptions-item label="购卡客户">{{ cardDetail.buyerName }}</el-descriptions-item>
        <el-descriptions-item label="C端客户手机">{{ cardDetail.bindPhone }}</el-descriptions-item>
        <el-descriptions-item label="绑定时间">{{ proxy.parseTime(cardDetail.bindTime) }}</el-descriptions-item>
        <el-descriptions-item label="使用时间">{{ proxy.parseTime(cardDetail.useTime) }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ cardDetail.remark }}</el-descriptions-item>
        <el-descriptions-item label="生效日期">{{ proxy.parseTime(cardDetail.effectiveTime) }}</el-descriptions-item>
        <el-descriptions-item label="失效日期">{{ proxy.parseTime(cardDetail.expireTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ proxy.parseTime(cardDetail.updateTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新人">{{ cardDetail.updateBy }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 单个调整失效日期对话框 -->
    <el-dialog v-model="singleAdjustExpireDateDialogVisible" title="延期" width="500px" append-to-body>
      <div class="dialog-body-text">请设置新的失效日期，调整后将覆盖原有失效日期。</div>
      <el-form>
        <el-form-item label="卡流水号">
          <span>{{ currentCard.cardNo }}</span>
        </el-form-item>
        <el-form-item label="当前失效日期">
          <span>{{ proxy.parseTime(currentCard.expireTime) }}</span>
        </el-form-item>
        <el-form-item label="新失效日期" required>
          <el-date-picker
            v-model="singleNewExpireDate"
            type="datetime"
            placeholder="选择日期时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabledDate="disabledDate"
            :default-time="new Date(2000, 1, 1, 23, 59, 59)"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="日期变化" v-if="singleDateChangeDays !== 0">
          <span :class="singleDateChangeDays > 0 ? 'text-success' : 'text-danger'">
            {{ singleDateChangeDays > 0 ? '延后' : '提前' }} <strong>{{ Math.abs(singleDateChangeDays) }}</strong> 天
          </span>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="singleOperationRemark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelSingleAdjustExpireDate">取消</el-button>
          <el-button type="primary" @click="confirmSingleAdjustExpireDate">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量调整失效日期对话框 -->
    <el-dialog v-model="batchAdjustExpireDateDialogVisible" title="批量延期" width="500px" append-to-body>
      <div class="dialog-body-text">请设置新的失效日期，调整后将覆盖原有失效日期。</div>
      <div class="mb-3">
        已选择 <span class="text-primary font-bold">{{ operatingRows.length }}</span> 条记录
      </div>
      <el-form>
        <el-form-item label="新失效日期" required>
          <el-date-picker
            v-model="batchNewExpireDate"
            type="datetime"
            placeholder="选择日期时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabledDate="disabledDate"
            :default-time="new Date(2000, 1, 1, 23, 59, 59)"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="batchOperationRemark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelBatchAdjustExpireDate">取消</el-button>
          <el-button type="primary" @click="confirmBatchAdjustExpireDate">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 挂失确认对话框 -->
    <el-dialog v-model="lossCardDialogVisible" :title="lossCardDialogTitle" width="500px" append-to-body>
      <div class="dialog-body-text">挂失后，相应的卡流水号将不可使用。</div>
      <div v-if="!currentCard.id" class="mb-3">
        已选择 <span class="text-primary font-bold">{{ operatingRows.length }}</span> 条记录
      </div>
      <el-form ref="lossCardFormRef" :model="lossCardForm" :rules="lossCardRules">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="lossCardForm.remark" type="textarea" placeholder="请输入挂失原因" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelLossCard">取消</el-button>
          <el-button type="primary" @click="confirmLossCard">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 解除挂失确认对话框 -->
    <el-dialog v-model="unlockCardDialogVisible" :title="unlockCardDialogTitle" width="500px" append-to-body>
      <div class="dialog-body-text">解除挂失后，相应的卡流水号将恢复可用状态。</div>
      <div v-if="!currentCard.id" class="mb-3">
        已选择 <span class="text-primary font-bold">{{ unlockRows.length }}</span> 条记录
      </div>
      <el-form ref="unlockCardFormRef" :model="unlockCardForm" :rules="unlockCardRules">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="unlockCardForm.remark" type="textarea" placeholder="请输入解除挂失原因" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelUnlockCard">取消</el-button>
          <el-button type="primary" @click="confirmUnlockCard">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 作废确认对话框 -->
    <el-dialog v-model="voidCardDialogVisible" :title="voidCardDialogTitle" width="500px" append-to-body>
      <div class="dialog-body-text">作废后，相应的卡流水号将不可使用，且不可恢复，请谨慎操作。</div>
      <div v-if="!currentCard.id" class="mb-3">
        已选择 <span class="text-primary font-bold">{{ voidRows.length }}</span> 条记录
      </div>
      <el-form>
        <el-form-item label="备注" required>
          <el-input v-model="voidCardRemark" type="textarea" placeholder="请输入作废原因" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelVoidCard">取消</el-button>
          <el-button type="danger" @click="confirmVoidCard">确定作废</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CardNumber">
import { getCurrentInstance, ref, toRefs, onMounted, watch } from 'vue';
import {
  listCardNumber,
  adjustExpireDateCardNumbers,
  getCardDetail,
  lossCardNumbers,
  unlockCardNumbers,
  voidCardNumbers
} from '@/api/mall/card/number';
import { useRoute } from 'vue-router';
import { centToYuan } from '@/utils/moneyUtils';

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const showSearch = ref(true);
const loading = ref(false);
const showMoreCondition = ref(false);
const { mall_card_sale_status, mall_card_available_status, mall_card_usage_status } = toRefs(
  proxy?.useDict('mall_card_sale_status', 'mall_card_available_status', 'mall_card_usage_status')
);
const total = ref(0);
const cardNumberList = ref([]);

// 添加日期范围变量
const dateRangeSaleTime = ref(['', '']);
const dateRangeIssueCardTime = ref(['', '']);
const dateRangeBindTime = ref(['', '']);
const dateRangeUseTime = ref(['', '']);
const dateRangeEffectiveTime = ref(['', '']);
const dateRangeExpireTime = ref(['', '']);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  customerId: undefined,
  cardNoStart: undefined,
  cardNoEnd: undefined,
  cardCode: undefined,
  batchNumber: undefined,
  bindPhone: undefined,
  saleStatus: undefined,
  availableStatus: undefined,
  usageStatus: undefined,
  saleTime: undefined,
  saleOrderId: undefined,
  issueCardTime: undefined,
  bindTime: undefined,
  useTime: undefined,
  effectiveTime: undefined,
  expireTime: undefined
});

// 对话框显示控制
const exportCardInfoDialogVisible = ref(false);
const exportCardSecretDialogVisible = ref(false);

// 查看详情相关数据
const viewDialogVisible = ref(false);
const cardDetail = ref({});

// 单个调整失效日期相关数据
const singleAdjustExpireDateDialogVisible = ref(false);
const singleNewExpireDate = ref('');
const singleOperationRemark = ref('');
const currentCard = ref({});
const singleDateChangeDays = ref(0); // 新增：单个调整的日期变化天数

// 批量调整失效日期相关数据
const batchAdjustExpireDateDialogVisible = ref(false);
const batchNewExpireDate = ref('');
const batchOperationRemark = ref('');
const operatingRows = ref([]);

// 挂失相关数据
const lossCardDialogVisible = ref(false);
const lossCardDialogTitle = ref('');
const lossCardForm = ref({
  remark: ''
});
const lossCardRules = {
  remark: [
    { required: true, message: '请输入挂失原因', trigger: 'blur' }
  ]
};

// 解除挂失相关数据
const unlockCardDialogVisible = ref(false);
const unlockCardDialogTitle = ref('');
const unlockCardForm = ref({
  remark: ''
});
const unlockCardRules = {
  remark: [
    { required: true, message: '请输入解除挂失原因', trigger: 'blur' }
  ]
};
const unlockRows = ref([]);

// 作废相关数据
const voidCardDialogVisible = ref(false);
const voidCardDialogTitle = ref('');
const voidCardRemark = ref('');
const voidRows = ref([]);

// 禁用过去的日期
const disabledDate = (time) => {
  return time.getTime() < Date.now() - 8.64e7; // 禁用今天之前的日期
};

/** 计算日期差异天数 */
const calculateDateDifference = (newDate, oldDate) => {
  if (!newDate || !oldDate) return 0;

  const date1 = new Date(newDate);
  const date2 = new Date(oldDate);

  // 计算时间差（毫秒）
  const diffTime = date1.getTime() - date2.getTime();

  // 转换为天数（向下取整）
  return Math.floor(diffTime / (1000 * 60 * 60 * 24));
};

/** 监听单个调整的日期变化 */
watch(
  singleNewExpireDate,
  (val) => {
    if (val && currentCard.value && currentCard.value.expireTime) {
      // 确保正确计算日期差异
      singleDateChangeDays.value = calculateDateDifference(val, currentCard.value.expireTime);
    } else {
      singleDateChangeDays.value = 0;
    }
  },
  { immediate: true }
);

// 添加一个公共函数用于检查卡是否可以挂失
const canCardBeLost = (card) => {
  // 必须是可用状态为正常的卡
  if (card.availableStatus !== '20') {
    return { canLost: false, message: '只能挂失可用状态为正常的卡' };
  }

  // 不能是已作废或已使用的卡
  if (card.usageStatus === '91' || card.usageStatus === '92') {
    return { canLost: false, message: '已作废或已使用的卡不能挂失' };
  }

  // 已售且待使用/使用中的兑换码不能挂失
  if (card.saleStatus === '30' && (card.usageStatus === '11' || card.usageStatus === '21')) {
    return { canLost: false, message: '已售且待使用/使用中的兑换码不能挂失' };
  }

  return { canLost: true, message: '' };
};

// 搜索
const handleQuery = async () => {
  loading.value = true;
  try {
    // 添加日期范围参数处理
    proxy?.addDateRange(queryParams.value, dateRangeSaleTime.value, 'SaleTime');
    proxy?.addDateRange(queryParams.value, dateRangeIssueCardTime.value, 'IssueCardTime');
    proxy?.addDateRange(queryParams.value, dateRangeBindTime.value, 'BindTime');
    proxy?.addDateRange(queryParams.value, dateRangeUseTime.value, 'UseTime');
    proxy?.addDateRange(queryParams.value, dateRangeEffectiveTime.value, 'EffectiveTime');
    proxy?.addDateRange(queryParams.value, dateRangeExpireTime.value, 'ExpireTime');

    const _queryParams = { ...queryParams.value };

    // 流水号
    if (_queryParams.cardNoStart) {
      _queryParams.params['beginCardNo'] = queryParams.value.cardNoStart.trim();
      delete _queryParams.cardNoStart;
    }
    if (_queryParams.cardNoEnd) {
      _queryParams.params['endCardNo'] = queryParams.value.cardNoEnd.trim();
      delete _queryParams.cardNoEnd;
    }

    const res = await listCardNumber({
      ..._queryParams
    });
    if (res.code === 200) {
      cardNumberList.value = res.rows;
      total.value = res.total;
    }
  } catch (error) {
    proxy.$modal.msgError('获取卡流水号列表失败');
  } finally {
    loading.value = false;
  }
};

// 重置查询
const resetQuery = () => {
  // 重置日期范围变量
  dateRangeSaleTime.value = ['', ''];
  dateRangeIssueCardTime.value = ['', ''];
  dateRangeBindTime.value = ['', ''];
  dateRangeUseTime.value = ['', ''];
  dateRangeEffectiveTime.value = ['', ''];
  dateRangeExpireTime.value = ['', ''];

  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    customerId: undefined,
    cardNoStart: undefined,
    cardNoEnd: undefined,
    cardCode: undefined,
    batchNumber: undefined,
    bindPhone: undefined,
    saleStatus: undefined,
    availableStatus: undefined,
    usageStatus: undefined,
    saleTime: undefined,
    issueCardTime: undefined,
    bindTime: undefined,
    useTime: undefined,
    effectiveTime: undefined,
    expireTime: undefined
  };
  handleQuery();
};

// 导出卡信息（不含卡密）
const handleExportCardInfo = () => {
  exportCardInfoDialogVisible.value = true;
};

// 确认导出卡信息
const confirmExportCardInfo = () => {
  proxy.download(
    '/mall/exchangeCard/exportSimple',
    {
      ...queryParams.value
    },
    `卡信息列表_${new Date().getTime()}.xlsx`
  );
  exportCardInfoDialogVisible.value = false;
};

// 导出卡密（包含卡密）
const handleExportCardSecret = () => {
  exportCardSecretDialogVisible.value = true;
};

// 确认导出卡密
const confirmExportCardSecret = () => {
  proxy.download(
    '/mall/exchangeCard/exportFull',
    {
      ...queryParams.value
    },
    `卡密列表_${new Date().getTime()}.xlsx`
  );
  exportCardSecretDialogVisible.value = false;
};

// 表格多选
const selectedRows = ref([]);
const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
};

// 查看详情
const handleView = async (row) => {
  try {
    const res = await getCardDetail(row.id);
    if (res.code === 200) {
      cardDetail.value = res.data;
      viewDialogVisible.value = true;
    }
  } catch (error) {
    proxy.$modal.msgError('获取卡流水号详情失败');
  }
};

// 单个调整失效日期
const handleSingleAdjustExpireDate = async (row) => {
  // 检查可用状态和使用状态
  if (row.availableStatus !== '20') {
    proxy.$modal.msgError('只能调整可用状态为正常的卡的失效日期');
    return;
  }

  if (row.usageStatus === '91' || row.usageStatus === '92') {
    proxy.$modal.msgError('已作废或已使用的卡不能调整失效日期');
    return;
  }

  // 获取最新的卡详情，以便获取最新的备注
  try {
    const res = await getCardDetail(row.id);
    if (res.code === 200) {
      currentCard.value = res.data;
      // 再次检查获取到的详情中的状态
      if (currentCard.value.availableStatus !== '20') {
        proxy.$modal.msgError('该卡当前可用状态不正常，无法调整失效日期');
        return;
      }

      if (currentCard.value.usageStatus === '91' || currentCard.value.usageStatus === '92') {
        proxy.$modal.msgError('该卡已作废或已使用，无法调整失效日期');
        return;
      }

      singleNewExpireDate.value = currentCard.value.expireTime || '';
      singleOperationRemark.value = currentCard.value.remark || '';
      singleAdjustExpireDateDialogVisible.value = true;
    }
  } catch (error) {
    proxy.$modal.msgError('获取卡详情失败');
  }
};

// 取消单个调整失效日期
const cancelSingleAdjustExpireDate = () => {
  singleAdjustExpireDateDialogVisible.value = false;
  singleNewExpireDate.value = '';
  singleOperationRemark.value = '';
  singleDateChangeDays.value = 0;
  currentCard.value = {};
};

// 确认单个调整失效日期
const confirmSingleAdjustExpireDate = async () => {
  if (!singleNewExpireDate.value) {
    proxy.$modal.msgError('请选择新的失效日期');
    return;
  }
  try {
    await adjustExpireDateCardNumbers({
      ids: [currentCard.value.id],
      expireTime: singleNewExpireDate.value,
      remark: singleOperationRemark.value
    });
    proxy.$modal.msgSuccess('调整失效日期成功');
    singleAdjustExpireDateDialogVisible.value = false;
    singleNewExpireDate.value = '';
    singleOperationRemark.value = '';
    singleDateChangeDays.value = 0;
    currentCard.value = {};
    handleQuery();
  } catch (error) {
    proxy.$modal.msgError('调整失效日期失败');
  }
};

// 批量调整失效日期
const handleBatchAdjustExpireDate = (selection) => {
  if (!selection || selection.length === 0) {
    proxy.$modal.msgError('请选择要调整失效日期的卡流水号');
    return;
  }

  // 过滤出可用状态为正常且非作废且非已使用的卡
  const validCards = selection.filter((item) => item.availableStatus === '20' && item.usageStatus !== '91' && item.usageStatus !== '92');

  if (validCards.length === 0) {
    proxy.$modal.msgError('所选卡流水号中没有符合调整失效日期条件的卡');
    return;
  }

  if (validCards.length < selection.length) {
    proxy.$modal.msgWarning(`已选择${selection.length}条记录，其中${validCards.length}条可调整失效日期(可用状态正常且非作废非已使用)`);
  }

  operatingRows.value = validCards;
  batchNewExpireDate.value = '';
  batchOperationRemark.value = '';
  batchAdjustExpireDateDialogVisible.value = true;
};

// 取消批量调整失效日期
const cancelBatchAdjustExpireDate = () => {
  batchAdjustExpireDateDialogVisible.value = false;
  batchNewExpireDate.value = '';
  batchOperationRemark.value = '';
};

// 确认批量调整失效日期
const confirmBatchAdjustExpireDate = async () => {
  if (!batchNewExpireDate.value) {
    proxy.$modal.msgError('请选择新的失效日期');
    return;
  }

  try {
    await adjustExpireDateCardNumbers({
      ids: operatingRows.value.map((item) => item.id),
      expireTime: batchNewExpireDate.value,
      remark: batchOperationRemark.value
    });
    proxy.$modal.msgSuccess('批量调整失效日期成功');
    batchAdjustExpireDateDialogVisible.value = false;
    batchNewExpireDate.value = '';
    batchOperationRemark.value = '';
    handleQuery();
  } catch (error) {
    proxy.$modal.msgError('批量调整失效日期失败');
  }
};

// 单个挂失
const handleSingleLossCard = async (row) => {
  // 使用公共函数检查卡是否可以挂失
  const checkResult = canCardBeLost(row);
  if (!checkResult.canLost) {
    proxy.$modal.msgError(checkResult.message);
    return;
  }

  // 获取最新的卡详情，以便获取最新的备注
  try {
    const res = await getCardDetail(row.id);
    if (res.code === 200) {
      currentCard.value = res.data;

      // 再次检查获取到的详情
      const detailCheckResult = canCardBeLost(currentCard.value);
      if (!detailCheckResult.canLost) {
        proxy.$modal.msgError(detailCheckResult.message);
        return;
      }

      lossCardDialogTitle.value = `挂失卡流水号 ${row.cardNo}`;
      lossCardForm.value.remark = currentCard.value.remark || '';
      operatingRows.value = []; // 确保单个挂失时操作行为空
      lossCardDialogVisible.value = true;
    }
  } catch (error) {
    proxy.$modal.msgError('获取卡详情失败');
  }
};

// 取消单个挂失
const cancelLossCard = () => {
  lossCardDialogVisible.value = false;
  lossCardForm.value.remark = '';
  currentCard.value = {};
  if (lossCardFormRef.value) {
    lossCardFormRef.value.resetFields();
  }
};

// 确认挂失
const confirmLossCard = async () => {
  if (!lossCardFormRef.value) return;

  await lossCardFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const ids = currentCard.value?.id ? [currentCard.value.id] : operatingRows.value.map((item) => item.id);
        await lossCardNumbers({
          ids,
          remark: lossCardForm.value.remark
        });
        proxy.$modal.msgSuccess('挂失成功');
        lossCardDialogVisible.value = false;
        lossCardForm.value.remark = '';
        currentCard.value = {};
        operatingRows.value = [];
        handleQuery();
      } catch (error) {
        proxy.$modal.msgError('挂失失败');
      }
    }
  });
};

// 批量挂失
const handleBatchLossCard = (selection) => {
  if (!selection || selection.length === 0) {
    proxy.$modal.msgError('请选择要挂失的卡流水号');
    return;
  }

  // 使用公共函数过滤出可以挂失的卡
  const validCards = selection.filter((item) => canCardBeLost(item).canLost);

  if (validCards.length === 0) {
    proxy.$modal.msgError('所选卡流水号中没有符合挂失条件的卡');
    return;
  }

  if (validCards.length < selection.length) {
    proxy.$modal.msgWarning(`已选择${selection.length}条记录，其中${validCards.length}条可挂失(可用状态正常且非作废非已使用，且非已售待使用/使用中)`);
  }

  operatingRows.value = validCards;
  currentCard.value = {};
  lossCardDialogTitle.value = '批量挂失';
  lossCardForm.value.remark = '';
  lossCardDialogVisible.value = true;
};

// 批量解除挂失
const handleBatchUnlockCard = (selection) => {
  if (!selection || selection.length === 0) {
    proxy.$modal.msgError('请选择要解除挂失的卡流水号');
    return;
  }

  // 过滤出挂失状态且使用状态为10、11、21的卡
  const validCards = selection.filter((item) => item.availableStatus === '30' && ['10', '11', '21'].includes(item.usageStatus));

  if (validCards.length === 0) {
    proxy.$modal.msgError('所选卡流水号中没有符合解除挂失条件的卡');
    return;
  }

  if (validCards.length < selection.length) {
    proxy.$modal.msgWarning(`已选择${selection.length}条记录，其中${validCards.length}条可解除挂失(挂失状态且使用状态为待激活/待使用/使用中)`);
  }

  unlockRows.value = validCards;
  currentCard.value = {};
  unlockCardDialogTitle.value = '批量解除挂失';
  unlockCardForm.value.remark = '';
  unlockCardDialogVisible.value = true;
};

// 单个解除挂失
const handleSingleUnlockCard = async (row) => {
  // 检查可用状态和使用状态
  if (row.availableStatus !== '30') {
    proxy.$modal.msgError('只能解除挂失状态的卡');
    return;
  }

  if (!['10', '11', '21'].includes(row.usageStatus)) {
    proxy.$modal.msgError('只有使用状态为待激活/待使用/使用中的卡才能解除挂失');
    return;
  }

  try {
    const res = await getCardDetail(row.id);
    if (res.code === 200) {
      const cardData = res.data;
      // 再次检查获取到的详情中的可用状态和使用状态
      if (cardData.availableStatus !== '30') {
        proxy.$modal.msgError('该卡当前不是挂失状态，无法解除挂失');
        return;
      }

      if (!['10', '11', '21'].includes(cardData.usageStatus)) {
        proxy.$modal.msgError('该卡当前使用状态不符合解除挂失条件');
        return;
      }

      currentCard.value = cardData;
      unlockRows.value = [cardData];
      unlockCardDialogTitle.value = `解除挂失卡流水号 ${row.cardNo}`;
      unlockCardForm.value.remark = cardData.remark || '';
      unlockCardDialogVisible.value = true;
    }
  } catch (error) {
    proxy.$modal.msgError('获取卡详情失败');
  }
};

// 取消解除挂失
const cancelUnlockCard = () => {
  unlockCardDialogVisible.value = false;
  unlockCardForm.value.remark = '';
  unlockRows.value = [];
  currentCard.value = {};
  if (unlockCardFormRef.value) {
    unlockCardFormRef.value.resetFields();
  }
};

// 确认解除挂失
const confirmUnlockCard = async () => {
  if (!unlockCardFormRef.value) return;

  await unlockCardFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await unlockCardNumbers({
          ids: unlockRows.value.map((item) => item.id),
          remark: unlockCardForm.value.remark
        });
        proxy.$modal.msgSuccess('解除挂失成功');
        unlockCardDialogVisible.value = false;
        unlockCardForm.value.remark = '';
        unlockRows.value = [];
        handleQuery();
      } catch (error) {
        proxy.$modal.msgError('解除挂失失败');
      }
    }
  });
};

// 单个作废
const handleSingleVoidCard = async (row) => {
  // 检查可用状态
  if (row.availableStatus !== '20') {
    proxy.$modal.msgError('只能作废可用状态为正常的卡');
    return;
  }

  // 检查使用状态
  if (!['10', '11', '21'].includes(row.usageStatus)) {
    proxy.$modal.msgError('只有使用状态为10、11、21的卡才能作废');
    return;
  }

  // 获取最新的卡详情，以便获取最新的备注
  try {
    const res = await getCardDetail(row.id);
    if (res.code === 200) {
      currentCard.value = res.data;
      // 再次检查获取到的详情中的可用状态和使用状态
      if (currentCard.value.availableStatus !== '20') {
        proxy.$modal.msgError('该卡当前可用状态不正常，无法作废');
        return;
      }
      if (!['10', '11', '21'].includes(currentCard.value.usageStatus)) {
        proxy.$modal.msgError('该卡当前使用状态不符合作废条件');
        return;
      }

      voidRows.value = [currentCard.value];
      voidCardDialogTitle.value = `作废卡流水号 ${row.cardNo}`;
      voidCardRemark.value = '';
      voidCardDialogVisible.value = true;
    }
  } catch (error) {
    proxy.$modal.msgError('获取卡详情失败');
  }
};

// 取消单个作废
const cancelVoidCard = () => {
  voidCardDialogVisible.value = false;
  voidCardRemark.value = '';
  voidRows.value = [];
  currentCard.value = {};
};

// 确认作废
const confirmVoidCard = async () => {
  if (!voidCardRemark.value) {
    proxy.$modal.msgError('请输入作废原因');
    return;
  }

  try {
    await voidCardNumbers({
      ids: voidRows.value.map((item) => item.id),
      remark: voidCardRemark.value
    });
    proxy.$modal.msgSuccess('作废成功');
    voidCardDialogVisible.value = false;
    voidCardRemark.value = '';
    voidRows.value = [];
    handleQuery();
  } catch (error) {
    proxy.$modal.msgError('作废失败');
  }
};

// 批量作废
const handleBatchVoidCard = (selection) => {
  if (!selection || selection.length === 0) {
    proxy.$modal.msgError('请选择要作废的卡流水号');
    return;
  }

  // 过滤出符合条件的卡：可用状态为正常且使用状态为10、11或21的卡
  const validCards = selection.filter((item) => item.availableStatus === '20' && ['10', '11', '21'].includes(item.usageStatus));

  if (validCards.length === 0) {
    proxy.$modal.msgError('所选卡流水号中没有符合作废条件的卡');
    return;
  }

  if (validCards.length < selection.length) {
    proxy.$modal.msgWarning(`已选择${selection.length}条记录，其中${validCards.length}条可作废(可用状态正常且使用状态符合条件)`);
  }

  voidRows.value = validCards;
  currentCard.value = {}; // 清空currentCard
  voidCardDialogTitle.value = '批量作废';
  voidCardRemark.value = '';
  voidCardDialogVisible.value = true;
};

/** 监听路由参数 */
const listenRoute = () => {
  const saleOrderId = route.query.saleOrderId;
  const batchNumber = route.query.batchNumber;
  if (saleOrderId) {
    queryParams.value.saleOrderId = saleOrderId;
    router.replace({ query: {} });
  }
  if (batchNumber) {
    queryParams.value.batchNumber = batchNumber;
    queryParams.value.saleStatus = '10'; // 未销售的dict key 为 "10"
    router.replace({ query: {} });
  } else {
    queryParams.value.saleStatus = ''; // 未销售的dict key 为 "10"
  }
};

onMounted(() => {
  listenRoute();
  handleQuery();
});

// 在 script setup 中添加以下内容
const lossCardFormRef = ref(null);
const unlockCardFormRef = ref(null);
</script>

<style scoped>
.dialog-body-text {
  margin-bottom: 20px;
  color: #666;
}
.text-primary {
  color: #409eff;
}
.text-success {
  color: #67c23a;
}
.text-danger {
  color: #f56c6c;
}
.text-gray-500 {
  color: #909399;
}
.font-bold {
  font-weight: bold;
}
.mb-3 {
  margin-bottom: 12px;
}
.mt-2 {
  margin-top: 8px;
}
</style>
