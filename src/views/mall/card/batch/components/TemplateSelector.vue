<template>
  <el-dialog title="第一步：选择卡模板" v-model="visible" width="70%" @close="handleClose">
    <div class="mb-[10px]">
      <el-form :inline="true" :model="queryParams" ref="queryFormRef">
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入名称" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable @change="handleQuery">
            <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="卡类型" prop="cardType">
          <el-select v-model="queryParams.cardType" placeholder="请选择卡类型" clearable @change="handleQuery">
            <el-option v-for="dict in mall_card_type" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table v-loading="loading" :data="templateList" border height="370px" @row-click="handleRowClick">
      <el-table-column type="selection" width="55" label="序号" />
      <el-table-column label="卡封面" prop="coverImageUrl" width="80">
        <template #default="scope">
          <el-image v-if="scope.row.coverImageUrl" :src="scope.row.coverImageUrl" style="width: 50px; height: 50px"></el-image>
          <span v-else>无图片</span>
        </template>
      </el-table-column>
      <el-table-column label="模板名称" prop="name" min-width="200" show-overflow-tooltip>
        <template #default="scope">
          <el-tooltip :content="scope.row.name" placement="top" :show-after="500" popper-class="template-name-tooltip">
            <div class="template-name">{{ scope.row.name }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="面值(元)" prop="value" width="100">
        <template #default="scope">
          {{ scope.row.value ? (scope.row.value / 100).toFixed(2) : '0.00' }}
        </template>
      </el-table-column>
      <el-table-column label="兑换次数" prop="times" width="100" show-overflow-tooltip />
      <el-table-column label="卡形态" prop="cardForm" width="100">
        <template #default="scope">
          <dict-tag :options="mall_card_form" :value="scope.row.cardForm" />
        </template>
      </el-table-column>
      <el-table-column label="卡类型" prop="cardType" width="100">
        <template #default="scope">
          <dict-tag :options="mall_card_type" :value="scope.row.cardType" />
        </template>
      </el-table-column>
      <el-table-column label="模板状态" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="交付方式" prop="deliveryType" width="100">
        <template #default="scope">
          <dict-tag :options="mall_delivery_type" :value="scope.row.deliveryType" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click.stop="handleSelect(scope.row)"
            :disabled="scope.row.status === '1'"
            :title="scope.row.status === '1' ? '已停用的卡模板不可选择' : ''"
          >
            选择
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';
import { listExchangeTemplate } from '@/api/mall/exchangeTemplate';
import DictTag from '@/components/DictTag/index.vue';

const { proxy } = getCurrentInstance();
const emit = defineEmits(['select', 'cancel']);

// 获取字典数据
const { sys_normal_disable, mall_card_type, mall_card_form, mall_delivery_type } = toRefs(
  proxy?.useDict('sys_normal_disable', 'mall_card_type', 'mall_card_form', 'mall_delivery_type')
);

// 状态变量
const loading = ref(false);
const visible = ref(false);
const total = ref(0);
const templateList = ref([]);
const queryFormRef = ref(null);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: undefined,
  status: '0',
  cardType: undefined
});

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

// 重置查询
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

// 获取模板列表
const getList = async () => {
  loading.value = true;
  try {
    const response = await listExchangeTemplate(queryParams);
    templateList.value = response.rows;
    total.value = response.total;
  } catch (error) {
    console.error('获取卡模板列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 行点击事件
const handleRowClick = (row) => {
  if (row.status === '1') {
    proxy.$modal.msgError('该卡模板已停用，无法选择');
    return;
  }
  handleSelect(row);
};

// 选择模板
const handleSelect = (row) => {
  if (row.status === '1') {
    proxy.$modal.msgError('该卡模板已停用，无法选择');
    return;
  }
  emit('select', row);
  visible.value = false;
};

// 关闭弹窗
const handleClose = () => {
  emit('cancel');
  visible.value = false;
};

// 打开选择器
const openSelector = () => {
  visible.value = true;
  getList();
};

// 暴露方法
defineExpose({
  openSelector
});
</script>

<style>
/* 全局样式，不使用 scoped，确保可以影响到 body 下的弹出层元素 */
.template-name-tooltip {
  width: 300px !important;
  word-break: break-all !important;
  line-height: 1.5 !important;
  padding: 8px 12px !important;
}
</style>

<style scoped>
.el-table :deep(.el-table__row) {
  cursor: pointer;
}

.el-table :deep(.disabled-row) {
  background-color: #f5f7fa;
  color: #909399;
  cursor: not-allowed;
}

.template-name {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  line-height: 1.4;
  max-height: 2.8em;
}
</style>
