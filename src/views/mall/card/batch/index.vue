<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="批次名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="批次名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="批次号" prop="batchNumber">
              <el-input v-model="queryParams.batchNumber" placeholder="请输入批次号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="批次状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择批次状态" clearable>
                <el-option v-for="dict in mall_card_make_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="销售范围" prop="saleScope">
              <el-select v-model="queryParams.saleScope" placeholder="请选择销售范围" clearable>
                <el-option v-for="dict in mall_card_sale_scope" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="卡类型" prop="cardType">
              <el-select v-model="queryParams.cardType" placeholder="请选择卡类型" clearable>
                <el-option v-for="dict in mall_card_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="有效期类型" v-if="showMoreCondition" prop="effectiveType">
              <el-select v-model="queryParams.effectiveType" placeholder="请选择有效期类型" clearable>
                <el-option v-for="dict in mall_effective_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleSelectTemplate" v-hasPermi="['mall:exchangeBatch:add']">新建</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="batchList" border>
        <el-table-column type="index" width="55" label="序号" />
        <el-table-column label="卡封面" prop="coverImageUrl">
          <template #default="scope">
            <image-preview :src="scope.row.coverImageUrl" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="批次名称（批次号）" prop="name" min-width="200" show-overflow-tooltip>
          <template #default="scope">
            <el-tooltip :content="scope.row.name" placement="top" :show-after="200" popper-class="batch-name-tooltip" :max-width="300">
              <div class="two-line-ellipsis">{{ scope.row.name }}</div>
            </el-tooltip>
            <el-tooltip content="快速查询处于可售状态的卡号，如看流水号" placement="top">
              <el-link type="primary" @click="queryCardPage(scope.row.batchNumber)">{{ scope.row.batchNumber }}</el-link>
            </el-tooltip>
          </template>
        </el-table-column>
        <!-- <el-table-column label="批次号" prop="batchNumber" min-width="160" show-overflow-tooltip>
          <template #default="scope">
            <el-tooltip content="快速查询处于可售状态的卡号，如看流水号" placement="top">
              <el-link type="primary" @click="queryCardPage(scope.row.batchNumber)">{{ scope.row.batchNumber }}</el-link>
            </el-tooltip>
          </template>
        </el-table-column> -->
        <el-table-column label="批次状态" prop="status" width="80">
          <template #default="scope">
            <dict-tag :options="mall_card_make_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="批次面值(元)" prop="value" width="100">
          <template #default="scope">
            {{ scope.row.value ? `¥${(scope.row.value / 100).toFixed(2)}` : '' }}
          </template>
        </el-table-column>
        <el-table-column label="制卡数量" prop="qty" width="100"></el-table-column>
        <el-table-column label="卡类型" prop="cardType" width="100">
          <template #default="scope">
            <dict-tag :options="mall_card_type" :value="scope.row.cardType" />
          </template>
        </el-table-column>
        <el-table-column label="销售范围" prop="saleScope" width="80">
          <template #default="scope">
            <dict-tag :options="mall_card_sale_scope" :value="scope.row.saleScope" />
          </template>
        </el-table-column>
        <el-table-column label="激活方式" prop="activationType" width="100">
          <template #default="scope">
            <dict-tag :options="mall_activation_type" :value="scope.row.activationType" />
          </template>
        </el-table-column>

        <el-table-column label="有效期类型" prop="effectiveType" width="100">
          <template #default="scope">
            <dict-tag :options="mall_effective_type" :value="scope.row.effectiveType" />
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ proxy.parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新人" prop="updateByName" width="150" />
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="200">
          <template #default="scope">
            <div class="flex items-center gap-2">
              <!-- 查看 -->
              <el-tooltip content="查看" placement="top">
                <el-button icon="View" type="primary" link @click="handleView(scope.row)" v-hasPermi="['mall:exchangeBatch:query']" />
              </el-tooltip>
              <!-- 编辑 -->
              <el-tooltip content="编辑" placement="top">
                <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mall:exchangeBatch:edit']" />
              </el-tooltip>
              <!-- 提交，准备生成卡 -->
              <el-tooltip content="提交，准备生成卡" placement="top" v-if="scope.row.status === '01'">
                <el-button icon="Position" type="primary" link @click="handleSubmit(scope.row)" v-hasPermi="['mall:exchangeBatch:edit']" />
              </el-tooltip>
              <!-- 准备制卡，批次将流转到制卡中 -->
              <el-tooltip content="准备制卡，将批次状态设置为制卡中" placement="top" v-if="scope.row.status === '21'">
                <el-button icon="Position" type="primary" link @click="handleMakeCard(scope.row)" v-hasPermi="['mall:exchangeBatch:edit']" />
              </el-tooltip>
              <!-- 完成制卡实体卡 -->
              <el-tooltip content="如果确认实体卡制作完成，将批次状态设置为制卡完成" placement="top" v-if="scope.row.status === '41'">
                <el-button icon="Position" type="primary" link v-hasPermi="['mall:exchangeBatch:edit']" @click="handleMakeCardFinish(scope.row)" />
              </el-tooltip>
              <!-- 删除 -->
              <el-tooltip content="删除" placement="top" v-if="scope.row.status === '01'">
                <el-button icon="Delete" type="primary" link @click="handleDelete(scope.row)" v-hasPermi="['mall:exchangeBatch:remove']" />
              </el-tooltip>
              <!-- 导出卡密 -->
              <el-tooltip content="导出卡密" placement="top" v-if="['41'].includes(scope.row.status)">
                <el-button icon="Download" type="primary" link @click="handleExportBatchCard(scope.row)" v-hasPermi="['mall:exchangeBatch:edit']" />
              </el-tooltip>
              <!-- 作废批次 -->
              <el-tooltip content="作废批次" placement="top" v-if="['21', '41', '51'].includes(scope.row.status)">
                <el-button icon="Delete" type="danger" link @click="handleVoidBatch(scope.row)" v-hasPermi="['mall:exchangeCard:voidCard']" />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 批次创建和编辑弹窗 -->
    <CardModelDialog ref="cardModelDialogRef" @submit="getList" />

    <!-- 创建批次时，选择模板 -->
    <TemplateSelector ref="templateSelectorRef" @select="handleTemplateSelect" @cancel="handleTemplateSelectorCancel" />

    <!-- 作废批次确认对话框 -->
    <el-dialog v-model="voidBatchDialogVisible" title="作废批次" width="500px" append-to-body>
      <div class="dialog-body-text">
        <p>确定要作废该批次下未售出的卡号吗？此操作将不可逆转，请谨慎操作。</p>
        <p>
          <span class="text-danger font-bold">批次号：{{ voidBatchData.batchNumber }}</span>
        </p>
        <p>批次名称：{{ voidBatchData.name }}</p>
      </div>
      <el-form>
        <el-form-item label="备注" required>
          <el-input v-model="voidBatchRemark" type="textarea" placeholder="请输入作废原因" :rows="3" maxlength="200" show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="voidBatchDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmVoidBatch">确定作废</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CardBatchList">
import { listExchangeBatch, delExchangeBatch, submitExchangeBatch } from '@/api/mall/exchangeBatch';
import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
import CardModelDialog from './Dialog.vue';
import TemplateSelector from './components/TemplateSelector.vue';
import { updateExchangeBatch } from '@/api/mall/exchangeBatch';
import { centToYuan } from '@/utils/moneyUtils';
import { voidCardByBatch } from '@/api/mall/card/number';

const { proxy } = getCurrentInstance();
const { loadUserList, userOptions } = useSysUserSelect();
const showSearch = ref(true);
const loading = ref(false);
let cardModelDialogRef = ref(null);
const showMoreCondition = ref(false);
let templateSelectorRef = ref(null);
const queryFormRef = ref(null);
const { mall_card_type, mall_activation_type, mall_effective_type, mall_card_sale_scope, mall_card_make_status } = toRefs(
  proxy?.useDict('mall_card_type', 'mall_activation_type', 'mall_effective_type', 'mall_card_sale_scope', 'mall_card_make_status')
);
const total = ref(0);
const batchList = ref([]);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  name: undefined,
  saleScope: undefined,
  cardType: undefined,
  activationType: undefined,
  effectiveType: undefined
});

// 作废批次相关变量
const voidBatchDialogVisible = ref(false);
const voidBatchRemark = ref('');
const voidBatchData = ref({});

// 搜索
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

// 获取列表
const getList = async () => {
  loading.value = true;
  try {
    const res = await listExchangeBatch(queryParams.value);
    if (res.code === 200) {
      batchList.value = res.rows;
      total.value = res.total;
    }
  } catch (error) {
    console.error('获取卡批次列表失败', error);
  } finally {
    loading.value = false;
  }
};

// 重置查询
const resetQuery = () => {
  queryFormRef.value.resetFields();
  handleQuery();
};

// 打开模板选择器
const handleSelectTemplate = () => {
  templateSelectorRef.value.openSelector();
};

// 模板选择完成
const handleTemplateSelect = (templateData) => {
  // 打开批次弹窗并传入模板数据
  cardModelDialogRef.value.dialogOpen(templateData.id, 'add');
};

// 模板选择取消
const handleTemplateSelectorCancel = () => {
  console.log('取消选择模板');
};

// 编辑
const handleUpdate = (row) => {
  cardModelDialogRef.value.dialogOpen(row.id, 'edit');
};

// 查看详情
const handleView = async (row) => {
  cardModelDialogRef.value.dialogOpen(row.id, 'view');
};

// 删除批次
const handleDelete = async (row) => {
  try {
    // 只允许删除状态为'01'（待提交）的批次
    if (row.status !== '01') {
      proxy.$modal.msgError('只能删除待提交状态的批次');
      return;
    }
    await proxy.$modal.confirm('是否确认删除批次号为「' + row.batchNumber + '」的数据项？');
    const res = await delExchangeBatch(row.id);
    if (res.code === 200) {
      proxy.$modal.msgSuccess('删除成功');
      getList();
    }
  } catch (error) {
    console.error('删除批次失败', error);
    proxy.$modal.msgError('删除失败');
  }
};

// 提交批次
const handleSubmit = async (row) => {
  try {
    await proxy.$modal.confirm(
      '确认要提交该批次吗？提交后系统将按生成面值为' +
        centToYuan(row.value) +
        '元的' +
        row.qty +
        '张卡，一旦提交，将无法修改，请确认后提交。此批次卡要具备销售条件，还要经历：制卡和完成制卡两步，请务必知晓。'
    );
    const res = await submitExchangeBatch(row.id);
    if (res.code === 200) {
      proxy.$modal.msgSuccess('提交成功,卡生成中~');
      getList();
    }
  } catch (error) {
    console.error('提交批次失败，请稍后再试！', error);
  }
};

// 制卡
const handleMakeCard = async (row) => {
  try {
    await proxy.$modal.confirm(
      '确认准备制作实体卡吗？提交后，你就可以导出此批次的卡密，交付给制卡商。注意：距离可销售还有一个重要的步骤：完成制卡。'
    );
    const res = await updateExchangeBatch({ status: '41', id: row.id });
    if (res.code === 200) {
      proxy.$modal.msgSuccess('已经进入制卡中，系统已为你导出了一份卡密～');
      getList();
    }

    // 导出卡密准备制卡
    proxy.download(
      '/mall/exchangeCard/exportFullByBatch',
      {
        batchNumber: row.batchNumber
      },
      `兑换卡_${row.batchNumber}_制卡列表_${new Date().getTime()}.xlsx`
    );
  } catch (error) {
    console.error('制卡失败', error);
  }
};

// 制卡完成
const handleMakeCardFinish = async (row) => {
  try {
    await proxy.$modal.confirm('确认要制卡完成吗？请务必确保制卡商已经将卡交付给你，否则会带来不确定的业务风险。');
    const res = await updateExchangeBatch({ status: '51', id: row.id });
    if (res.code === 200) {
      proxy.$modal.msgSuccess('制卡完成，你可以销售这些卡了～');
      getList();
    }
  } catch (error) {
    console.error('制卡完成失败，请重试！', error);
  }
};

/** 查询批次关联的卡号 */
const queryCardPage = (batchNumber) => {
  const routeUrl = `/mall/card/number?batchNumber=${batchNumber}`;
  window.open(routeUrl, '_blank');
};

/** 导出批次卡密 */
const handleExportBatchCard = async (row) => {
  try {
    await proxy.$modal.confirm(
      '确定要导出卡密吗？导出卡密主要是为了交付给制卡商制卡。请谨慎操作。并请跟进制卡进度，如果收到了制卡商的卡，请就是在系统做完成制卡确认。'
    );
    proxy.download(
      '/mall/exchangeCard/exportFullByBatch',
      {
        batchNumber: row.batchNumber
      },
      `兑换卡_${row.batchNumber}_制卡列表_${new Date().getTime()}.xlsx`
    );
  } catch (error) {
    console.error('导出批次卡密失败', error);
  }
};

/** 作废批次 */
const handleVoidBatch = (row) => {
  if (!['21', '41', '51'].includes(row.status)) {
    proxy.$modal.msgError('只有制卡中或制卡完成状态的批次才能作废');
    return;
  }

  voidBatchData.value = row;
  voidBatchRemark.value = '';
  voidBatchDialogVisible.value = true;
};

/** 确认作废批次 */
const confirmVoidBatch = async () => {
  if (!voidBatchRemark.value) {
    proxy.$modal.msgError('请输入作废原因');
    return;
  }

  try {
    await voidCardByBatch({
      batchNumber: voidBatchData.value.batchNumber,
      remark: voidBatchRemark.value
    });
    proxy.$modal.msgSuccess('批次作废成功');
    voidBatchDialogVisible.value = false;
    voidBatchRemark.value = '';
    voidBatchData.value = {};
    getList();
  } catch (error) {
    console.error('作废批次失败', error);
    proxy.$modal.msgError('作废批次失败');
  }
};

// 页面初始化时获取数据
onMounted(() => {
  getList();
});
</script>

<style scoped>
.two-line-ellipsis {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}
</style>

<style>
.batch-name-tooltip {
  max-width: 300px !important;
  white-space: normal !important;
  word-break: break-all;
  line-height: 1.5;
}
</style>
