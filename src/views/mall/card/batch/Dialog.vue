<template>
  <el-dialog
    :title="isView ? '查看批次详情' : type === 'add' ? '新增卡批次' : '编辑卡批次'"
    destroy-on-close
    v-model="visible"
    @close="handleClose"
    width="80%"
  >
    <el-row :gutter="10">
      <!-- 左侧锚点导航 -->
      <el-col :span="4">
        <el-anchor>
          <el-anchor-link href="#basic" title="基础信息" />
          <el-anchor-link href="#effect" title="有效期设置" />
          <el-anchor-link href="#exchange" title="兑换方案" />
        </el-anchor>
      </el-col>
      <el-col :span="20">
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" label-position="top" :disabled="isView">
          <div id="basic" class="form-section">
            <div class="section-title">
              <span class="title-text">基础信息</span>
            </div>
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="批次号" prop="batchNumber">
                  <el-input v-model="formData.batchNumber" placeholder="新建后生成" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="批次名称" prop="name">
                  <el-input v-model="formData.name" maxlength="50" show-word-limit clearable placeholder="请输入批次名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="制卡数量" prop="qty">
                  <el-input-number
                    v-model="formData.qty"
                    :min="1"
                    :max="10000"
                    :precision="0"
                    :controls="true"
                    placeholder="请输入制卡数量"
                    style="width: 100%"
                    :disabled="type != 'add' && formData.status != '01'"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="销售范围" prop="saleScope">
                  <dict-select
                    v-model="formData.saleScope"
                    dict-key="mall_card_sale_scope"
                    placeholder="请选择"
                    clearable
                    style="width: 100%"
                    :disabled="type != 'add' && formData.status != '01'"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="面值" prop="value">
                  <el-input v-model="formData.value" placeholder="请输入面值" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="卡类型" prop="cardType">
                  <el-select v-model="formData.cardType" placeholder="请选择卡类型" clearable style="width: 100%" disabled>
                    <el-option v-for="dict in mall_card_type" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="兑换次数" prop="times">
                  <el-input v-model="formData.times" placeholder="请输入兑换次数" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="卡形态" prop="cardForm">
                  <el-select v-model="formData.cardForm" placeholder="请选择" clearable style="width: 100%" disabled>
                    <el-option v-for="dict in mall_card_form" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="交付方式" prop="deliveryType">
                  <el-select v-model="formData.deliveryType" placeholder="请选择激活方式" disabled>
                    <el-option v-for="dict in mall_delivery_type" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="排序" prop="sort">
                  <el-input-number v-model="formData.sort" :min="1" :max="999" :precision="0" placeholder="请输入排序" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="发货周期" prop="shippingCycle">
                  <el-select
                    v-model="formData.shippingCycle"
                    placeholder="请选择"
                    clearable
                    style="width: 100%"
                    @change="handleShippingCycleChange"
                    :disabled="type != 'add' && formData.status != '01'"
                  >
                    <el-option v-for="dict in mall_shipping_cycle" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="formData.shippingCycle !== '1'">
                <el-form-item label="默认第几天发货" prop="cycleDay">
                  <el-input-number
                    v-model="formData.cycleDay"
                    :min="getMinDay"
                    :max="getMaxDay"
                    :precision="0"
                    placeholder="请输入天数"
                    style="width: 100%"
                    :disabled="formData.shippingCycle === '1' || !formData.shippingCycle || formData.status != '01'"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="批次封面" prop="coverImage">
                  <div v-if="templateData.id && !formData.coverImage" class="template-cover-preview">
                    <el-image v-if="templateData.coverImageUrl" :src="templateData.coverImageUrl" style="width: 100px; height: 100px" />
                    <div class="template-cover-tip">模板封面</div>
                  </div>
                  <imageUpload :limit="1" v-model="formData.coverImage" :disabled="isView" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="批次描述" prop="desc">
                  <el-input type="textarea" :rows="4" maxlength="200" show-word-limit v-model="formData.desc" placeholder="请输入批次描述" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="备注" prop="instruction">
                  <el-input type="textarea" :rows="4" maxlength="200" show-word-limit v-model="formData.remark" placeholder="请输入备注" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div id="effect" class="form-section">
            <div class="section-title">
              <span class="title-text">有效期设置</span>
            </div>
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="有效期类型" prop="effectiveType">
                  <el-select 
                    v-model="formData.effectiveType" 
                    placeholder="请选择有效期类型" 
                    clearable 
                    style="width: 100%"
                    :disabled="type != 'add' && formData.status != '01'">
                    <el-option v-for="dict in mall_effective_type" :key="dict.value" :label="dict.label"
                      :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="formData.effectiveType === '1'">
                <el-form-item label="激活方式" prop="activationType">
                  <el-select 
                    v-model="formData.activationType" 
                    placeholder="请选择激活方式" 
                    clearable 
                    style="width: 100%"
                    :disabled="type != 'add' && formData.status != '01'">
                    <el-option v-for="dict in mall_activation_type" :key="dict.value" :label="dict.label"
                      :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <template v-if="formData.effectiveType === '2'">
                <el-col :span="6">
                  <el-form-item label="起止日期" prop="expireDates">
                    <el-date-picker
                      v-model="formData.expireDates"
                      type="daterange"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      style="width: 100%"
                      :disabledDate="disabledDate"
                      :disabled="type != 'add' && formData.status != '01'"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="失效前多少天禁止绑定" prop="prohibitBindDays">
                    <el-input-number
                      v-model="formData.prohibitBindDays"
                      :min="0"
                      placeholder="请输入天数"
                      style="width: 100%"
                      :disabled="type != 'add' && formData.status != '01'"
                    />
                  </el-form-item>
                </el-col>
              </template>
              <template v-else>
                <el-col :span="6">
                  <el-form-item label="激活后n天内生效" prop="daysAfterActivation">
                    <el-input-number
                      v-model="formData.daysAfterActivation"
                      :min="0"
                      placeholder="0"
                      style="width: 100%"
                      :disabled="type != 'add' && formData.status != '01'"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="多少天内有效" prop="effectiveDays">
                    <el-input-number
                      v-model="formData.effectiveDays"
                      :min="1"
                      placeholder="激活后n天后失效"
                      style="width: 100%"
                      :disabled="type != 'add' && formData.status != '01'"
                    />
                  </el-form-item>
                </el-col>
              </template>
            </el-row>
            <!-- 可兑换日期区间已移除 -->
          </div>
          <div id="exchange" class="form-section">
            <div class="section-title">
              <span class="title-text">兑换方案</span>
            </div>
            <PlanTable ref="planTableRef" @change="handlePlanChange" :plans="formData.exchangePlans" :disabled="isView" :times="formData.times" />
          </div>
        </el-form>
      </el-col>
    </el-row>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="!isView">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, getCurrentInstance, computed } from 'vue';
import PlanTable from '../model/components/PlanTable.vue';
import { getExchangeTemplate } from '@/api/mall/exchangeTemplate';
import { addExchangeBatch, updateExchangeBatch, getExchangeBatch, updateExchangeCardInstance } from '@/api/mall/exchangeBatch';

const { proxy } = getCurrentInstance();
const emit = defineEmits(['submit', 'success']);
const instanceList = ref([]);

// 获取数据字典
const {
  mall_card_type,
  mall_card_form,
  mall_delivery_type,
  mall_shipping_cycle,
  mall_activation_type,
  mall_effective_type,
  mall_card_sale_scope,
  mall_exchange_type
} = toRefs(
  proxy?.useDict(
    'mall_card_type',
    'mall_card_form',
    'mall_delivery_type',
    'mall_shipping_cycle',
    'mall_activation_type',
    'mall_effective_type',
    'mall_card_sale_scope',
    'mall_exchange_type'
  )
);

// 禁用过去的日期
const disabledDate = (time) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0); // 设置为当天的0点
  return time.getTime() < today.getTime();
};

// 状态变量
const formRef = ref(null);
const planTableRef = ref(null);
const formData = ref({
  name: '',
  qty: 0,
  saleScope: '',
  cardType: '',
  cardForm: '',
  times: '',
  value: '',
  shippingCycle: '',
  cycleDay: 0,
  coverImage: '',
  coverImageUrl: '', // 封面图片URL
  desc: '',
  instruction: '',
  activationType: '1',
  effectiveType: '1',
  effectiveDate: '',
  expireDates: '',
  prohibitBindDays: '',
  daysAfterActivation: 0,
  effectiveDays: 0,
  startDate: '',
  endDate: '',
  exchangePlans: [], // 兑换方案列表
  deliveryType: '', // 支付方式
  cardMaskValue: '', // 卡模板面值
  cardMaskValue2: '', // 面值2
  expireDateSelect: '', // 截止日期选择
  templateId: null // 模板ID
});
const templateData = ref({}); // 存储模板数据
const isView = ref(false);
const visible = ref(false);
const warehouseList = ref([]);
const type = ref(''); // 保存当前操作类型：add、edit、view

// 根据周期类型获取最小天数
const getMinDay = computed(() => {
  return 1; // 所有类型最小值都是1
});

// 根据周期类型获取最大天数
const getMaxDay = computed(() => {
  if (formData.value.shippingCycle === '2') {
    return 7; // 按周发货，限制1-7天
  } else if (formData.value.shippingCycle === '3') {
    return 31; // 按月发货，限制1-31天
  }
  return 365; // 默认最大值
});

// 处理发货周期变更
const handleShippingCycleChange = (val) => {
  if (val === '') {
    // 按日期区间，清空天数
    formData.value.cycleDay = null;
  } else if (val === '2') {
    // 按周
    if (!formData.value.cycleDay || formData.value.cycleDay > 7) {
      formData.value.cycleDay = 1; // 默认周一
    }
  } else if (val === '3') {
    // 按月
    if (!formData.value.cycleDay || formData.value.cycleDay > 31) {
      formData.value.cycleDay = 1; // 默认每月1日
    }
  }
};

// 表单校验规则优化
const rules = {
  name: [
    { required: true, message: '请输入批次名称', trigger: 'blur' },
    { max: 50, message: '批次名称不能超过50个字符', trigger: 'blur' }
  ],
  qty: [
    { required: true, message: '请输入制卡数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 10000, message: '制卡数量必须为1-10000之间的正整数', trigger: 'blur' }
  ],
  saleScope: [{ required: true, message: '请选择销售范围', trigger: 'change' }],
  warehouseId: [{ required: true, message: '请选择入库库房', trigger: 'change' }],
  cardType: [{ required: true, message: '请选择卡类型', trigger: 'change' }],
  cardForm: [{ required: true, message: '请选择卡面形态', trigger: 'change' }],
  activationType: [{ required: true, message: '请选择激活方式', trigger: 'change' }],
  effectiveType: [{ required: true, message: '请选择有效期类型', trigger: 'change' }],
  // 新增必填校验规则
  sort: [{ type: 'number', min: 1, max: 999, message: '排序必须为1-999之间的整数', trigger: 'blur' }],
  remark: [
    { required: true, message: '请输入备注', trigger: 'blur' },
    { max: 200, message: '备注不能超过200个字符', trigger: 'blur' }
  ],
  desc: [{ max: 200, message: '批次描述不能超过200个字符', trigger: 'blur' }],
  times: [{ required: true, message: '请输入卡次', trigger: 'blur' }],
  // 有效期相关校验
  expireDates: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (formData.value.effectiveType === '2' && (!value || !Array.isArray(value) || value.length !== 2)) {
          callback(new Error('请选择有效期'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ],
  prohibitBindDays: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (formData.value.effectiveType === '2' && (value === undefined || value === null)) {
          callback(new Error('请输入失效前禁止绑定天数'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  effectiveDays: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (formData.value.effectiveType === '1' && (value === undefined || value === null || value === '')) {
          callback(new Error('请输入有效天数'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  daysAfterActivation: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (formData.value.effectiveType === '1' && (value === undefined || value === null || value === '')) {
          callback(new Error('请输入激活后天数'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  exchangePlans: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (formData.value.times > 0 && (!value || value.length === 0)) {
          callback(new Error('请至少添加一个兑换方案'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ]
};

// 处理兑换方案变更
const handlePlanChange = async (plans, editIndex) => {
  const instance = instanceList.value[editIndex];
  const { exchangePlans } = formData.value;
  const oldPlan = exchangePlans[editIndex];
  const newPlan = plans[editIndex];
  if (oldPlan && newPlan) {
    // 更新方案
    await updateExchangeCardInstance({
      id: instance.id,
      exchangePlanId: newPlan.id,
      instruction: instance.instruction,
      batchNumber: instance.batchNumber,
      name: newPlan.name,
      no: editIndex + 1
    });
  }
  formData.value.exchangePlans = plans;
};

// 打开弹窗
const dialogOpen = async (id, operationType) => {
  visible.value = true;
  type.value = operationType; // 保存操作类型

  const initData = {};

  if (operationType === 'add') {
    // 根据id查询模板详情，回显部分数据
    const { data, code } = await getExchangeTemplate(id);
    if (code === 200) {
      templateData.value = data;
      // 从模板数据初始化批次数据
      Object.assign(initData, {
        status: '01', // 新建时默认状态为01 待提交
        qty: data.qty,
        templateId: data.id,
        cardType: data.cardType,
        value: data.value,
        name: data.name,
        times: data.times,
        coverImage: data.coverImage, // 新建时清空封面
        activationType: '1', // 默认自动激活
        cardForm: data.cardForm, // 默认实体卡
        deliveryType: data.deliveryType, // 默认物流配送
        saleScope: data.saleScope, // 默认全部可售
        shippingCycle: data.shippingCycle, // 默认按月发货
        cycleDay: data.cycleDay, // 默认每月1号发货
        sort: 1, // 默认排序值
        exchangePlans: data.detail, // 清空兑换方案
        daysAfterActivation: 0, // 激活后天数
        effectiveDays: data.effectiveDays, // 有效期天数
        prohibitBindDays: 2 // 失效前禁止绑定天数
      });
    }
  } else {
    const { data, code } = await getExchangeBatch(id);
    if (code === 200) {
      // 编辑或查看时，设置查看模式
      isView.value = operationType === 'view';

      // 处理日期区间
      if (data.effectiveDate && data.expireDate) {
        data.expireDates = [data.effectiveDate, data.expireDate];
      }

      // 处理兑换方案数据
      if (data.instanceList && Array.isArray(data.instanceList)) {
        instanceList.value = data.instanceList;
        data.exchangePlans = data.instanceList.map((item) => {
          const { exchangePlan } = item;
          return {
            id: item.exchangePlanId,
            no: item.no,
            name: exchangePlan.name,
            type: exchangePlan.type,
            optional_qty: exchangePlan.optionalQty,
            code: exchangePlan.code,
            price: exchangePlan.price,
            desc: exchangePlan.desc,
            exchangeEndDate: exchangePlan.exchangeEndDate,
            exchangeStartDate: exchangePlan.exchangeStartDate,
            goodsCount: exchangePlan.detail.length
          };
        });
      }

      Object.assign(initData, data);
    }
  }

  // 重置表单
  formData.value = { ...formData.value, ...initData };

  // 处理面值显示
  if (formData.value.value && typeof formData.value.value === 'number') {
    // 面值显示为元
    formData.value.value = (formData.value.value / 100).toFixed(2);
  }
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
  isView.value = false;
  type.value = ''; // 重置操作类型
  formData.value = {
    name: '',
    qty: 0,
    saleScope: '',
    warehouseId: '',
    cardType: '',
    cardForm: '',
    times: '',
    value: '',
    shippingCycle: '',
    cycleDay: null,
    coverImage: '',
    coverImageUrl: '',
    desc: '',
    instruction: '',
    activationType: '1',
    effectiveType: '1',
    effectiveDate: '',
    expireDates: '',
    prohibitBindDays: '',
    daysAfterActivation: 0,
    effectiveDays: 0,
    startDate: '',
    endDate: '',
    exchangePlans: [],
    deliveryType: '',
    cardMaskValue: '',
    cardMaskValue2: '',
    expireDateSelect: '',
    templateId: null
  };
};

// 准备提交数据
const prepareSubmitData = () => {
  const submitData = { ...formData.value };

  // 处理金额转换（界面输入的是元，需要转为分存储）
  if (submitData.value && typeof submitData.value === 'number') {
    submitData.value = Math.round(submitData.value * 100);
  } else if (submitData.value && typeof submitData.value === 'string') {
    submitData.value = Math.round(parseFloat(submitData.value) * 100);
  }

  // 确保模板ID被传递到后端
  if (templateData.value.id) {
    submitData.templateId = templateData.value.id;
  }

  // 确保有效期相关字段已设置
  if (submitData.effectiveType === '2') {
    // 固定日期模式
    if (Array.isArray(submitData.expireDates) && submitData.expireDates.length === 2) {
      submitData.effectiveDate = submitData.expireDates[0];
      submitData.expireDate = submitData.expireDates[1];
    } else {
      submitData.effectiveDate = '';
      submitData.expireDate = '';
    }
  }
  // 兑换方案
  if (submitData.exchangePlans && Array.isArray(submitData.exchangePlans)) {
    submitData.instanceList = submitData.exchangePlans.map((item, index) => ({
      no: index + 1,
      exchangePlanId: item.id,
      name: item.name,
      type: item.type,
      optionalQty: item.optional_qty,
      code: item.code,
      price: item.price,
      instruction: item.desc
    }));
  }

  return submitData;
};

// 提交表单优化
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    const submitData = prepareSubmitData();
    const apiFunc = submitData.id ? updateExchangeBatch : addExchangeBatch;

    const res = await apiFunc(submitData);
    if (res.code === 200) {
      proxy.$modal.msgSuccess(submitData.id ? '修改成功' : '新增成功');
      handleClose();
      emit('submit');
    } else {
      proxy.$modal.msgError(res.msg || '提交失败');
    }
  } catch (error) {
    console.error('表单验证失败', error);
    proxy.$modal.msgError('提交失败，请检查表单数据');
  }
};

// 导出方法
defineExpose({
  dialogOpen
});
</script>

<style scoped>
@import '@/assets/styles/anchorform.scss';

.form-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.section-title {
  margin-bottom: 15px;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title .title-text {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  position: relative;
}

/* .section-title .title-text::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 16px;
  width: 3px;
  background-color: #409eff;
} */

.state-label {
  display: inline-block;
  padding: 2px 10px;
  background-color: #f0f9eb;
  color: #67c23a;
  border-radius: 4px;
  font-size: 12px;
}

.batch-cover-uploader {
  width: 100%;
}

.cover-placeholder {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.cover-placeholder:hover {
  border-color: #409eff;
}

.uploaded-cover {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 6px;
}

.template-cover-preview {
  margin-bottom: 10px;
  text-align: center;
}

.template-cover-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
