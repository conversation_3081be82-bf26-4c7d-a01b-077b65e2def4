<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="模板名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" clearable @change="handleQuery">
                <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="卡类型" prop="cardType">
              <el-select v-model="queryParams.cardType" placeholder="请选择卡类型" clearable @change="handleQuery">
                <el-option v-for="dict in mall_card_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="卡形态" prop="cardForm">
              <el-select v-model="queryParams.cardForm" placeholder="请选择卡形态" clearable @change="handleQuery">
                <el-option v-for="dict in mall_card_form" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="交付方式" prop="deliveryType">
              <el-select v-model="queryParams.deliveryType" placeholder="请选择交付方式" clearable @change="handleQuery">
                <el-option v-for="dict in mall_delivery_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新人" prop="updateBy">
              <el-select v-model="queryParams.updateBy" placeholder="请输入更新人" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mall:exchangeTemplate:add']">新增</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="templateList" border>
        <el-table-column type="index" width="55" label="序号" />
        <el-table-column label="模板名称" prop="name" min-width="150">
          <template #default="scope">
            <el-tooltip popper-class="template-name-tooltip" :content="scope.row.name" placement="top" :show-after="200" :max-width="300">
              <div class="two-line-ellipsis">{{ scope.row.name }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="卡封面" prop="coverImageUrl">
          <template #default="scope">
            <image-preview :src="scope.row.coverImageUrl" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="卡类型" prop="cardType">
          <template #default="scope">
            <dict-tag :options="mall_card_type" :value="scope.row.cardType" />
          </template>
        </el-table-column>
        <el-table-column label="模板状态" prop="status">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="'0'"
              :inactive-value="'1'"
              @change="(val) => handleStatusChange(scope.row)"
              v-hasPermi="['mall:card:model:edit']"
              active-text="启用"
              inactive-text="停用"
              inline-prompt
            />
          </template>
        </el-table-column>
        <el-table-column label="兑换次数" prop="times" width="100">
          <template #header>
            <span>兑换次数</span>
            <el-tooltip content="一张卡可以兑换的次数" placement="top">
              <el-icon :size="12" class="el-icon--right">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </template>
          <template #default="scope">
            <span>{{ scope.row.times }}</span>
          </template>
        </el-table-column>
        <el-table-column label="卡形态" prop="cardForm" width="100">
          <template #default="scope">
            <dict-tag :options="mall_card_form" :value="scope.row.cardForm" />
          </template>
        </el-table-column>
        <el-table-column label="是否周期发货" prop="shippingCycle" width="120">
          <template #default="scope">
            <dict-tag :options="mall_shipping_cycle" :value="scope.row.shippingCycle" />
          </template>
        </el-table-column>
        <el-table-column label="第几天发货" prop="cycleDay" width="120">
          <template #header>
            <span>第几天发货</span>
            <el-tooltip content="默认第几天发货，在该周期内用户可以调整时间" placement="top">
              <el-icon :size="12" class="el-icon--right">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </template>
          <template #default="scope">
            <span>{{ scope.row.cycleDay || '用户选择' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="面值（元）" prop="value" width="100">
          <template #default="scope">
            {{ scope.row.value ? (scope.row.value / 100).toFixed(2) : '- -' }}
          </template>
        </el-table-column>
        <el-table-column label="卡描述" prop="desc" width="250" show-overflow-tooltip></el-table-column>

        <el-table-column label="更新人" prop="updateByName" width="150" />
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ proxy.parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="150">
          <template #default="scope">
            <div class="flex items-center gap-2">
              <el-tooltip content="编辑" placement="top">
                <el-button link type="primary" @click="handleUpdate(scope.row)" icon="Edit" v-hasPermi="['mall:exchangeTemplate:edit']" />
              </el-tooltip>
              <el-tooltip content="查看" placement="top">
                <el-button link type="primary" @click="handleView(scope.row)" icon="View" v-hasPermi="['mall:exchangeTemplate:query']" />
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button link type="primary" @click="handleDelete(scope.row)" icon="Delete" v-hasPermi="['mall:exchangeTemplate:remove']" />
              </el-tooltip>
              <!-- <el-dropdown v-if="checkPermi(['mall:card:model:remove'])">
                <el-button link type="primary">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleDelete(scope.row)">
                      <div class="flex items-center">
                        <el-icon class="mr-1"><Delete /></el-icon>
                        <span>删除</span>
                      </div>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown> -->
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <CardModelDialog ref="cardModelDialogRef" @submit="handleSubmit" />
  </div>
</template>

<script setup name="modelList">
import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
import CardModelDialog from './Dialog.vue';
import { listExchangeTemplate, getExchangeTemplate, delExchangeTemplate, updateExchangeTemplate } from '@/api/mall/exchangeTemplate';
import { Delete, ArrowDown } from '@element-plus/icons-vue';
import { checkPermi } from '@/utils/permission';

const { proxy } = getCurrentInstance();
const { loadUserList, userOptions } = useSysUserSelect();
const showSearch = ref(true);
const loading = ref(false);
let cardModelDialogRef = ref(null);
const dateRangeUpdateTime = ref(['', '']);
const { sys_normal_disable, mall_card_type, mall_card_form, mall_delivery_type, mall_shipping_cycle } = toRefs(
  proxy?.useDict('sys_normal_disable', 'mall_card_type', 'mall_card_form', 'mall_delivery_type', 'mall_shipping_cycle')
);
const total = ref(0);
const templateList = ref([]);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  name: undefined,
  status: undefined,
  cardType: undefined,
  cardForm: undefined,
  deliveryType: undefined,
  value: undefined,
  updateBy: undefined
});

// 搜索
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

// 获取列表
const getList = async () => {
  loading.value = true;
  try {
    // 处理日期范围
    const params = {
      ...queryParams.value,
      params: {}
    };

    if (dateRangeUpdateTime.value && dateRangeUpdateTime.value.length === 2 && dateRangeUpdateTime.value[0] && dateRangeUpdateTime.value[1]) {
      params.params.beginUpdateTime = dateRangeUpdateTime.value[0];
      params.params.endUpdateTime = dateRangeUpdateTime.value[1];
    }

    const response = await listExchangeTemplate(params);
    templateList.value = response.rows;
    total.value = response.total;
  } catch (error) {
    console.error('获取卡模板列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 重置查询
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  // 手动重置查询参数
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    status: undefined,
    cardType: undefined,
    cardForm: undefined,
    deliveryType: undefined,
    value: undefined,
    updateBy: undefined
  };
  handleQuery();
};

// 新增
const handleAdd = () => {
  cardModelDialogRef.value.dialogOpen({});
};

// 编辑
const handleUpdate = (row) => {
  const id = row.id;
  loading.value = true;
  getExchangeTemplate(id)
    .then((response) => {
      loading.value = false;
      if (response.code === 200) {
        cardModelDialogRef.value.dialogOpen(response.data, 'edit');
      } else {
        proxy.$modal.msgError('获取模板详情失败');
      }
    })
    .catch((error) => {
      loading.value = false;
      console.error('获取模板详情失败:', error);
      proxy.$modal.msgError('获取模板详情失败');
    });
};

// 查看
const handleView = (row) => {
  const id = row.id;
  loading.value = true;
  getExchangeTemplate(id)
    .then((response) => {
      loading.value = false;
      if (response.code === 200) {
        cardModelDialogRef.value.dialogOpen(response.data, 'view');
      } else {
        proxy.$modal.msgError('获取模板详情失败');
      }
    })
    .catch((error) => {
      loading.value = false;
      console.error('获取模板详情失败:', error);
      proxy.$modal.msgError('获取模板详情失败');
    });
};

// 删除
const handleDelete = (row) => {
  const ids = row.id || templateList.value.map((item) => item.id);
  proxy.$modal
    .confirm(`是否确认删除卡模板? 请谨慎操作！`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      return delExchangeTemplate(ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
};

// 状态修改
const handleStatusChange = (row) => {
  const text = row.status === '0' ? '启用' : '停用';
  // 使用updateExchangeTemplate API更新状态
  updateExchangeTemplate({
    id: row.id,
    status: row.status
  })
    .then(() => {
      proxy.$modal.msgSuccess(`模板已${text}`);
    })
    .catch(() => {
      // 如果API调用失败，恢复开关状态
      row.status = row.status === '0' ? '1' : '0';
      proxy.$modal.msgError(`${text}失败`);
    });
};

const handleSubmit = () => {
  getList();
};

// 初始加载
loadUserList();
getList();
</script>

<style scoped>
.two-line-ellipsis {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}
</style>

<style>
.template-name-tooltip {
  max-width: 300px !important;
  white-space: normal !important;
  word-break: break-all;
  line-height: 1.5;
}
</style>
