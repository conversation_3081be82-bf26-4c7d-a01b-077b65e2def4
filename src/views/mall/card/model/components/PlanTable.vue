<template>
  <div class="plan-table-container">
    <div class="section-title">
      <!-- <span class="title-text">兑换方案</span> -->
    </div>
    <el-table :data="planList" style="width: 100%" border>
      <el-table-column label="卡次" width="70">
        <template #default="scope">第{{ scope.$index + 1 }}次</template>
      </el-table-column>
      <el-table-column label="兑换方案" prop="name" width="200" show-overflow-tooltip>
        <template #default="scope">
          <el-tooltip content="查看详情" placement="top">
            <el-link type="primary" @click="openPlanDetailInEdit(scope.row)">
              {{ scope.row.name }}
            </el-link>
            <!-- <div class="plan-name-cell">{{ scope.row.name }}</div> -->
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="方案类型" prop="sort" width="100">
        <template #default="scope">
          <dict-tag :options="mall_exchange_type" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="商品数量" prop="goodsCount" width="90">
        <template #default="scope">
          <span style="color: #909399">{{ scope.row.goodsCount }}</span>
          <span style="color: #f56c6c; margin: 5px">选</span>
          <span style="color: #909399">{{ scope.row.optional_qty }}</span>
        </template>
      </el-table-column>
      <el-table-column label="兑换开始" prop="exchangeStartDate" width="120">
        <template #default="scope"> {{ parseTime(scope.row.exchangeStartDate, '{y}-{m}-{d}') }} </template>
      </el-table-column>
      <el-table-column label="兑换结束" prop="exchangeEndDate" width="120">
        <template #default="scope"> {{ parseTime(scope.row.exchangeEndDate, '{y}-{m}-{d}') }} </template>
      </el-table-column>
      <el-table-column label="方案描述" prop="desc" show-overflow-tooltip />
      <el-table-column label="操作" width="60" align="center" v-if="!disabled" fixed="right">
        <template #default="scope">
          <el-tooltip content="更换方案" placement="top">
            <el-button type="primary" link icon="Edit" @click="handleChangePlan(scope.$index)"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <!-- 选择兑换方案弹窗 -->
    <plan-selector ref="planSelectorRef" :multiple="false" @select="handlePlanSelected" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, getCurrentInstance } from 'vue';
import PlanSelector from './PlanSelector.vue';
import { ExchangePlanVO } from '@/api/mall/exchangePlan/types';

const { proxy } = getCurrentInstance();
const { mall_exchange_type, sys_normal_disable } = toRefs(proxy?.useDict('mall_exchange_type', 'sys_normal_disable'));
const emit = defineEmits(['change']);

const props = defineProps({
  plans: {
    type: Array,
    default: () => []
  },
  disabled: {
    type: Boolean,
    default: false
  },
  maxPlans: {
    type: Number,
    default: 0
  },
  times: {
    type: [Number, String],
    default: 1
  }
});

const planList = ref([]);

watch(
  () => props.maxPlans,
  (val) => {
    planList.value = planList.value.slice(0, val);
    emit('change', planList.value);
  }
);

watch(
  () => props.plans,
  (val) => {
    planList.value = val;
    emit('change', planList.value);
  },
  {
    immediate: true
  }
);

// 方案选择器
const planSelectorRef = ref(null);
// 当前正在编辑的卡次索引，-1表示新增
const currentEditIndex = ref(-1);

// 更换方案
const handleChangePlan = (index) => {
  // 标记当前正在编辑的卡次索引
  currentEditIndex.value = index;
  // 打开方案选择器（不再排除已选择的方案）
  planSelectorRef.value.open();
};

// 处理选择已有兑换方案
const handlePlanSelected = (selectedPlan) => {
  const newPlans = [...planList.value];
  const oldPlan = newPlans[currentEditIndex.value];
  const planData = {
    id: selectedPlan.id,
    name: selectedPlan.name,
    code: selectedPlan.code,
    type: selectedPlan.type,
    optional_qty: selectedPlan.optional_qty,
    price: selectedPlan.price,
    desc: selectedPlan.desc,
    goodsCount: selectedPlan.goodsCount,
    exchangeStartDate: selectedPlan.exchangeStartDate,
    exchangeEndDate: selectedPlan.exchangeEndDate,
    oldId: oldPlan.id,
    currentIndex: currentEditIndex.value
  };
  newPlans[currentEditIndex.value] = planData;
  emit('change', newPlans, currentEditIndex.value);
  // 重置编辑索引
  currentEditIndex.value = -1;
};

// 打开方案详情(编辑页面)
const openPlanDetailInEdit = (row?: ExchangePlanVO) => {
  const routeUrl = `/mall/goods/exchangePlan?id=${row?.id}&openEdit=true`;
  window.open(routeUrl, '_blank');
};

defineExpose({
  handleChangePlan
});
</script>

<style scoped>
.plan-table-container {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title-text {
  font-size: 16px;
  font-weight: bold;
}

.plan-name-cell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}
</style>

<style>
/* 覆盖el-tooltip的默认样式 */
.el-popper.is-dark {
  max-width: 300px;
  word-break: break-all;
  line-height: 1.5;
}
</style>
