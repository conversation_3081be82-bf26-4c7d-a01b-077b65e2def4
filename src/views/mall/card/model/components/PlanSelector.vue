<template>
  <el-dialog :title="title || '选择兑换方案'" v-model="visible" width="70%" :close-on-click-modal="false" destroy-on-close>
    <div class="plan-select-container">
      <!-- 方案搜索 -->
      <el-form :model="queryParams" :inline="true">
        <el-form-item label="方案名称">
          <el-input v-model="queryParams.name" placeholder="请输入方案名称" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="方案编码">
          <el-input v-model="queryParams.code" placeholder="请输入方案编码" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="方案类型">
          <el-select v-model="queryParams.type" placeholder="请选择方案类型" clearable>
            <el-option v-for="dict in mall_exchange_type" :key="dict.value" :label="dict.label" :value="dict.value" @change="handleQuery" />
          </el-select>
        </el-form-item>
        <el-form-item label="方案状态">
          <el-select v-model="queryParams.status" placeholder="请选择方案状态" clearable>
            <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" @change="handleQuery" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 方案列表 -->
      <el-table
        :data="planList"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
        ref="planTableRef"
        border
        highlight-current-row
        style="width: 100%"
        v-loading="loading"
        :row-class-name="rowClassName"
        :selectable="rowSelectable"
      >
        <el-table-column type="selection" width="55" v-if="multiple" />
        <el-table-column type="selection" width="50" />
        <el-table-column label="方案名称" prop="name" width="200" show-overflow-tooltip>
          <template #default="scope">
            <el-tooltip content="方案编码" placement="top">
              <div>{{ scope.row.code }}</div>
            </el-tooltip>
            <el-tooltip content="方案名称" placement="top">
              <div class="two-line-ellipsis" style="margin-top: 5px; color: #909399">{{ scope.row.name }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <!-- <el-table-column label="方案编码" prop="code" width="160" /> -->
        <el-table-column label="方案类型" prop="type" width="100">
          <template #default="scope">
            <dict-tag :options="mall_exchange_type" :value="scope.row.type" />
          </template>
        </el-table-column>
        <el-table-column label="商品数量" prop="goodsCount" width="80">
          <template #default="scope">
            <span style="color: #909399">{{ scope.row.goodsCount }}</span>
            <span style="color: #f56c6c; margin: 5px">选</span>
            <span style="color: #909399">{{ scope.row.optional_qty }}</span>
          </template>
        </el-table-column>
        <el-table-column label="市场价(元)" prop="price" width="100">
          <template #default="scope">
            {{ formatPrice(scope.row.price) }}
          </template>
        </el-table-column>
        <el-table-column label="最低售价(元)" prop="minSellPrice" width="100">
          <template #default="scope">
            {{ formatPrice(scope.row.minSellPrice) }}
          </template>
        </el-table-column>
        <el-table-column label="成本价(元)" prop="costPrice" width="100">
          <template #default="scope">
            {{ formatPrice(scope.row.costPrice) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="80">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="兑换开始" prop="exchangeStartDate" width="100">
          <template #default="scope">
            {{ parseTime(scope.row.exchangeStartDate, '{y}-{m}-{d}') }}
          </template>
        </el-table-column>
        <el-table-column label="兑换结束" prop="exchangeEndDate" width="100">
          <template #default="scope">
            {{ parseTime(scope.row.exchangeEndDate, '{y}-{m}-{d}') }}
          </template>
        </el-table-column>
        <el-table-column label="方案描述" prop="desc" width="200" show-overflow-tooltip />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="multiple ? selectedList.length === 0 : !selectedItem">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted, computed, getCurrentInstance } from 'vue';
import { ElMessage } from 'element-plus';
import { listExchangePlan } from '@/api/mall/exchangePlan';

const { proxy } = getCurrentInstance();
const { mall_exchange_type, sys_normal_disable } = toRefs(proxy?.useDict('mall_exchange_type', 'sys_normal_disable'));

const props = defineProps({
  // 是否多选
  multiple: {
    type: Boolean,
    default: false
  },
  // 排除的方案ID列表（已选择的不再显示）
  excludeIds: {
    type: Array,
    default: () => []
  },
  // 对话框标题
  title: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['select']);

// 状态变量
const visible = ref(false);
const loading = ref(false);
const planList = ref([]);
const total = ref(0);
const selectedItem = ref(null);
const selectedList = ref([]);
const planTableRef = ref(null);
const currentExcludeIds = ref([]); // 当前排除的ID列表

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: '',
  code: '',
  type: '',
  status: '0'
});

// 格式化价格（分转元）
const formatPrice = (price) => {
  if (price === undefined || price === null) return '0.00';
  return (price / 100).toFixed(2);
};

// 获取方案列表
const getPlanList = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize,
      name: queryParams.name,
      code: queryParams.code,
      status: queryParams.status,
      type: queryParams.type + ''
    };

    const res = await listExchangePlan(params);
    planList.value =
      res.rows.map((item) => ({
        id: item.id,
        name: item.name,
        code: item.code,
        type: item.type,
        desc: item.desc,
        price: item.price,
        minSellPrice: item.minSellPrice,
        costPrice: item.costPrice,
        status: item.status,
        goodsCount: item.detail.length || '- -',
        optional_qty: item.optionalQty + '',
        exchangeStartDate: item.exchangeStartDate,
        exchangeEndDate: item.exchangeEndDate
      })) || [];
    total.value = res.total || 0;

    // 不再过滤已选择的方案，而是在UI上将其标记为禁用
  } catch (error) {
    console.error('获取兑换方案列表失败:', error);
    ElMessage.error('获取兑换方案列表失败');
  } finally {
    loading.value = false;
  }
};

// 行样式
const rowClassName = ({ row }) => {
  // 如果方案已停用，添加禁用样式
  if (row.status === '1') {
    return 'disabled-row';
  }
  return '';
};

// 控制行是否可选
const rowSelectable = (row) => {
  // 如果方案已停用，不可选
  return row.status !== '1';
};

// 处理查询
const handleQuery = () => {
  queryParams.pageNum = 1;
  getPlanList();
};

// 重置查询
const resetQuery = () => {
  queryParams.name = '';
  queryParams.code = '';
  queryParams.type = '';
  queryParams.status = '';
  queryParams.pageNum = 1;
  getPlanList();
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  queryParams.pageSize = size;
  getPlanList();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  queryParams.pageNum = page;
  getPlanList();
};

// 处理表格行点击
const handleRowClick = (row) => {
  // 如果方案已停用，则不允许选择
  if (row.status === '1') {
    ElMessage.warning('已停用的兑换方案不可选择');
    return;
  }

  if (props.multiple) {
    // 多选模式下，点击行切换选中状态
    planTableRef.value.toggleRowSelection(row);
  } else {
    // 单选模式下，设置选中的方案
    selectedItem.value = row;
    // 清除之前的选择
    if (planTableRef.value) {
      planTableRef.value.clearSelection();
      planTableRef.value.toggleRowSelection(row, true);
    }
  }
};

// 打开对话框
const open = (excludeIds = [], maxSelectCount = null) => {
  visible.value = true;

  // 记录最大可选数量
  if (props.multiple && maxSelectCount) {
    // 如果是多选模式且指定了最大选择数量
    maxSelectLimit.value = maxSelectCount;
  } else {
    maxSelectLimit.value = null;
  }

  // 重置选择状态
  selectedItem.value = null;
  selectedList.value = [];

  // 加载方案列表
  handleQuery();
};

// 最大可选数量限制
const maxSelectLimit = ref(null);

// 处理多选变化
const handleSelectionChange = (selection) => {
  // 过滤掉已停用的方案
  const validSelection = selection.filter((item) => item.status !== '1');

  // 如果选择包含停用的方案，提示用户
  if (selection.length !== validSelection.length) {
    ElMessage.warning('已停用的兑换方案不可选择');
  }

  // 如果设置了最大选择限制且当前选择超出限制
  if (props.multiple && maxSelectLimit.value && validSelection.length > maxSelectLimit.value) {
    // 将最后一个选择的取消选中
    const lastSelected = validSelection[validSelection.length - 1];
    planTableRef.value.toggleRowSelection(lastSelected, false);

    // 提示用户
    ElMessage.warning(`最多只能选择${maxSelectLimit.value}个方案`);

    // 恢复之前的选择
    selectedList.value = validSelection.slice(0, maxSelectLimit.value);
    return;
  }

  selectedList.value = validSelection;
  if (!props.multiple && validSelection.length > 0) {
    selectedItem.value = validSelection[0];
  }
};

// 取消选择
const handleCancel = () => {
  visible.value = false;
};

// 确认选择
const handleConfirm = () => {
  if (props.multiple) {
    if (selectedList.value.length === 0) {
      ElMessage.warning('请至少选择一个兑换方案');
      return;
    }
    emit('select', selectedList.value);
  } else {
    if (!selectedItem.value) {
      ElMessage.warning('请选择一个兑换方案');
      return;
    }
    emit('select', selectedItem.value);
  }
  visible.value = false;
};

defineExpose({
  open
});
</script>

<style scoped>
.plan-select-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

/* 停用行样式 */
:deep(.disabled-row) {
  background-color: #f5f7fa;
  color: #909399;
  cursor: not-allowed;
}
</style>
