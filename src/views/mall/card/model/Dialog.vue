<template>
  <el-dialog :title="dialogTitle" v-model="visible" @close="handleClose" width="80%">
    <el-row :gutter="10">
      <!-- 左侧锚点导航 -->
      <el-col :span="4">
        <el-anchor>
          <el-anchor-link href="#basic" title="基础信息" />
          <el-anchor-link href="#dh" title="兑换方案" />
        </el-anchor>
      </el-col>
      <el-col :span="20">
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="80px" label-position="top" :disabled="type === 'view'">
          <div id="basic" class="form-section">
            <div class="section-title">
              <span class="title-text">基础信息</span>
            </div>
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="模板名称" prop="name">
                  <el-input v-model="formData.name" placeholder="请输入模板名称" maxlength="50" show-word-limit />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="卡类型" prop="cardType">
                  <el-select v-model="formData.cardType" placeholder="请选择卡类型">
                    <el-option v-for="dict in mall_card_type" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="卡形态" prop="cardForm">
                  <el-select v-model="formData.cardForm" placeholder="请选择卡形态">
                    <el-option v-for="dict in mall_card_form" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="模板状态" prop="status">
                  <el-select v-model="formData.status" placeholder="请选择状态">
                    <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="面值(元)" prop="value">
                  <el-input-number v-model="formData.value" :min="0.01" :max="100000" placeholder="请输入面值" :precision="2" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="交付方式" prop="deliveryType">
                  <el-select v-model="formData.deliveryType" placeholder="请选择">
                    <el-option v-for="dict in mall_delivery_type" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="是否周期发货" prop="shippingCycle">
                  <el-select v-model="formData.shippingCycle" placeholder="请选择" @change="handleShippingCycleChange">
                    <el-option v-for="dict in mall_shipping_cycle" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="formData.shippingCycle !== '1'">
                <el-form-item label="默认第几天发货" prop="cycleDay">
                  <el-input-number
                    v-model="formData.cycleDay"
                    :min="getMinDay"
                    :max="getMaxDay"
                    :precision="0"
                    placeholder="请输入天数"
                    style="width: 100%"
                    :disabled="formData.shippingCycle === '1' || !formData.shippingCycle"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="封面图片" prop="coverImage" required>
                  <imageUpload :limit="1" v-model="formData.coverImage" :disabled="type === 'view'" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="模板描述" prop="desc">
                  <el-input type="textarea" :rows="6" maxlength="255" show-word-limit v-model="formData.desc" placeholder="请输入模板描述" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="备注" prop="remark">
                  <el-input type="textarea" :rows="6" maxlength="500" show-word-limit v-model="formData.remark" placeholder="请输入备注" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10"> </el-row>
          </div>
          <div id="dh" class="form-section">
            <div class="section-title">
              <span class="title-text">兑换方案</span>
            </div>
            <el-col :span="6">
              <el-form-item label="兑换次数(这里设置卡次的数量)" prop="times">
                <el-input-number
                  v-model="formData.times"
                  :min="1"
                  :max="99"
                  :precision="0"
                  :step="1"
                  placeholder="请输入次数"
                  style="width: 100%"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>
            <plan-table
              id="planTableComponent"
              :plans="planList"
              :disabled="type === 'view'"
              :maxPlans="formData.times"
              @change="handlePlanChange"
              ref="planTableRef"
            ></plan-table>
          </div>
        </el-form>
      </el-col>
    </el-row>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ type === 'view' ? '关闭' : '取消' }}</el-button>
        <el-button v-if="type !== 'view'" type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, getCurrentInstance, computed, watch } from 'vue';
import { addExchangeTemplate, updateExchangeTemplate } from '@/api/mall/exchangeTemplate';
import PlanTable from './components/PlanTable.vue';
import { yuanToCent } from '@/utils/moneyUtils';
import { nullable } from 'vue-types';

const { proxy } = getCurrentInstance();
const emit = defineEmits(['submit']);
const formRef = ref();
const formData = ref({
  name: '',
  status: '0',
  cardType: '',
  cardForm: '',
  times: 1,
  value: 0,
  sort: 0,
  desc: '',
  remark: '',
  detail: '',
  instruction: '',
  deliveryType: '',
  shippingCycle: '',
  coverImage: '',
  cycleDay: null,
  effStartDate: '',
  effEndDate: '',
  code: '',
  planList: []
});
const type = ref('add');
const visible = ref(false);
const submitLoading = ref(false);
const planList = ref([]);
const effDateRange = ref([]);
const planTableRef = ref(null);

const { sys_normal_disable, mall_card_type, mall_card_form, mall_delivery_type, mall_shipping_cycle } = toRefs(
  proxy?.useDict('sys_normal_disable', 'mall_card_type', 'mall_card_form', 'mall_delivery_type', 'mall_shipping_cycle')
);

const rules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { max: 50, message: '模板名称不能超过50个字符', trigger: 'blur' }
  ],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  cardType: [{ required: true, message: '请选择卡类型', trigger: 'change' }],
  cardForm: [{ required: true, message: '请选择卡形态', trigger: 'change' }],
  times: [{ required: true, message: '请输入兑换次数', trigger: 'blur' }],
  value: [
    { required: true, message: '请输入面值', trigger: 'blur' },
    { type: 'number', min: 0.01, max: 100000, message: '面值必须大于0且小于10万元', trigger: 'blur' }
  ],
  deliveryType: [{ required: true, message: '请选择交付方式', trigger: 'change' }],
  shippingCycle: [{ required: true, message: '请选择是否周期发货', trigger: 'change' }],
  cycleDay: [{ required: false, message: '请输入有效天数', trigger: 'blur' }],
  coverImage: [{ required: true, message: '请上传封面图片', trigger: 'change' }],
  desc: [{ max: 255, message: '模板描述不能超过255个字符', trigger: 'blur' }]
};

// 计算属性
const dialogTitle = computed(() => {
  const titleMap = {
    add: '新增卡模板',
    edit: '编辑卡模板',
    view: '查看卡模板'
  };
  return titleMap[type.value] || '卡模板';
});

// 根据周期类型获取最小天数
const getMinDay = computed(() => {
  return 1; // 所有类型最小值都是1
});

// 根据周期类型获取最大天数
const getMaxDay = computed(() => {
  if (formData.value.shippingCycle === '2') {
    return 7; // 按周发货，限制1-7天
  } else if (formData.value.shippingCycle === '3') {
    return 31; // 按月发货，限制1-31天
  }
  return 365; // 默认最大值
});

// 监听有效期日期范围变化
watch(effDateRange, (newValue) => {
  if (newValue && newValue.length === 2) {
    formData.value.effStartDate = newValue[0];
    formData.value.effEndDate = newValue[1];
  } else {
    formData.value.effStartDate = '';
    formData.value.effEndDate = '';
  }
});

// 有效期类型变更处理
const handleShippingCycleChange = (val) => {
  if (val === '1') {
    // 不限制，清空天数
    formData.value.cycleDay = null;
  } else if (val === '2') {
    // 按周
    if (!formData.value.cycleDay || formData.value.cycleDay > 7) {
      formData.value.cycleDay = 1; // 默认周一
    }
  } else if (val === '3') {
    // 按月
    if (!formData.value.cycleDay || formData.value.cycleDay > 31) {
      formData.value.cycleDay = 1; // 默认每月1日
    }
  }
};

const dialogOpen = (data, _type = 'add') => {
  visible.value = true;
  type.value = _type;

  // 重置表单
  planList.value = [];

  // 填充表单数据
  if (Object.keys(data).length > 0) {
    formData.value = { ...data };

    // 面值转换：分 -> 元（除以100）
    if (formData.value.value) {
      formData.value.value = formData.value.value / 100;
    }

    // 处理有效期日期范围
    if (formData.value.effStartDate && formData.value.effEndDate) {
      effDateRange.value = [formData.value.effStartDate, formData.value.effEndDate];
    } else {
      effDateRange.value = [];
    }
    // 处理兑换方案列表
    if (formData.value.detail && formData.value.detail.length > 0) {
      planList.value = formData.value.detail;
    }
  } else {
    // 新增时的默认值
    formData.value = {
      name: '',
      status: '0',
      cardType: '',
      cardForm: '',
      times: undefined,
      value: undefined,
      sort: 0,
      desc: '',
      remark: '',
      detail: '',
      instruction: '',
      deliveryType: '',
      shippingCycle: '',
      coverImage: '',
      cycleDay: null,
      effStartDate: '',
      effEndDate: '',
      code: ''
    };
    effDateRange.value = [];

    // 新增时，默认填充一个空的方案项
    planList.value = [{ id: '', name: '', type: '', describe: '', order: 1 }];
  }
};

const handleClose = () => {
  visible.value = false;
};

const handleSubmit = async () => {
  if (type.value === 'view') {
    handleClose();
    return;
  }

  formRef.value.validate(async (valid) => {
    if (!valid) return;

    // 检查兑换方案数量是否与次数匹配
    if (planList.value.length !== formData.value.times) {
      proxy.$modal.msgError(`兑换方案数量需要与兑换次数(${formData.value.times})一致，当前配置了${planList.value.length}个方案`);
      return;
    }

    // 检查每个方案是否完整填写
    for (let i = 0; i < planList.value.length; i++) {
      const plan = planList.value[i];

      // 检查方案其他必填字段
      if (!plan.name || !plan.type) {
        proxy.$modal.msgError(`第${i + 1}次兑换的方案信息不完整，请检查`);
        return;
      }
    }

    submitLoading.value = true;
    try {
      // 根据API要求，detail字段为字符串，将planList序列化为JSON字符串
      const detail = planList.value;

      // 组装完整表单数据，符合API要求
      const submitData = {
        name: formData.value.name,
        code: formData.value.code,
        coverImage: formData.value.coverImage,
        status: formData.value.status,
        cardType: formData.value.cardType,
        cardForm: formData.value.cardForm,
        times: formData.value.times,
        desc: formData.value.desc,
        instruction: formData.value.instruction,
        // 面值转换：元 -> 分（使用工具函数确保正确转换）
        value: yuanToCent(formData.value.value),
        detail: detail,
        deliveryType: formData.value.deliveryType,
        shippingCycle: formData.value.shippingCycle,
        sort: formData.value.sort,
        remark: formData.value.remark
      };

      // 添加有效期相关参数
      if (formData.value.shippingCycle === '1') {
        // 日期区间
        submitData.effStartDate = formData.value.effStartDate;
        submitData.effEndDate = formData.value.effEndDate;
      } else if (formData.value.shippingCycle === '2' || formData.value.shippingCycle === '3') {
        // 按周或按月都需要传递cycleDay
        submitData.cycleDay = formData.value.cycleDay;
      }

      if (type.value === 'add') {
        await addExchangeTemplate(submitData);
        proxy.$modal.msgSuccess('新增成功');
      } else {
        // 修改时需要传入id
        if (formData.value.id) {
          submitData.id = formData.value.id;
        }
        await updateExchangeTemplate(submitData);
        proxy.$modal.msgSuccess('修改成功');
      }
      handleClose();
      emit('submit');
    } catch (error) {
      console.error('提交表单失败:', error);
    } finally {
      submitLoading.value = false;
    }
  });
};

// 处理兑换方案变更
const handlePlanChange = (plans) => {
  planList.value = plans;
};

// 监听兑换次数变化，设置验证规则
watch(
  () => formData.value.times,
  (newValue, oldValue) => {
    // 根据兑换次数调整方案列表
    if (newValue > planList.value.length) {
      // 如果增加了次数，添加空白方案项
      for (let i = planList.value.length; i < newValue; i++) {
        planList.value.push({ id: '', name: '', type: '', describe: '', order: i + 1 });
      }
    } else if (newValue < planList.value.length) {
      // 如果减少了次数，截断方案列表
      planList.value = planList.value.slice(0, newValue);
    }
  }
);

defineExpose({
  dialogOpen
});
</script>

<style scoped>
@import '@/assets/styles/anchorform.scss';

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title-text {
  font-size: 16px;
  font-weight: bold;
}
</style>
