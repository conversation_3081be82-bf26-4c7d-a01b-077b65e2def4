<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="商品订单id" prop="goodsOrderId">
              <el-input v-model="queryParams.goodsOrderId" placeholder="请输入商品订单id" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="商品id" prop="goodsId">
              <el-input v-model="queryParams.goodsId" placeholder="请输入商品id" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="被替换商品id" prop="replacedGoodsId">
              <el-input v-model="queryParams.replacedGoodsId" placeholder="请输入被替换商品id" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="更新人" prop="updateBy">
              <el-input v-model="queryParams.updateBy" placeholder="请输入更新人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <!-- <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mall:goodsOrderDetail:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['mall:goodsOrderDetail:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['mall:goodsOrderDetail:remove']"
              >删除</el-button
            >
          </el-col> -->
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['mall:goodsOrderDetail:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="goodsOrderDetailList" @selection-change="handleSelectionChange" border>
        <!-- <el-table-column type="selection" width="55" /> -->
        <el-table-column label="id" prop="id" v-if="false" />
        <!-- <el-table-column label="商品订单id" prop="goodsOrderId" /> -->
        <!-- <el-table-column label="商品id" prop="goodsId" /> -->
        <el-table-column label="商品图片" prop="goodsImageUrl">
          <template #default="scope">
            <image-preview :src="scope.row.goodsImageUrl" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="商品" prop="goodsName" width="150" />
        <el-table-column label="需求数量" prop="qty" />
        <el-table-column label="折扣" prop="discount" />
        <el-table-column label="单价" prop="unitPrice" />
        <el-table-column label="明细总价" prop="detailTotal" />
        <!-- <el-table-column label="被替换商品id" prop="replacedGoodsId" /> -->
        <el-table-column label="被替换商品" prop="replacedGoodsImageUrl">
          <template #default="scope">
            <image-preview :src="scope.row.replacedGoodsImageUrl" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="被替换商品" prop="replacedGoodsName" width="150" />
        <el-table-column label="被替换数量" prop="replacedQty" />
        <el-table-column label="被替换原因" prop="replacedReason">
          <template #default="scope">
            <dict-tag :options="mall_replaced_goods_reason" :value="scope.row.replacedReason" />
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" />
        <!-- <el-table-column label="更新人" prop="updateBy" /> -->
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mall:goodsOrderDetail:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mall:goodsOrderDetail:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改商品订单明细对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body @close="cancel" draggable>
      <el-form ref="goodsOrderDetailFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="商品订单id" prop="goodsOrderId">
          <el-input v-model="form.goodsOrderId" placeholder="请输入商品订单id" />
        </el-form-item>
        <el-form-item label="商品id" prop="goodsId">
          <el-input v-model="form.goodsId" placeholder="请输入商品id" />
        </el-form-item>
        <el-form-item label="需求数量" prop="qty">
          <el-input v-model="form.qty" placeholder="请输入需求数量" />
        </el-form-item>
        <el-form-item label="折扣" prop="discount">
          <el-input v-model="form.discount" placeholder="请输入折扣" />
        </el-form-item>
        <el-form-item label="单价，单位为分" prop="unitPrice">
          <el-input v-model="form.unitPrice" placeholder="请输入单价，单位为分" />
        </el-form-item>
        <el-form-item label="明细总价，单位为分" prop="detailTotal">
          <el-input v-model="form.detailTotal" placeholder="请输入明细总价，单位为分" />
        </el-form-item>
        <el-form-item label="被替换商品id" prop="replacedGoodsId">
          <el-input v-model="form.replacedGoodsId" placeholder="请输入被替换商品id" />
        </el-form-item>
        <el-form-item label="被替换数量" prop="replacedQty">
          <el-input v-model="form.replacedQty" placeholder="请输入被替换数量" />
        </el-form-item>
        <el-form-item label="被替换原因" prop="replacedReason">
          <el-radio-group v-model="form.replacedReason">
            <el-radio v-for="dict in mall_replaced_goods_reason" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GoodsOrderDetail" lang="ts">
import {
  listGoodsOrderDetail,
  getGoodsOrderDetail,
  delGoodsOrderDetail,
  addGoodsOrderDetail,
  updateGoodsOrderDetail
} from '@/api/mall/goodsOrderDetail';
import { GoodsOrderDetailVO, GoodsOrderDetailQuery, GoodsOrderDetailForm } from '@/api/mall/goodsOrderDetail/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_replaced_goods_reason } = toRefs<any>(proxy?.useDict('mall_replaced_goods_reason'));

const goodsOrderDetailList = ref<GoodsOrderDetailVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑

const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const goodsOrderDetailFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GoodsOrderDetailForm = {
  id: undefined,
  goodsOrderId: undefined,
  goodsId: undefined,
  qty: undefined,
  discount: undefined,
  unitPrice: undefined,
  detailTotal: undefined,
  replacedGoodsId: undefined,
  replacedQty: undefined,
  replacedReason: undefined,
  remark: undefined
};
const data = reactive<PageData<GoodsOrderDetailForm, GoodsOrderDetailQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    goodsOrderId: undefined,
    goodsId: undefined,
    replacedGoodsId: undefined,
    updateBy: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    goodsOrderId: [{ required: true, message: '商品订单id不能为空', trigger: 'blur' }],
    goodsId: [{ required: true, message: '商品id不能为空', trigger: 'blur' }],
    qty: [{ required: true, message: '需求数量不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询商品订单明细列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listGoodsOrderDetail(queryParams.value);
  goodsOrderDetailList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  goodsOrderDetailFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: GoodsOrderDetailVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加商品订单明细';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: GoodsOrderDetailVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getGoodsOrderDetail(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改商品订单明细';
};

/** 提交按钮 */
const submitForm = () => {
  goodsOrderDetailFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGoodsOrderDetail(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addGoodsOrderDetail(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: GoodsOrderDetailVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除商品订单明细编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delGoodsOrderDetail(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'mall/goodsOrderDetail/export',
    {
      ...queryParams.value
    },
    `goodsOrderDetail_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
