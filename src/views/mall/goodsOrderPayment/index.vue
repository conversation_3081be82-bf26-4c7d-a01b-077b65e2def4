<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="订单id" prop="orderId">
              <el-input v-model="queryParams.orderId" placeholder="请输入订单id" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="支付方式" prop="paymentMethod">
              <el-select v-model="queryParams.paymentMethod" placeholder="请选择支付方式" clearable >
                <el-option v-for="dict in mall_payment_method" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="支付状态" prop="paymentStatus">
              <el-select v-model="queryParams.paymentStatus" placeholder="请选择支付状态" clearable >
                <el-option v-for="dict in mall_payment_slip_status" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="支付流水号" prop="paymentId">
              <el-input v-model="queryParams.paymentId" placeholder="请输入支付流水号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="卡号" prop="cardCode">
              <el-input v-model="queryParams.cardCode" placeholder="请输入卡号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="卡次" prop="cardInstance">
              <el-input v-model="queryParams.cardInstance" placeholder="请输入卡次" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="支付时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangePaymentTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item label="支付金额" prop="paymentAmount">
              <el-input v-model="queryParams.paymentAmount" placeholder="请输入支付金额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="退款流水号" prop="refundId">
              <el-input v-model="queryParams.refundId" placeholder="请输入退款流水号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="退款时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeRefundTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item label="更新人" prop="updateBy">
              <el-input v-model="queryParams.updateBy" placeholder="请输入更新人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                <el-button link @click="showMoreCondition = !showMoreCondition">
                    {{ showMoreCondition ? '收起' : '展开' }}
                    <el-icon class="el-icon--right">
                        <arrow-up v-if="showMoreCondition" />
                        <arrow-down v-else />
                    </el-icon>
                </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mall:goodsOrderPayment:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['mall:goodsOrderPayment:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['mall:goodsOrderPayment:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['mall:goodsOrderPayment:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="goodsOrderPaymentList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="id" align="center" prop="id" v-if="true" />
        <el-table-column label="订单id" align="center" prop="orderId" />
        <el-table-column label="支付方式" align="center" prop="paymentMethod">
          <template #default="scope">
            <dict-tag :options="mall_payment_method" :value="scope.row.paymentMethod"/>
          </template>
        </el-table-column>
        <el-table-column label="支付状态" align="center" prop="paymentStatus">
          <template #default="scope">
            <dict-tag :options="mall_payment_slip_status" :value="scope.row.paymentStatus"/>
          </template>
        </el-table-column>
        <el-table-column label="支付流水号" align="center" prop="paymentId" />
        <el-table-column label="卡号" align="center" prop="cardCode" />
        <el-table-column label="卡次" align="center" prop="cardInstance" />
        <el-table-column label="支付时间" align="center" prop="paymentTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.paymentTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="支付金额" align="center" prop="paymentAmount" />
        <el-table-column label="退款流水号" align="center" prop="refundId" />
        <el-table-column label="退款时间" align="center" prop="refundTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.refundTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="更新人" align="center" prop="updateBy" />
        <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mall:goodsOrderPayment:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mall:goodsOrderPayment:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改商品订单支付对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body @close="cancel" draggable>
      <el-form ref="goodsOrderPaymentFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="订单id" prop="orderId">
          <el-input v-model="form.orderId" placeholder="请输入订单id" />
        </el-form-item>
        <el-form-item label="支付方式" prop="paymentMethod">
          <el-select v-model="form.paymentMethod" placeholder="请选择支付方式">
            <el-option
                v-for="dict in mall_payment_method"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="支付状态" prop="paymentStatus">
          <el-radio-group v-model="form.paymentStatus">
            <el-radio
              v-for="dict in mall_payment_slip_status"
              :key="dict.value"
              :value="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="支付流水号" prop="paymentId">
          <el-input v-model="form.paymentId" placeholder="请输入支付流水号" />
        </el-form-item>
        <el-form-item label="卡号" prop="cardCode">
          <el-input v-model="form.cardCode" placeholder="请输入卡号" />
        </el-form-item>
        <el-form-item label="卡次" prop="cardInstance">
          <el-input v-model="form.cardInstance" placeholder="请输入卡次" />
        </el-form-item>
        <el-form-item label="支付时间" prop="paymentTime">
          <el-date-picker clearable
            v-model="form.paymentTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择支付时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="支付金额" prop="paymentAmount">
          <el-input v-model="form.paymentAmount" placeholder="请输入支付金额" />
        </el-form-item>
        <el-form-item label="退款流水号" prop="refundId">
          <el-input v-model="form.refundId" placeholder="请输入退款流水号" />
        </el-form-item>
        <el-form-item label="退款时间" prop="refundTime">
          <el-date-picker clearable
            v-model="form.refundTime"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择退款时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GoodsOrderPayment" lang="ts">
import { listGoodsOrderPayment, getGoodsOrderPayment, delGoodsOrderPayment, addGoodsOrderPayment, updateGoodsOrderPayment } from '@/api/mall/goodsOrderPayment';
import { GoodsOrderPaymentVO, GoodsOrderPaymentQuery, GoodsOrderPaymentForm } from '@/api/mall/goodsOrderPayment/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_payment_method, mall_payment_slip_status } = toRefs<any>(proxy?.useDict('mall_payment_method', 'mall_payment_slip_status'));

const goodsOrderPaymentList = ref<GoodsOrderPaymentVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑

const dateRangePaymentTime = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeRefundTime = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const goodsOrderPaymentFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GoodsOrderPaymentForm = {
  id: undefined,
  orderId: undefined,
  paymentMethod: undefined,
  paymentStatus: undefined,
  paymentId: undefined,
  cardCode: undefined,
  cardInstance: undefined,
  paymentTime: undefined,
  paymentAmount: undefined,
  refundId: undefined,
  refundTime: undefined,
  remark: undefined,
}
const data = reactive<PageData<GoodsOrderPaymentForm, GoodsOrderPaymentQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderId: undefined,
    paymentMethod: undefined,
    paymentStatus: undefined,
    paymentId: undefined,
    cardCode: undefined,
    cardInstance: undefined,
    paymentAmount: undefined,
    refundId: undefined,
    updateBy: undefined,
    params: {
      paymentTime: undefined,
      refundTime: undefined,
      updateTime: undefined
    }
  },
  rules: {
    id: [
      { required: true, message: "id不能为空", trigger: "blur" }
    ],
    orderId: [
      { required: true, message: "订单id不能为空", trigger: "blur" }
    ],
    paymentMethod: [
      { required: true, message: "支付方式不能为空", trigger: "change" }
    ],
    paymentStatus: [
      { required: true, message: "支付状态不能为空", trigger: "change" }
    ],
    paymentId: [
      { required: true, message: "支付流水号不能为空", trigger: "blur" }
    ],
    cardCode: [
      { required: true, message: "卡号不能为空", trigger: "blur" }
    ],
    cardInstance: [
      { required: true, message: "卡次不能为空", trigger: "blur" }
    ],
    paymentTime: [
      { required: true, message: "支付时间不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询商品订单支付列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangePaymentTime.value, 'PaymentTime');
  proxy?.addDateRange(queryParams.value, dateRangeRefundTime.value, 'RefundTime');
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listGoodsOrderPayment(queryParams.value);
  goodsOrderPaymentList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  goodsOrderPaymentFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangePaymentTime.value = ['', ''];
  dateRangeRefundTime.value = ['', ''];
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: GoodsOrderPaymentVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加商品订单支付";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: GoodsOrderPaymentVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getGoodsOrderPayment(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改商品订单支付";
}

/** 提交按钮 */
const submitForm = () => {
  goodsOrderPaymentFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGoodsOrderPayment(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addGoodsOrderPayment(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: GoodsOrderPaymentVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除商品订单支付编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delGoodsOrderPayment(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('mall/goodsOrderPayment/export', {
    ...queryParams.value
  }, `goodsOrderPayment_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
