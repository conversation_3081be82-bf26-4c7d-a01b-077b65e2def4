<template>
  <div class="mb-2" style="display: flex; justify-content: space-between; align-items: center" gap="10px">
    <el-button type="primary" @click="handleAdd">新增</el-button>
    <el-tooltip content="刷新" placement="top">
      <el-link type="primary" icon="Refresh" @click="getList" />
    </el-tooltip>
  </div>
  <div>
    <el-table v-loading="loading" :data="combinedGoodsDetailList" border>
      <el-table-column label="id" prop="id" v-if="false" />
      <el-table-column label="明细首图" prop="goodsName" width="80">
        <template #default="scope">
          <image-preview :src="scope.row.detailGoodsVo.imagesUrl" :width="50" :height="50" />
        </template>
      </el-table-column>
      <el-table-column label="商品明细" prop="detailName" min-width="150" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ scope.row.detailGoodsVo.name }}</span>
          <br />
          <el-tooltip content="商品编码" placement="top">
            <span style="color: #999">{{ scope.row.detailGoodsVo.code }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="数量" prop="qty" min-width="80">
        <template #default="scope">
          <span>{{ scope.row.qty }}</span>
          <span style="padding-left: 10px; color: #999">{{ scope.row.detailGoodsVo.saleUnitLabel }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否计价" prop="isInPrice" min-width="80">
        <template #default="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.isInPrice" />
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="remark" min-width="200" show-overflow-tooltip />
      <el-table-column label="操作" class-name="small-padding fixed-width" width="100" fixed="right">
        <template #default="scope">
          <el-tooltip content="修改" placement="top">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mall:combinedGoodsDetail:edit']"></el-button>
          </el-tooltip>
          <el-tooltip content="剔除" placement="top">
            <el-button
              link
              type="primary"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['mall:combinedGoodsDetail:remove']"
            ></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </div>

  <!-- 添加或修改组合商品明细对话框 -->
  <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body destroy-on-close draggable>
    <el-form ref="combinedGoodsDetailFormRef" :model="form" :rules="rules" label-width="120px" label-position="top">
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="商品" prop="goodsId">
            <goods-select v-model="form.goodsId" style="width: 100%" :isCombinedGoods="'Y'" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="商品明细" prop="detailId">
            <goods-select v-model="form.detailId" style="width: 100%" :isCombinedGoods="'N'" :disabled="dialogEditStatus" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数量" prop="qty">
            <el-input-number v-model="form.qty" placeholder="请输入数量" :min="1" :max="9999" controls-position="right" :precision="0" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否计价" prop="isInPrice">
            <template #label>
              <span>是否计价</span>
              <el-tooltip content="不计价，则生成发票的时候不会出现在商品明细中" placement="top">
                <el-icon class="el-icon--right">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </template>
            <el-radio-group v-model="form.isInPrice">
              <el-radio v-for="dict in sys_yes_no" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" v-model="form.remark" placeholder="请输入备注" :rows="4" maxlength="200" show-word-limit />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-checkbox v-if="!dialogEditStatus" v-model="continueCreate" style="float: left; margin-top: 8px">继续新建</el-checkbox>
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {
  listCombinedGoodsDetail,
  getCombinedGoodsDetail,
  delCombinedGoodsDetail,
  addCombinedGoodsDetail,
  updateCombinedGoodsDetail
} from '@/api/mall/combinedGoodsDetail';
import { CombinedGoodsDetailVO, CombinedGoodsDetailQuery, CombinedGoodsDetailForm } from '@/api/mall/combinedGoodsDetail/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_yes_no } = toRefs<any>(proxy?.useDict('sys_yes_no'));

const combinedGoodsDetailList = ref<CombinedGoodsDetailVO[]>([]);
const loading = ref(true);
const buttonLoading = ref(false);
const combinedGoodsDetailFormRef = ref<ElFormInstance>();

const dialogEditStatus = ref(false); //false:新增，true：编辑
const continueCreate = ref(true); // 是否继续创建，对应复选框的值

const props = defineProps<{
  goodsId: string | number;
}>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: CombinedGoodsDetailForm = {
  id: undefined,
  goodsId: props.goodsId,
  detailId: undefined,
  qty: undefined,
  saleUnitLabel: undefined,
  isInPrice: 'Y',
  remark: undefined
};

const data = reactive<PageData<CombinedGoodsDetailForm, CombinedGoodsDetailQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    goodsId: props.goodsId
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    goodsId: [{ required: true, message: '商品不能为空', trigger: 'blur' }],
    detailId: [{ required: true, message: '商品明细不能为空', trigger: 'blur' }],
    qty: [{ required: true, message: '数量不能为空', trigger: 'blur' }],
    isInPrice: [{ required: true, message: '是否计价，关联字典「sys_yes_no」不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

const getList = async () => {
  loading.value = true;
  const res = await listCombinedGoodsDetail(queryParams.value);
  combinedGoodsDetailList.value = res.rows;
  loading.value = false;
  console.log(props.goodsId);
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  combinedGoodsDetailFormRef.value?.resetFields();
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  dialogEditStatus.value = false;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加组合商品明细';
  dialogEditStatus.value = false;
};

/** 修改按钮操作 */
const handleUpdate = async (row?: CombinedGoodsDetailVO) => {
  reset();
  const res = await getCombinedGoodsDetail(row?.id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改组合商品明细';
  dialogEditStatus.value = true;
};

/** 提交按钮 */
const submitForm = () => {
  combinedGoodsDetailFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateCombinedGoodsDetail(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addCombinedGoodsDetail(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      if (continueCreate.value && !dialogEditStatus.value) {
        const currentForm = form;
        reset();
        form.value.goodsId = currentForm.value.goodsId;
        form.value.isInPrice = currentForm.value.isInPrice;
      } else {
        dialogEditStatus.value = false;
        dialog.visible = false;
      }
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: CombinedGoodsDetailVO) => {
  await proxy?.$modal.confirm('是否确认删除组合商品明细编号为"' + row?.detailName + '"的数据项？').finally(() => (loading.value = false));
  await delCombinedGoodsDetail(row?.id);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

onMounted(() => {
  getList();
});
</script>
