<template>
  <el-dialog :title="dialog.title" v-model="dialog.visible" width="60%" append-to-body destroy-on-close @close="handleClose" draggable>
    <combined-goods-table :goodsId="goodsId" />
  </el-dialog>
</template>

<script setup lang="ts">
import { listGoods, getGoods, delGoods, addGoods, updateGoods } from '@/api/mall/goods';
import { GoodsVO, GoodsQuery, GoodsForm } from '@/api/mall/goods/types';
import { ref, watch } from 'vue';
import CombinedGoodsTable from './CombinedGoodsTable.vue';

const goodsId = ref<string | number | undefined>();
const goodsVo = ref<GoodsVO>();

const dialog = ref({
  visible: false,
  title: '组合商品明细'
});

const emit = defineEmits(['refresh']);

const handleClose = () => {
  dialog.value.visible = false;
  emit('refresh');
};

const openDialog = async (id: string | number) => {
  const res = await getGoods(id);
  goodsVo.value = res.data;
  dialog.value.title = '组合商品明细 ( ' + goodsVo.value.name + ' )';
  dialog.value.visible = true;
  goodsId.value = id;
};

defineExpose({
  openDialog
});
</script>
