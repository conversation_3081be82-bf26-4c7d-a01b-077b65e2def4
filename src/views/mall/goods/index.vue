<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="120px" class="search-form-container">
            <el-form-item label="商品名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入商品名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="商品编码" prop="code">
              <el-input v-model="queryParams.code" placeholder="请输入商品编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否erp商品" prop="isErpGoods" v-if="showMoreCondition">
              <el-select v-model="queryParams.isErpGoods" placeholder="请选择是否erp商品" clearable>
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="erp商品编码" prop="erpCode" v-if="showMoreCondition">
              <el-input v-model="queryParams.erpCode" placeholder="请输入erp商品编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="69码" prop="barcode" v-if="showMoreCondition">
              <el-input v-model="queryParams.barcode" placeholder="请输入69码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="采购单位" prop="purchaseUnit" v-if="showMoreCondition">
              <el-select v-model="queryParams.purchaseUnit" placeholder="请选择采购单位" clearable>
                <el-option v-for="dict in mall_goods_unit" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否组合商品" prop="isCombinedGoods">
              <el-select v-model="queryParams.isCombinedGoods" placeholder="请选择是否组合商品" clearable>
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="商品类型" prop="goodsType">
              <el-select v-model="queryParams.goodsType" placeholder="请选择商品类型" clearable>
                <el-option v-for="dict in mall_goods_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="商品等级" prop="goodsGrade" v-if="showMoreCondition">
              <el-select v-model="queryParams.goodsGrade" placeholder="请选择商品等级" clearable @change="handleQuery">
                <el-option v-for="dict in mall_goods_grade" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否显示等级" prop="isShowGrade" v-if="showMoreCondition">
              <el-select v-model="queryParams.isShowGrade" placeholder="请选择是否显示等级" clearable @change="handleQuery">
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="销售单位" prop="saleUnit" v-if="showMoreCondition">
              <el-select v-model="queryParams.saleUnit" placeholder="请输入销售单位" clearable @change="handleQuery">
                <el-option v-for="dict in mall_goods_unit" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="包装类型" prop="packageType">
              <el-select v-model="queryParams.packageType" placeholder="请选择包装类型" clearable @change="handleQuery">
                <el-option v-for="dict in mall_package_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="品牌" prop="brandKey" v-if="showMoreCondition">
              <el-select v-model="queryParams.brandKey" placeholder="请选择品牌" clearable @change="handleQuery">
                <el-option v-for="dict in mall_goods_brand" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否显示" prop="isShow" v-if="showMoreCondition">
              <el-select v-model="queryParams.isShow" placeholder="请选择是否显示等级" clearable @change="handleQuery">
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="销售状态" prop="saleStatus">
              <el-select v-model="queryParams.saleStatus" placeholder="请选择销售状态" clearable @change="handleQuery">
                <el-option v-for="dict in mall_sale_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="商品标签" prop="tags" v-if="showMoreCondition">
              <el-select v-model="queryParams.tags" placeholder="请选择商品标签" clearable @change="handleQuery">
                <el-option v-for="dict in mall_goods_tag" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="商品角标" prop="subImageId" v-if="showMoreCondition">
              <sub-image-select v-model="queryParams.subImageId" @change="handleQuery" />
            </el-form-item>
            <el-form-item label="通用商品详情" prop="generalDetailId" v-if="showMoreCondition">
              <general-detail-select v-model="queryParams.generalDetailId" @change="handleQuery" />
            </el-form-item>
            <el-form-item label="前端分类" prop="frontCategoryIds" v-if="showMoreCondition">
              <front-category-select v-model="queryParams.frontCategoryIds" @change="handleQuery" />
            </el-form-item>
            <el-form-item label="后台分类" prop="backCategoryId" v-if="showMoreCondition">
              <back-category-select v-model="queryParams.backCategoryId" @change="handleQuery" />
            </el-form-item>
            <el-form-item label="更新人" prop="updateBy" v-if="showMoreCondition">
              <el-select v-model="queryParams.updateBy" placeholder="请输入更新人" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mall:goods:add']">新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['mall:goods:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['mall:goods:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['mall:goods:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table
        v-loading="loading"
        :data="goodsList"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        border
        :row-class-name="tableRowClassName"
      >
        <el-table-column type="index" width="55" label="序号" v-if="false" />
        <el-table-column label="排序" prop="sort" sortable="custom" align="center" width="80">
          <template #default="scope">
            <div class="sort-cell" @mouseenter="scope.row.showSortEdit = true" @mouseleave="scope.row.showSortEdit = false">
              <el-popover
                placement="top"
                :width="200"
                trigger="click"
                v-model:visible="scope.row.showSortPopover"
                @show="scope.row.newSort = scope.row.sort"
              >
                <template #reference>
                  <div class="sort-value-wrapper">
                    <span>{{ scope.row.sort }}</span>
                    <el-icon v-if="scope.row.showSortEdit" class="edit-icon">
                      <Edit />
                    </el-icon>
                  </div>
                </template>
                <div class="sort-popover-content">
                  <el-input
                    v-model.number="scope.row.newSort"
                    type="number"
                    placeholder="请输入排序值"
                    :min="1"
                    :max="999999"
                    @keyup.enter="confirmSortUpdate(scope.row)"
                  />
                  <div class="sort-popover-buttons">
                    <el-button size="small" @click="scope.row.showSortPopover = false">取消</el-button>
                    <el-button size="small" type="primary" @click="confirmSortUpdate(scope.row)">确定</el-button>
                  </div>
                </div>
              </el-popover>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="商品图片" align="center" prop="imagesUrl" width="80">
          <template #default="scope">
            <image-preview :src="scope.row.imagesUrl" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="商品名称" prop="name" :show-overflow-tooltip="true" width="200">
          <template #default="scope">
            <div>
              <el-tooltip content="商品名称" placement="top">
                <span>{{ scope.row.name }}</span>
              </el-tooltip>
            </div>
            <div style="font-size: 12px; color: #999">
              <el-tooltip content="商品编码" placement="top">
                <span>{{ scope.row.code }}</span>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="销售单位" prop="saleUnit">
          <template #default="scope">
            <dict-tag :options="mall_goods_unit" :value="scope.row.saleUnit" />
          </template>
        </el-table-column>
        <el-table-column label="商品类型" prop="goodsType">
          <template #default="scope">
            <dict-tag :options="mall_goods_type" :value="scope.row.goodsType" />
          </template>
        </el-table-column>
        <el-table-column label="销售状态" prop="saleStatus" width="120">
          <template #default="scope">
            <dict-tag :options="mall_sale_status" :value="scope.row.saleStatus" />
          </template>
        </el-table-column>
        <el-table-column label="是否组合商品" prop="isCombinedGoods" width="120">
          <template #default="scope">
            <template style="display: flex; align-items: center; gap: 10px">
              <dict-tag :options="sys_yes_no" :value="scope.row.isCombinedGoods" />
              <el-link v-if="scope.row.isCombinedGoods === 'Y'" type="primary" @click="handleOpenCombinedGoodsDialog(scope.row)">
                <el-tooltip content="明细数量，立即维护？" placement="top">
                  <span>{{ scope.row.combinedGoodsDetailCount }}</span>
                </el-tooltip>
              </el-link>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="库存" prop="stockQty" width="100" sortable="custom" />
        <el-table-column label="零售价（元）" prop="currentSalePrice" width="160" sortable="custom">
          <template #default="scope">
            {{ centToYuan(scope.row.currentSalePrice) || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="市场价（元）" prop="currentMarketPrice" width="160" sortable="custom">
          <template #default="scope">
            {{ scope.row.currentMarketPrice === 0 ? '--' : centToYuan(scope.row.currentMarketPrice) }}
          </template>
        </el-table-column>
        <el-table-column label="成本价（元）" prop="currentCostPrice" width="160" sortable="custom">
          <template #default="scope">
            {{ scope.row.currentCostPrice === 0 ? '--' : centToYuan(scope.row.currentCostPrice) }}
          </template>
        </el-table-column>
        <el-table-column label="是否erp商品" prop="isErpGoods" width="100">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isErpGoods" />
          </template>
        </el-table-column>
        <el-table-column label="erp商品编码" prop="erpCode" v-if="showMoreCondition" min-width="150" />
        <el-table-column label="69码" prop="barcode" v-if="showMoreCondition" min-width="150" />
        <el-table-column label="采购单位" prop="purchaseUnit" min-width="100">
          <template #default="scope">
            <dict-tag :options="mall_goods_unit" :value="scope.row.purchaseUnit" />
          </template>
        </el-table-column>
        <el-table-column label="商品等级" prop="goodsGrade">
          <template #default="scope">
            <dict-tag :options="mall_goods_grade" :value="scope.row.goodsGrade" />
          </template>
        </el-table-column>
        <el-table-column label="是否显示等级" prop="isShowGrade" width="120">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isShowGrade" />
          </template>
        </el-table-column>
        <el-table-column label="商品重量(kg)" prop="unitWeight" width="120" />
        <el-table-column label="包装类型" prop="packageType" width="100">
          <template #default="scope">
            <dict-tag :options="mall_package_type" :value="scope.row.packageType" />
          </template>
        </el-table-column>
        <el-table-column label="品牌" prop="brandKey" width="100">
          <template #default="scope">
            <dict-tag :options="mall_goods_brand" :value="scope.row.brandKey" />
          </template>
        </el-table-column>
        <el-table-column label="是否显示" prop="isShow" width="120">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isShow" />
          </template>
        </el-table-column>
        <el-table-column label="商品标签" prop="tags" width="120">
          <template #default="scope">
            <dict-tag :options="mall_goods_tag" :value="scope.row.tags" />
          </template>
        </el-table-column>
        <el-table-column label="前端分类" prop="frontCategoryLabel" width="120" />
        <el-table-column label="后台分类" prop="backCategoryLabel" width="120" />
        <el-table-column label="更新人" prop="updateNickName" width="150" />
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" width="150" fixed="right">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mall:goods:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="复制" placement="top">
              <el-button link type="primary" icon="CopyDocument" @click="handleCopy(scope.row)" v-hasPermi="['mall:goods:add']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mall:goods:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改商品对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="80%" append-to-body @close="cancel" draggable destroy-on-close>
      <el-row :gutter="10">
        <!-- 左侧锚点导航 -->
        <el-col :span="4">
          <el-anchor :default-active="currentAnchor">
            <el-anchor-link href="#basic" title="基础信息" />
            <el-anchor-link href="#combined" title="组合商品明细" v-if="form.isCombinedGoods === 'Y'" />
            <el-anchor-link href="#image" title="商品图片" />
            <el-anchor-link href="#erp" title="ERP商品信息" v-if="form.isErpGoods === 'Y'" />
            <el-anchor-link href="#stock" title="库存和价格" />
            <el-anchor-link href="#detail" title="商品详情" />
            <el-anchor-link href="#note" title="备注信息" />
          </el-anchor>
        </el-col>

        <!-- 右侧表单内容 -->
        <el-col :span="20">
          <el-form ref="goodsFormRef" :model="form" :rules="rules" label-width="150px" label-position="top" scroll-to-error>
            <!-- 基础信息 -->
            <div id="basic" class="form-section">
              <div class="section-title">
                <span class="title-text">基础信息</span>
              </div>
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-form-item label="是否erp商品" prop="isErpGoods">
                    <el-radio-group v-model="form.isErpGoods" :disabled="dialogEditStatus" @change="handleErpGoodsChange">
                      <el-radio v-for="dict in sys_yes_no" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="是否组合商品" prop="isCombinedGoods">
                    <el-radio-group
                      v-model="form.isCombinedGoods"
                      :disabled="isCombinedGoodsDisabled || dialogEditStatus"
                      @change="handleCombinedGoodsChange"
                    >
                      <el-radio v-for="dict in sys_yes_no" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="商品类型" prop="goodsType">
                    <dict-select v-model="form.goodsType" dict-key="mall_goods_type" placeholder="请选择商品类型" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="包装类型" prop="packageType">
                    <dict-select v-model="form.packageType" dict-key="mall_package_type" placeholder="请选择包装类型" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="商品名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入商品名称" maxlength="50" show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="商品编码" prop="code">
                    <template #label>
                      <span> 商品编码 </span>
                      <el-tooltip content="了解更多商品编码描述" placement="top">
                        <el-icon @click="openLink('https://rcn3q3ujmqdd.feishu.cn/docx/KREPdBnjNo5Nafx17hmcY7OQnbb?from=from_copylink')">
                          <QuestionFilled />
                        </el-icon>
                      </el-tooltip>
                    </template>
                    <el-input v-model="form.code" placeholder="不输入则系统自动生成" maxlength="20" show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="卖点" prop="sellPoint">
                    <el-input v-model="form.sellPoint" placeholder="请输入卖点" maxlength="100" show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="商品等级" prop="goodsGrade">
                    <dict-select v-model="form.goodsGrade" dict-key="mall_goods_grade" placeholder="请选择商品等级" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="是否显示等级" prop="isShowGrade">
                    <el-radio-group v-model="form.isShowGrade">
                      <el-radio v-for="dict in sys_yes_no" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="销售单位" prop="saleUnit">
                    <dict-select v-model="form.saleUnit" dict-key="mall_goods_unit" placeholder="请选择销售单位" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="商品重量(kg)" prop="unitWeight">
                    <el-input-number
                      v-model="form.unitWeight"
                      placeholder="单位(kg)"
                      :min="0"
                      :max="999999"
                      controls-position="right"
                      :precision="3"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="品牌" prop="brandKey">
                    <dict-select v-model="form.brandKey" dict-key="mall_goods_brand" placeholder="请选择品牌" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="销售状态" prop="saleStatus">
                    <el-radio-group v-model="form.saleStatus">
                      <el-radio v-for="dict in mall_sale_status" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="排序" prop="sort">
                    <el-input-number v-model="form.sort" placeholder="请输入排序" :min="1" :max="999999" :precision="0" controls-position="right" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="商品标签" prop="tags">
                    <dict-select v-model="form.tags" dict-key="mall_goods_tag" placeholder="请选择商品标签" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="前端分类" prop="frontCategoryIds">
                    <template #label>
                      <span> 前端分类 </span>
                      <el-tooltip content="打开前端分类" placement="top" v-if="form.frontCategoryIds">
                        <el-icon @click="goToFrontCategoryInEdit">
                          <Edit />
                        </el-icon>
                      </el-tooltip>
                      <el-tooltip content="新增前端分类" placement="top" v-else>
                        <el-icon @click="goToFrontCategoryInAdd">
                          <Plus />
                        </el-icon>
                      </el-tooltip>
                    </template>
                    <front-category-select v-model="form.frontCategoryIds" style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="后台分类" prop="backCategoryId">
                    <template #label>
                      <span> 后台分类 </span>
                      <el-tooltip content="打开后台分类" placement="top" v-if="form.backCategoryId">
                        <el-icon @click="goToBackCategoryInEdit">
                          <Edit />
                        </el-icon>
                      </el-tooltip>
                      <el-tooltip content="新增后台分类" placement="top" v-else>
                        <el-icon @click="goToBackCategoryInAdd">
                          <Plus />
                        </el-icon>
                      </el-tooltip>
                    </template>
                    <back-category-select v-model="form.backCategoryId" style="width: 100%" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 组合商品信息 -->
            <div id="combined" class="form-section" v-if="form.isCombinedGoods === 'Y'">
              <div class="section-title">
                <span class="title-text">组合商品明细</span>
              </div>
              <div v-if="form.id">
                <CombinedGoodsTable :goodsId="form.id" />
              </div>
              <div v-else style="text-align: center; color: #999; font-size: 14px">创建商品后，再添加商品明细</div>
            </div>

            <!-- 商品图片 -->
            <div id="image" class="form-section">
              <div class="section-title">
                <span class="title-text">商品图片</span>
              </div>
              <el-form-item label="商品图片" prop="images"> <image-upload v-model="form.images" :limit="6" /> </el-form-item>

              <!-- <el-form-item label="商品视频" prop="video">
                <video-upload v-model="form.video" :limit="1" />
              </el-form-item> -->
            </div>

            <!-- ERP商品信息 -->
            <div id="erp" class="form-section" v-if="form.isErpGoods === 'Y'">
              <div class="section-title">
                <span class="title-text">ERP商品信息</span>
              </div>
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-form-item label="erp商品编码" prop="erpCode" :required="form.isErpGoods === 'Y'">
                    <el-input v-model="form.erpCode" placeholder="请输入erp商品编码" maxlength="20" show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="69码" prop="barcode">
                    <el-input v-model="form.barcode" placeholder="请输入69码" maxlength="13" show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="采购单位" prop="purchaseUnit" :required="form.isErpGoods === 'Y'">
                    <el-select v-model="form.purchaseUnit" placeholder="请选择采购单位">
                      <el-option v-for="dict in mall_goods_unit" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 库存和价格 -->
            <div id="stock" class="form-section">
              <div class="section-title">
                <span class="title-text">库存和价格</span>
              </div>
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-form-item label="库存" prop="stockQty">
                    <el-input-number v-model="form.stockQty" placeholder="请输入库存" :min="0" :max="999999" controls-position="right" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="已售库存">
                    <el-input v-model="form.stockQty" placeholder="已售库存" disabled />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="剩余库存">
                    <el-input v-model="form.stockQty" placeholder="剩余库存" disabled />
                  </el-form-item>
                </el-col>
                <el-col :span="6"></el-col>
                <el-col :span="6">
                  <el-form-item label="零售价格（元）" prop="currentSalePrice">
                    <!--                       v-model="form.currentSalePrice" -->
                    <el-input-number
                      placeholder="支持两位小数"
                      controls-position="right"
                      :min="0"
                      :max="9999.99"
                      :step="1"
                      :precision="2"
                      @change="(val) => (form.currentSalePrice = yuanToCent(val))"
                      :model-value="Number(centToYuan(form.currentSalePrice))"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="市场价格（元）" prop="currentMarketPrice">
                    <el-input-number
                      placeholder="支持两位小数"
                      controls-position="right"
                      :min="0"
                      :max="9999.99"
                      :step="1"
                      :precision="2"
                      @change="(val) => (form.currentMarketPrice = yuanToCent(val))"
                      :model-value="Number(centToYuan(form.currentMarketPrice))"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="成本价（元）" prop="currentCostPrice">
                    <el-input-number
                      placeholder="支持两位小数"
                      controls-position="right"
                      :min="0"
                      :max="9999.99"
                      :step="1"
                      :precision="2"
                      @change="(val) => (form.currentCostPrice = yuanToCent(val))"
                      :model-value="Number(centToYuan(form.currentCostPrice))"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 商品详情 -->
            <div id="detail" class="form-section">
              <div class="section-title">
                <span class="title-text">商品详情</span>
              </div>
              <el-row :gutter="10">
                <el-col :span="18">
                  <el-form-item label="商品详情" prop="detail">
                    <editor v-model="form.detail" :min-height="192" style="width: 95%" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-row>
                    <el-col :span="24">
                      <el-form-item label="商品角标" prop="subImageId">
                        <template #label>
                          <span> 商品角标 </span>
                          <el-tooltip content="打开商品角标" placement="top" v-if="form.subImageId">
                            <el-icon @click="goToSubImageInEdit">
                              <Edit />
                            </el-icon>
                          </el-tooltip>
                          <el-tooltip content="新增商品角标" placement="top" v-else>
                            <el-icon @click="goToSubImageInAdd">
                              <Plus />
                            </el-icon>
                          </el-tooltip>
                        </template>
                        <sub-image-select v-model="form.subImageId" style="width: 100%" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item label="通用商品详情" prop="generalDetailId">
                        <template #label>
                          <span>
                            通用商品详情
                            <el-tooltip content="打开通用商品详情" placement="top" v-if="form.generalDetailId">
                              <el-icon @click="goToGeneralDetailInEdit">
                                <Edit />
                              </el-icon>
                            </el-tooltip>
                            <el-tooltip content="新增通用商品详情" placement="top" v-else>
                              <el-icon @click="goToGeneralDetailInAdd">
                                <Plus />
                              </el-icon>
                            </el-tooltip>
                          </span>
                        </template>
                        <general-detail-select v-model="form.generalDetailId" style="width: 100%" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item label="分享描述" prop="shareDesc">
                        <el-input type="textarea" v-model="form.shareDesc" placeholder="请输入分享描述" :rows="5" maxlength="200" show-word-limit />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </div>

            <!-- 备注信息 -->
            <div id="note" class="form-section">
              <div class="section-title">
                <span class="title-text">备注信息</span>
              </div>
              <el-row :gutter="10">
                <el-col :span="24">
                  <el-form-item label="备注" prop="remark">
                    <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" :rows="5" maxlength="200" show-word-limit />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <CombinedGoodsDialog :goodsId="goodsId" ref="combinedGoodsDialogRef" @refresh="getList" />
  </div>
</template>

<script setup name="Goods" lang="ts">
import { listGoods, getGoods, delGoods, addGoods, updateGoods, checkGoodsNameUnique, checkGoodsCodeUnique } from '@/api/mall/goods';
import { GoodsVO, GoodsQuery, GoodsForm } from '@/api/mall/goods/types';
import { centToYuan, yuanToCent } from '@/utils/moneyUtils';
import CombinedGoodsTable from './components/CombinedGoodsTable.vue';
import CombinedGoodsDialog from './components/CombinedGoodsDialog.vue';
import { onMounted, getCurrentInstance, ref, reactive, toRefs, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import DictTag from '@/components/DictTag/index.vue';
import ImagePreview from '@/components/ImagePreview/index.vue';
import Editor from '@/components/Editor/index.vue';
import { parseTime } from '@/utils/ruoyi';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_package_type, mall_goods_type, mall_goods_grade, mall_goods_tag, sys_yes_no, mall_goods_unit, mall_goods_brand, mall_sale_status } =
  toRefs<any>(
    proxy?.useDict(
      'mall_package_type',
      'mall_goods_type',
      'mall_goods_grade',
      'mall_goods_tag',
      'sys_yes_no',
      'mall_goods_unit',
      'mall_goods_brand',
      'mall_sale_status'
    )
  );

const goodsList = ref<GoodsVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const combinedGoodsDialogRef = ref(null);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑
const isCombinedGoodsDisabled = ref(false); // 是否禁用组合商品
const goodsId = ref<string | number>(); // 商品id

const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const goodsFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const route = useRoute();
const router = useRouter();

const currentAnchor = ref('#basic');

// 检查唯一性查询体
const queryUnique: GoodsForm = {
  id: undefined,
  name: undefined,
  code: undefined
};

const initFormData: GoodsForm = {
  id: undefined,
  name: undefined,
  code: undefined,
  sellPoint: undefined,
  isErpGoods: 'Y',
  erpCode: undefined,
  barcode: undefined,
  purchaseUnit: undefined,
  isCombinedGoods: 'N',
  goodsType: undefined,
  goodsGrade: undefined,
  isShowGrade: 'Y',
  saleUnit: undefined,
  unitWeight: undefined,
  packageType: undefined,
  brandKey: '001',
  isShow: 'Y',
  saleStatus: '0',
  sort: 1,
  tags: undefined,
  images: undefined,
  subImageId: undefined,
  // video: undefined,
  generalDetailId: undefined,
  detail: undefined,
  remark: undefined,
  frontCategoryIds: undefined,
  backCategoryId: undefined,
  stockQty: 0,
  currentSalePrice: undefined,
  currentMarketPrice: undefined,
  shareDesc: undefined,
  currentCostPrice: undefined
};
const data = reactive<PageData<GoodsForm, GoodsQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    code: undefined,
    isErpGoods: undefined,
    erpCode: undefined,
    barcode: undefined,
    purchaseUnit: undefined,
    isCombinedGoods: undefined,
    goodsType: undefined,
    goodsGrade: undefined,
    isShowGrade: undefined,
    saleUnit: undefined,
    unitWeight: undefined,
    packageType: undefined,
    brandKey: undefined,
    isShow: undefined,
    saleStatus: undefined,
    tags: undefined,
    subImageId: undefined,
    generalDetailId: undefined,
    frontCategoryIds: undefined,
    backCategoryId: undefined,
    stockQty: undefined,
    currentSalePrice: undefined,
    currentMarketPrice: undefined,
    shareDesc: undefined,
    updateBy: undefined,
    orderByColumn: undefined,
    isAsc: 'desc',
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    name: [
      { required: true, message: '商品名称不能为空', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value) {
            queryUnique.name = form.value.name;
            queryUnique.id = form.value.id;
            checkGoodsNameUnique(queryUnique)
              .then((res) => {
                if (res.data) {
                  callback(new Error('商品名称已存在'));
                } else {
                  callback();
                }
              })
              .catch(() => {
                callback(new Error('验证商品名称失败'));
              });
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ],
    code: [
      // 系统自动编码规则为商品编码不能为ZH或者DP开头，后面9位为数字的字符串；所有手动输入的时候不能使用这个规则
      {
        validator: (rule, value, callback) => {
          if (value) {
            // 检查是否以ZH或DP开头，仅在新增时校验
            if (!form.value.id && (value.startsWith('ZH') || value.startsWith('DP'))) {
              callback(new Error('手动输入的商品编码不能以ZH或DP开头'));
              return;
            }
            // 检查是否已存在
            queryUnique.code = form.value.code;
            queryUnique.id = form.value.id;
            checkGoodsCodeUnique(queryUnique)
              .then((res) => {
                if (res.data) {
                  callback(new Error('商品编码已存在'));
                } else {
                  callback();
                }
              })
              .catch(() => {
                callback(new Error('验证商品编码失败'));
              });
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ],
    isErpGoods: [{ required: true, message: '是否erp商品不能为空', trigger: 'change' }],
    isCombinedGoods: [{ required: true, message: '是否组合商品不能为空', trigger: 'change' }],
    saleUnit: [{ required: true, message: '销售单位不能为空', trigger: 'change' }],
    goodsType: [{ required: true, message: '商品类型不能为空', trigger: 'change' }],
    goodsGrade: [{ required: true, message: '商品等级不能为空', trigger: 'change' }],
    isShowGrade: [{ required: true, message: '是否显示等级不能为空', trigger: 'change' }],
    isShow: [{ required: true, message: '是否显示等级不能为空', trigger: 'change' }],
    saleStatus: [{ required: true, message: '销售状态不能为空', trigger: 'change' }],
    images: [{ required: true, message: '商品图片不能为空', trigger: 'blur' }],
    currentSalePrice: [{ required: true, message: '零售价不能为空', trigger: 'blur' }]
    // currentMarketPrice: [{ min: 0, max: 9999, message: '单价不超过9999元,不能为负数', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
const { loadUserList, userOptions } = useSysUserSelect();

/** 查询商品列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listGoods(queryParams.value);
  goodsList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  dialogEditStatus.value = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  goodsFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: GoodsVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加商品';
  dialogEditStatus.value = false;
  scrollToBasic();
};

/** 修改按钮操作 */
const handleUpdate = async (row?: GoodsVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getGoods(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改商品';
  dialogEditStatus.value = true;
  scrollToBasic();
};

/** 复制按钮操作 */
const handleCopy = async (row?: GoodsVO) => {
  reset();
  const res = await getGoods(row?.id);
  Object.assign(form.value, res.data);
  form.value.id = undefined;
  form.value.code = undefined;
  form.value.saleStatus = '1';
  dialog.visible = true;
  dialog.title = '复制新建';
  dialogEditStatus.value = false;
  scrollToBasic();
};

/**
 * 滚动到basic
 */
const scrollToBasic = () => {
  currentAnchor.value = '#basic';
  nextTick(() => {
    document.getElementById('basic')?.scrollIntoView({ behavior: 'smooth' });
  });
  console.log('scrollToBasic');
};

/** 提交按钮 */
const submitForm = () => {
  console.log('form.value', form.value);
  goodsFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGoods(form.value).finally(() => (buttonLoading.value = false));
      } else {
        try {
          const res = await addGoods(form.value);
          if (form.value.isCombinedGoods === 'Y') {
            handleOpenCombinedGoodsDialog(res.data);
            return;
          }
        } catch (error) {
          console.error('添加商品失败:', error);
          proxy?.$modal.msgError('添加失败');
          return;
        } finally {
          buttonLoading.value = false;
        }
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: GoodsVO) => {
  await proxy?.$modal.confirm('是否确认删除商品名称为"' + row.name + '"的商品？').finally(() => (loading.value = false));
  await delGoods(row?.id);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'mall/goods/export',
    {
      ...queryParams.value
    },
    `goods_${new Date().getTime()}.xlsx`
  );
};

// 打开通用详情弹窗（编辑）
const goToGeneralDetailInEdit = () => {
  const routeUrl = `/mall/goods/set/generalDetail?id=${form.value.generalDetailId}&openEdit=true`;
  window.open(routeUrl, '_blank');
};

// 打开通用详情弹窗（新建）
const goToGeneralDetailInAdd = () => {
  const routeUrl = `/mall/goods/set/generalDetail?openAdd=true`;
  window.open(routeUrl, '_blank');
};

// 打开商品角标弹窗（编辑）
const goToSubImageInEdit = () => {
  const routeUrl = `/mall/goods/set/subImage?id=${form.value.subImageId}&openEdit=true`;
  window.open(routeUrl, '_blank');
};

// 打开商品角标弹窗（新建）
const goToSubImageInAdd = () => {
  const routeUrl = `/mall/goods/set/subImage?openAdd=true`;
  window.open(routeUrl, '_blank');
};

// 打开前台分类弹窗（编辑）
const goToFrontCategoryInEdit = () => {
  const routeUrl = `/mall/goods/set/frontCategory?id=${form.value.frontCategoryIds}&openEdit=true`;
  window.open(routeUrl, '_blank');
};

// 打开前台分类弹窗（新建）
const goToFrontCategoryInAdd = () => {
  const routeUrl = `/mall/goods/set/frontCategory?openAdd=true`;
  window.open(routeUrl, '_blank');
};

// 打开后台分类弹窗（编辑）
const goToBackCategoryInEdit = () => {
  const routeUrl = `/mall/goods/set/backCategory?id=${form.value.backCategoryId}&openEdit=true`;
  window.open(routeUrl, '_blank');
};

// 打开后台分类弹窗（新建）
const goToBackCategoryInAdd = () => {
  const routeUrl = `/mall/goods/set/backCategory?openAdd=true`;
  window.open(routeUrl, '_blank');
};

// 添加watch函数来监听isErpGoods的变化
watch(
  () => form.value.isErpGoods,
  (newVal) => {
    isCombinedGoodsDisabled.value = newVal === 'Y';
  }
);

// 打开组合商品弹窗
const handleOpenCombinedGoodsDialog = (row: GoodsVO) => {
  combinedGoodsDialogRef.value.openDialog(row.id);
  dialog.visible = false;
};

// 添加行样式方法
const tableRowClassName = ({ row }) => {
  if (row.isCombinedGoods === 'Y' && row.combinedGoodsDetailCount < 2) {
    return 'warning-row';
  }
  return '';
};

/** 是否erp商品变化 */
const handleErpGoodsChange = () => {
  if (form.value.isErpGoods === 'Y') {
    form.value.isCombinedGoods = 'N';
  }
};

/** 是否组合商品变化 */
const handleCombinedGoodsChange = () => {
  if (form.value.isCombinedGoods === 'Y') {
    form.value.isErpGoods = 'N';
  }
};

/** 打开链接 */
const openLink = (url: string) => {
  window.open(url, '_blank');
};

/** 监听要打开哪个弹窗 */
const handleOpenDialog = () => {
  // 1. 打开新增弹窗
  const openAdd = route.query.openAdd;
  if (openAdd === 'true') {
    nextTick(() => {
      handleAdd();
      router.replace({ query: {} });
    });
  }
  // 2. 打开修改弹窗
  const id = route.query.id;
  const openEdit = route.query.openEdit;
  console.log('id', id);
  if (id && openEdit === 'true') {
    nextTick(() => {
      handleUpdate({ id: id } as GoodsVO);
      router.replace({ query: {} });
    });
  }
};

/** 表格排序事件处理 */
const handleSortChange = (column: any) => {
  if (column.prop) {
    queryParams.value.orderByColumn = column.prop;
    queryParams.value.isAsc = column.order === 'ascending' ? 'asc' : 'desc';
    getList();
  }
};

/**
 * 确认更新排序
 */
const confirmSortUpdate = (row: any) => {
  if (!row.newSort) {
    row.newSort = row.sort;
    row.showSortPopover = false;
    return;
  }

  const newSort = parseInt(row.newSort);
  if (isNaN(newSort) || newSort <= 0 || newSort > 999999) {
    proxy?.$modal.msgError('请输入1-999999之间的整数');
    return;
  }

  if (newSort !== row.sort) {
    updateGoodsSort(row.id, newSort);
  }
  row.showSortPopover = false;
};

/** 更新商品排序 */
const updateGoodsSort = async (id: string | number, sort: number) => {
  loading.value = true;
  try {
    // 先获取完整的商品数据
    const res = await getGoods(id);
    const goodsData = res.data;

    // 更新排序值
    goodsData.sort = sort;

    // 提交更新
    await updateGoods(goodsData);
    proxy?.$modal.msgSuccess('排序更新成功');
    getList();
  } catch (error) {
    console.error('更新排序失败:', error);
    proxy?.$modal.msgError('更新排序失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  getList();
  loadUserList();
  handleOpenDialog();
});
</script>

<style scoped lang="scss">
@import '@/assets/styles/anchorform.scss';

:deep(.warning-row) {
  background-color: #fef0f0 !important;
}

.sort-cell {
  display: flex;
  justify-content: center;

  .sort-value-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;

    .edit-icon {
      margin-left: 5px;
      color: #409eff;
    }
  }
}

.sort-popover-content {
  padding: 5px;

  .sort-popover-buttons {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
    gap: 10px;
  }
}
</style>
