<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="商品" prop="goodsId">
              <el-input v-model="queryParams.goodsId" placeholder="请输入商品" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="商品明细" prop="detailId">
              <el-input v-model="queryParams.detailId" placeholder="请输入商品明细" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否计价" prop="isInPrice" v-if="showMoreCondition">
              <el-select v-model="queryParams.isInPrice" placeholder="请选择是否计价" clearable>
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" @change="handleQuery" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新人" prop="updateBy" v-if="showMoreCondition">
              <el-select v-model="queryParams.updateBy" placeholder="请输入更新人" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mall:combinedGoodsDetail:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['mall:combinedGoodsDetail:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['mall:combinedGoodsDetail:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['mall:combinedGoodsDetail:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="combinedGoodsDetailList" @selection-change="handleSelectionChange" border>
        <el-table-column type="index" width="55" label="序号" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="商品名称" prop="goodsName" min-width="150" />
        <el-table-column label="明细首图" prop="goodsName" width="80">
          <template #default="scope">
            <image-preview :src="scope.row.detailGoodsVo.imagesUrl" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="商品明细" prop="detailName" min-width="150" />
        <el-table-column label="数量" prop="qty" min-width="80">
          <template #default="scope">
            <span>{{ scope.row.qty }}</span>
            <span style="padding-left: 10px; color: #999">{{ scope.row.detailGoodsVo.saleUnitLabel }}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否计价" prop="isInPrice" min-width="80">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isInPrice" />
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="200" />
        <el-table-column label="更新人" prop="updateBy" min-width="150" />
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" width="150" fixed="right">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mall:combinedGoodsDetail:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['mall:combinedGoodsDetail:remove']"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改组合商品明细对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="combinedGoodsDetailFormRef" :model="form" :rules="rules" label-width="120px" label-position="top">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="商品" prop="goodsId">
              <goods-select v-model="form.goodsId" style="width: 100%" :isCombinedGoods="'Y'" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品明细" prop="detailId">
              <goods-select v-model="form.detailId" style="width: 100%" :isCombinedGoods="'N'" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数量" prop="qty">
              <el-input-number v-model="form.qty" placeholder="请输入数量" :min="1" :max="9999" controls-position="right" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否计价" prop="isInPrice">
              <template #label>
                <span>是否计价</span>
                <el-tooltip content="不计价，则生成发票的时候不会出现在商品明细中" placement="top">
                  <el-icon class="el-icon--right">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </template>
              <el-radio-group v-model="form.isInPrice">
                <el-radio v-for="dict in sys_yes_no" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input type="textarea" v-model="form.remark" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CombinedGoodsDetail" lang="ts">
import {
  listCombinedGoodsDetail,
  getCombinedGoodsDetail,
  delCombinedGoodsDetail,
  addCombinedGoodsDetail,
  updateCombinedGoodsDetail
} from '@/api/mall/combinedGoodsDetail';
import { CombinedGoodsDetailVO, CombinedGoodsDetailQuery, CombinedGoodsDetailForm } from '@/api/mall/combinedGoodsDetail/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_yes_no } = toRefs<any>(proxy?.useDict('sys_yes_no'));

const combinedGoodsDetailList = ref<CombinedGoodsDetailVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑

const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const combinedGoodsDetailFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: CombinedGoodsDetailForm = {
  id: undefined,
  goodsId: undefined,
  detailId: undefined,
  qty: undefined,
  isInPrice: 'Y',
  remark: undefined
};
const data = reactive<PageData<CombinedGoodsDetailForm, CombinedGoodsDetailQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    goodsId: undefined,
    detailId: undefined,
    isInPrice: undefined,
    updateBy: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    goodsId: [{ required: true, message: '商品不能为空', trigger: 'blur' }],
    detailId: [{ required: true, message: '商品明细不能为空', trigger: 'blur' }],
    qty: [{ required: true, message: '数量不能为空', trigger: 'blur' }],
    isInPrice: [{ required: true, message: '是否计价，关联字典「sys_yes_no」不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
const { loadUserList, userOptions } = useSysUserSelect();

/** 查询组合商品明细列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listCombinedGoodsDetail(queryParams.value);
  combinedGoodsDetailList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  combinedGoodsDetailFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: CombinedGoodsDetailVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加组合商品明细';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: CombinedGoodsDetailVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getCombinedGoodsDetail(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改组合商品明细';
};

/** 提交按钮 */
const submitForm = () => {
  combinedGoodsDetailFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateCombinedGoodsDetail(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addCombinedGoodsDetail(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: CombinedGoodsDetailVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除组合商品明细编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delCombinedGoodsDetail(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'mall/combinedGoodsDetail/export',
    {
      ...queryParams.value
    },
    `combinedGoodsDetail_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
  loadUserList();
});
</script>
