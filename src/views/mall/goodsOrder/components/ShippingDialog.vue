<template>
  <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body destroy-on-close @close="handleClose" draggable>
    <div class="mb-4">
      <el-text type="warning" size="large"> 提交快递单号则意味着告知系统该订单已发货，请谨慎操作！ </el-text>
    </div>
    <el-form ref="orderLogisticsFormRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="订单id" prop="orderId" v-if="false">
        <el-input v-model="form.orderId" placeholder="请输入订单id" disabled />
      </el-form-item>
      <el-form-item label="订单编号">
        <el-input :value="goodsOrder?.orderCode" disabled />
      </el-form-item>
      <el-form-item label="快递公司" prop="courierCompanyKey">
        <dict-select v-model="form.courierCompanyKey" dict-key="mall_courier_company" placeholder="请选择快递公司" />
      </el-form-item>
      <el-form-item label="快递单号" prop="courierNo">
        <el-input v-model="form.courierNo" maxlength="20" show-word-limit placeholder="请输入快递单号" />
      </el-form-item>
      <el-form-item label="发货时间" prop="changeStatusTime">
        <el-date-picker clearable v-model="form.changeStatusTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择发货时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="物流状态" prop="logisticsStatus">
        <el-select v-model="form.logisticsStatus" placeholder="请选择物流状态">
          <el-option v-for="dict in mall_logistics_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="发货备注" prop="remark">
        <el-input type="textarea" :rows="5" v-model="form.remark" placeholder="请输入备注" :maxlength="200" show-word-limit />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确定发货</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { addOrderLogistics } from '@/api/mall/orderLogistics';
import { OrderLogisticsQuery, OrderLogisticsForm } from '@/api/mall/orderLogistics/types';
import { GoodsOrderVO } from '@/api/mall/goodsOrder/types';
import { parseTime } from '@/utils/ruoyi';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_logistics_status } = toRefs<any>(proxy?.useDict('mall_logistics_status'));

const orderLogisticsFormRef = ref<ElFormInstance>();
const buttonLoading = ref(false);

const goodsOrder = ref<GoodsOrderVO>();

const dialog = ref({
  visible: false,
  title: '商品订单发货'
});

const emit = defineEmits(['refresh']);

const initFormData: OrderLogisticsForm = {
  id: undefined,
  orderId: undefined,
  courierCompanyKey: undefined,
  courierNo: undefined,
  changeStatusTime: undefined,
  logisticsStatus: '10',
  remark: undefined
};
const data = reactive<PageData<OrderLogisticsForm, OrderLogisticsQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderId: undefined,
    params: {
      changeStatusTime: undefined,
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    orderId: [{ required: true, message: '订单id不能为空', trigger: 'blur' }],
    courierCompanyKey: [{ required: true, message: '快递公司不能为空', trigger: 'blur' }],
    courierNo: [{ required: true, message: '快递单号不能为空', trigger: 'blur' }],
    changeStatusTime: [{ required: true, message: '发货时间不能为空', trigger: 'blur' }],
    logisticsStatus: [{ required: true, message: '物流状态不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);
const handleClose = () => {
  dialog.value.visible = false;
  emit('refresh');
};

const submitForm = () => {
  console.log(form.value);
  orderLogisticsFormRef.value?.validate(async (valid) => {
    if (valid) {
      buttonLoading.value = true;
      await addOrderLogistics(form.value).finally(() => (buttonLoading.value = false));
      proxy?.$modal.msgSuccess('操作成功');
      dialog.value.visible = false;
      emit('refresh');
    }
  });
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.value.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  orderLogisticsFormRef.value?.resetFields();
};
const openDialog = async (row: GoodsOrderVO) => {
  reset();
  goodsOrder.value = row;
  dialog.value.visible = true;
  form.value.orderId = row.id;
  form.value.changeStatusTime = parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}');
};

defineExpose({
  openDialog
});
</script>
