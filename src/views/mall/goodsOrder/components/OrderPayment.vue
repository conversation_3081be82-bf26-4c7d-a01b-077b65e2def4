<template>
  <div>
    <el-table v-loading="loading" :data="goodsOrderPaymentList" border>
      <el-table-column type="index" width="55" />
      <el-table-column label="id" prop="id" v-if="false" />
      <el-table-column label="支付方式" prop="paymentMethod" width="100">
        <template #default="scope">
          <dict-tag :options="mall_payment_method" :value="scope.row.paymentMethod" />
        </template>
      </el-table-column>
      <el-table-column label="支付状态" prop="paymentStatus" width="100">
        <template #default="scope">
          <dict-tag :options="mall_payment_slip_status" :value="scope.row.paymentStatus" />
        </template>
      </el-table-column>
      <el-table-column label="支付流水号" prop="paymentId" width="200" />
      <el-table-column label="卡号/账号" prop="cardCode" width="200" />
      <el-table-column label="卡次" prop="cardInstance" width="60" />
      <el-table-column label="支付时间" prop="paymentTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.paymentTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="支付金额(元)" prop="paymentAmount" width="100">
        <template #default="scope">
          <span>{{ centToYuan(scope.row.paymentAmount) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="退款流水号" prop="refundId" width="200" />
      <el-table-column label="退款时间" prop="refundTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.refundTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="remark" width="200" />
      <el-table-column label="更新时间" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" /> -->
  </div>
</template>

<script setup name="GoodsOrderPayment" lang="ts">
import {
  listGoodsOrderPayment,
  getGoodsOrderPayment,
  delGoodsOrderPayment,
  addGoodsOrderPayment,
  updateGoodsOrderPayment
} from '@/api/mall/goodsOrderPayment';
import { GoodsOrderPaymentVO, GoodsOrderPaymentQuery, GoodsOrderPaymentForm } from '@/api/mall/goodsOrderPayment/types';

import { centToYuan } from '@/utils/moneyUtils';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_payment_method, mall_payment_slip_status } = toRefs<any>(proxy?.useDict('mall_payment_method', 'mall_payment_slip_status'));

const goodsOrderPaymentList = ref<GoodsOrderPaymentVO[]>([]);
const loading = ref(true);
const total = ref(0);

const dateRangePaymentTime = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeRefundTime = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

// 接收父组件传递的商品订单id
const props = defineProps<{
  goodsOrderId: string | number;
}>();

const initFormData: GoodsOrderPaymentForm = {
  id: undefined,
  orderId: props.goodsOrderId,
  paymentMethod: undefined,
  paymentStatus: undefined,
  paymentId: undefined,
  cardCode: undefined,
  cardInstance: undefined,
  paymentTime: undefined,
  paymentAmount: undefined,
  refundId: undefined,
  refundTime: undefined,
  remark: undefined
};
const data = reactive<PageData<GoodsOrderPaymentForm, GoodsOrderPaymentQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderId: props.goodsOrderId,
    paymentMethod: undefined,
    paymentStatus: undefined,
    paymentId: undefined,
    cardCode: undefined,
    cardInstance: undefined,
    paymentAmount: undefined,
    refundId: undefined,
    updateBy: undefined,
    params: {
      paymentTime: undefined,
      refundTime: undefined,
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    orderId: [{ required: true, message: '订单id不能为空', trigger: 'blur' }],
    paymentMethod: [{ required: true, message: '支付方式不能为空', trigger: 'change' }],
    paymentStatus: [{ required: true, message: '支付状态不能为空', trigger: 'change' }],
    paymentId: [{ required: true, message: '支付流水号不能为空', trigger: 'blur' }],
    cardCode: [{ required: true, message: '卡号不能为空', trigger: 'blur' }],
    cardInstance: [{ required: true, message: '卡次不能为空', trigger: 'blur' }],
    paymentTime: [{ required: true, message: '支付时间不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询商品订单支付列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangePaymentTime.value, 'PaymentTime');
  proxy?.addDateRange(queryParams.value, dateRangeRefundTime.value, 'RefundTime');
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listGoodsOrderPayment(queryParams.value);
  goodsOrderPaymentList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

onMounted(() => {
  getList();
});
</script>
