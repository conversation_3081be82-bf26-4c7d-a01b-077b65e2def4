<template>
  <div class="mt-2">
    <div>
      <el-table v-loading="loading" :data="orderLogisticsList" @selection-change="handleSelectionChange" border>
        <el-table-column type="index" width="55" />
        <el-table-column label="id" prop="id" v-if="false" />
        <!-- <el-table-column label="订单id" prop="orderId" /> -->
        <el-table-column label="快递公司" prop="courierCompanyKey" width="150">
          <template #default="scope">
            <dict-tag :options="mall_courier_company" :value="scope.row.courierCompanyKey" />
          </template>
        </el-table-column>
        <el-table-column label="快递单号" prop="courierNo" min-width="250" />
        <el-table-column label="发货状态" prop="shippingStatus">
          <template #default="scope">
            <dict-tag :options="mall_courier_status" :value="scope.row.shippingStatus" />
          </template>
        </el-table-column>
        <el-table-column label="发货状态变化时间" prop="changeStatusTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.changeStatusTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="物流状态" prop="logisticsStatus">
          <template #default="scope">
            <dict-tag :options="mall_logistics_status" :value="scope.row.logisticsStatus" />
          </template>
        </el-table-column>
        <el-table-column label="物流变更时间" prop="logisticsStatusTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.logisticsStatusTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="快递费用" prop="courierAmount" />
        <el-table-column label="快递备注" prop="courierRemark" />
        <el-table-column label="收货时间" prop="receptionTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.receptionTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否签收" prop="isCheck">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isCheck" />
          </template>
        </el-table-column>
        <el-table-column label="收货类型" prop="receptionType">
          <template #default="scope">
            <dict-tag :options="mall_reception_type" :value="scope.row.receptionType" />
          </template>
        </el-table-column>
        <el-table-column label="仓库id" prop="warehouseId" />
        <el-table-column label="匹配类型" prop="matchType">
          <template #default="scope">
            <dict-tag :options="mall_order_match_warehouse" :value="scope.row.matchType" />
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" />
        <!-- <el-table-column label="更新人" prop="updateBy" /> -->
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mall:orderLogistics:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mall:orderLogistics:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <!-- <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" /> -->
    </div>
    <!-- 添加或修改快递物流对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="60%" append-to-body @close="cancel" draggable>
      <el-form ref="orderLogisticsFormRef" :model="form" :rules="rules" label-width="100px" label-position="top">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="订单id" prop="orderId">
              <el-input v-model="form.orderId" placeholder="请输入订单id" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="快递公司" prop="courierCompanyKey">
              <dict-select v-model="form.courierCompanyKey" dict-key="mall_courier_company" placeholder="请选择快递公司" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="快递单号" prop="courierNo">
              <el-input v-model="form.courierNo" placeholder="请输入快递单号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发货状态" prop="shippingStatus">
              <el-select v-model="form.shippingStatus" placeholder="请选择发货状态">
                <el-option v-for="dict in mall_courier_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发货变更" prop="changeStatusTime">
              <el-date-picker
                clearable
                v-model="form.changeStatusTime"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择发货变更"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物流状态" prop="logisticsStatus">
              <el-select v-model="form.logisticsStatus" placeholder="请选择物流状态">
                <el-option v-for="dict in mall_logistics_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物流变更" prop="logisticsStatusTime">
              <el-date-picker
                clearable
                v-model="form.logisticsStatusTime"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择物流变更"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="快递费用" prop="courierAmount">
              <el-input v-model="form.courierAmount" placeholder="请输入快递费用" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="快递备注" prop="courierRemark">
              <el-input v-model="form.courierRemark" placeholder="请输入快递备注" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收货时间" prop="receptionTime">
              <el-date-picker clearable v-model="form.receptionTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择收货时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否签收" prop="isCheck">
              <dict-select v-model="form.isCheck" dict-key="sys_yes_no" :show-footer="false" placeholder="请选择是否签收" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="快递单详情" prop="detail">
              <el-input v-model="form.detail" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收货类型" prop="receptionType">
              <el-select v-model="form.receptionType" placeholder="请选择收货类型">
                <el-option v-for="dict in mall_reception_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" prop="remark">
              <el-input type="textarea" v-model="form.remark" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OrderLogistics" lang="ts">
import { listOrderLogistics, getOrderLogistics, delOrderLogistics, addOrderLogistics, updateOrderLogistics } from '@/api/mall/orderLogistics';
import { OrderLogisticsVO, OrderLogisticsQuery, OrderLogisticsForm } from '@/api/mall/orderLogistics/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_courier_status, mall_reception_type, mall_order_match_warehouse, mall_logistics_status, mall_courier_company, sys_yes_no } = toRefs<any>(
  proxy?.useDict(
    'mall_courier_status',
    'mall_reception_type',
    'mall_order_match_warehouse',
    'mall_logistics_status',
    'mall_courier_company',
    'sys_yes_no'
  )
);

const orderLogisticsList = ref<OrderLogisticsVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑

const dateRangeChangeStatusTime = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const orderLogisticsFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

// 接收父组件传递的商品订单id
const props = defineProps<{
  goodsOrderId: string | number;
}>();

const initFormData: OrderLogisticsForm = {
  id: undefined,
  orderId: props.goodsOrderId,
  warehouseId: undefined,
  matchType: undefined,
  courierCompanyKey: undefined,
  courierNo: undefined,
  shippingStatus: undefined,
  changeStatusTime: undefined,
  logisticsStatus: undefined,
  logisticsStatusTime: undefined,
  courierAmount: undefined,
  courierRemark: undefined,
  receptionTime: undefined,
  isCheck: undefined,
  receptionType: undefined,
  detail: undefined,
  remark: undefined
};
const data = reactive<PageData<OrderLogisticsForm, OrderLogisticsQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderId: props.goodsOrderId,
    warehouseId: undefined,
    matchType: undefined,
    courierCompanyKey: undefined,
    courierNo: undefined,
    shippingStatus: undefined,
    logisticsStatus: undefined,
    logisticsStatusTime: undefined,
    courierAmount: undefined,
    courierRemark: undefined,
    receptionTime: undefined,
    isCheck: undefined,
    receptionType: undefined,
    updateBy: undefined,
    params: {
      changeStatusTime: undefined,
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    orderId: [{ required: true, message: '订单id不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询快递物流列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeChangeStatusTime.value, 'ChangeStatusTime');
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listOrderLogistics(queryParams.value);
  orderLogisticsList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  orderLogisticsFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeChangeStatusTime.value = ['', ''];
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: OrderLogisticsVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加快递物流';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: OrderLogisticsVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getOrderLogistics(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改快递物流';
};

/** 提交按钮 */
const submitForm = () => {
  orderLogisticsFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateOrderLogistics(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addOrderLogistics(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: OrderLogisticsVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除快递物流编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delOrderLogistics(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'mall/orderLogistics/export',
    {
      ...queryParams.value
    },
    `orderLogistics_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
