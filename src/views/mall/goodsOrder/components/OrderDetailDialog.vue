<template>
  <el-dialog :title="dialog.title" v-model="dialog.visible" width="60%" append-to-body destroy-on-close @close="handleClose" draggable>
    <OrderDetail :goodsOrderId="goodsOrder?.id" />
  </el-dialog>
</template>

<script setup lang="ts">
import OrderDetail from './OrderDetail.vue';
import { ref } from 'vue';
import { GoodsOrderVO } from '@/api/mall/goodsOrder/types';

const goodsOrder = ref<GoodsOrderVO>();

const dialog = ref({
  visible: false,
  title: '商品订单明细'
});

const emit = defineEmits(['refresh']);

const handleClose = () => {
  dialog.value.visible = false;
  emit('refresh');
};

const openDialog = async (row: GoodsOrderVO) => {
  goodsOrder.value = row;
  dialog.value.visible = true;
  dialog.value.title = '商品订单明细 (' + row.orderCode + ')';
};

defineExpose({
  openDialog
});
</script>
