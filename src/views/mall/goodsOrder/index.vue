<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="120px" class="search-form-container">
            <el-form-item label="订单编号" prop="orderCode">
              <el-input v-model="queryParams.orderCode" placeholder="请输入订单编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="订单状态" prop="orderStatus">
              <el-select v-model="queryParams.orderStatus" placeholder="请选择订单状态" clearable @change="handleQuery">
                <el-option v-for="dict in mall_goods_order_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="订单状态时间" style="width: 308px" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeStatusChangeTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="B端客户" prop="customerId">
              <customer-select v-model="queryParams.customerId" placeholder="请选择客户档案" @change="handleQuery" />
            </el-form-item>
            <el-form-item label="C端客户" prop="cCustomerPhone" v-if="showMoreCondition">
              <el-input v-model="queryParams.cCustomerPhone" placeholder="请输入下单人手机号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="承做人" prop="ownerId" v-if="showMoreCondition">
              <!-- <el-input v-model="queryParams.ownerId" placeholder="请输入承做人" clearable @keyup.enter="handleQuery" /> -->
              <el-select v-model="queryParams.ownerId" placeholder="请输入承做人" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
            <el-form-item label="承做部门" prop="ownerDeptId" v-if="showMoreCondition">
              <!-- <el-input v-model="queryParams.ownerDeptId" placeholder="请输入承做部门" clearable @keyup.enter="handleQuery" /> -->
              <el-tree-select
                v-model="queryParams.ownerDeptId"
                :data="deptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                placeholder="请选择承做部门"
                check-strictly
                filterable
                clearable
                @change="handleDeptChangeQuery"
              />
            </el-form-item>
            <!-- <el-form-item label="支付状态" prop="orderPaymentStatus" v-if="showMoreCondition">
              <el-select v-model="queryParams.orderPaymentStatus" placeholder="请选择支付状态" clearable @change="handleQuery">
                <el-option v-for="dict in mall_order_payment_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item> -->
            <el-form-item label="计划发货日期" style="width: 308px">
              <el-date-picker
                v-model="dateRangeShippingDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="订单类型" prop="orderType">
              <el-select v-model="queryParams.orderType" placeholder="请选择订单类型" clearable @change="handleQuery">
                <el-option v-for="dict in mall_goods_order_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="收件人姓名" prop="recipientName" v-if="showMoreCondition">
              <el-input v-model="queryParams.recipientName" placeholder="请输入收件人姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="收件人电话" prop="recipientPhone" v-if="showMoreCondition">
              <el-input v-model="queryParams.recipientPhone" placeholder="请输入收件人电话" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="收件人省份" prop="recipientProvince" v-if="showMoreCondition">
              <!-- <el-input v-model="queryParams.recipientProvince" placeholder="请输入收件人省份" clearable @keyup.enter="handleQuery" /> -->
              <el-select
                v-model="queryParams.recipientProvince"
                placeholder="请选择省份"
                clearable
                @change="handleSelectProvinceInQuery"
                @visible-change="loadProvinceList"
                filterable
              >
                <el-option v-for="item in provinceOptions" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>
            <el-form-item label="收件人城市" prop="recipientCity" v-if="showMoreCondition">
              <!-- <el-input v-model="queryParams.recipientCity" placeholder="请输入收件人城市" clearable @keyup.enter="handleQuery" /> -->
              <el-select
                v-model="queryParams.recipientCity"
                placeholder="请选择城市"
                clearable
                @change="handleQuery"
                filterable
                :disabled="!queryParams.recipientProvince"
              >
                <el-option v-for="item in cityOptions" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>

            <el-form-item label="标签" prop="tags" v-if="showMoreCondition">
              <dict-select v-model="queryParams.tags" dict-key="mall_goods_order_tags" placeholder="请选择标签" clearable @change="handleQuery" />
            </el-form-item>
            <el-form-item label="更新人" prop="updateBy" v-if="showMoreCondition">
              <el-select v-model="queryParams.updateBy" placeholder="请输入更新人" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Reading" @click="handleBusinessManual">业务手册</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-tooltip content="同步订单到聚水潭系统" placement="top">
              <el-button type="success" plain icon="Upload" :disabled="multiple" @click="handleUploadOrder()" v-hasPermi="['mall:goodsOrder:edit']">
                订单同步
              </el-button>
            </el-tooltip>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Close" :disabled="multiple" @click="handleCloseOrder()" v-hasPermi="['mall:goodsOrder:edit']">
              批量关闭
            </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="goodsOrderList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="订单编号" prop="orderCode" width="180" fixed="left" />
        <el-table-column label="商品种数" prop="detailCount" width="80">
          <template #default="scope">
            <el-link type="primary" @click="handleOrderDetail(scope.row)">{{ scope.row.detailCount }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="订单状态" prop="orderStatus" width="100">
          <template #default="scope">
            <dict-tag :options="mall_goods_order_status" :value="scope.row.orderStatus" />
          </template>
        </el-table-column>
        <el-table-column label="订单状态时间" prop="statusChangeTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.statusChangeTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="B端客户" prop="customerName" width="200" show-overflow-tooltip />
        <el-table-column label="C端客户手机号" prop="ccustomerPhone" width="120" />
        <el-table-column label="计划发货日期" prop="shippingDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.shippingDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="收件人姓名" prop="recipientName" width="100" show-overflow-tooltip />
        <el-table-column label="收件人电话" prop="recipientPhone" width="150" show-overflow-tooltip />
        <el-table-column label="收件人省份" prop="provinceName" width="100" show-overflow-tooltip />
        <el-table-column label="收件人城市" prop="cityName" width="100" show-overflow-tooltip />
        <el-table-column label="收件人地址" prop="recipientAddress" width="250" show-overflow-tooltip />
        <el-table-column label="C端客户备注" prop="cnote" width="180" show-overflow-tooltip />
        <el-table-column label="订单类型" prop="orderType" width="100">
          <template #default="scope">
            <dict-tag :options="mall_goods_order_type" :value="scope.row.orderType" />
          </template>
        </el-table-column>
        <el-table-column label="承做人" prop="ownerNickName" width="150" show-overflow-tooltip />
        <el-table-column label="承做部门" prop="ownerDeptName" width="150" show-overflow-tooltip />
        <el-table-column label="订单总金额(元)" prop="totalAmount" width="120">
          <template #default="scope">
            <span>{{ centToYuan(scope.row.totalAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="优惠金额(元)" prop="couponPrice" width="100">
          <template #default="scope">
            <span>{{ centToYuan(scope.row.couponPrice) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="支付金额(元)" prop="paymentPrice" width="100">
          <template #default="scope">
            <span>{{ centToYuan(scope.row.paymentPrice) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="支付状态" prop="orderPaymentStatus" width="100">
          <template #default="scope">
            <dict-tag :options="mall_order_payment_status" :value="scope.row.orderPaymentStatus" />
          </template>
        </el-table-column>
        <el-table-column label="B端客户备注" prop="bnote" width="200" show-overflow-tooltip />
        <el-table-column label="标签" prop="tags" width="250">
          <template #default="scope">
            <dict-tag :options="mall_goods_order_tags" :value="scope.row.tags" />
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" width="180" show-overflow-tooltip />
        <el-table-column label="更新人" prop="updateByNickName" width="150" show-overflow-tooltip />
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="150">
          <template #default="scope">
            <el-tooltip content="查看" placement="top">
              <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['mall:goodsOrder:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="审核订单" placement="top" v-if="scope.row.orderStatus === '21'">
              <el-button
                link
                type="primary"
                icon="Promotion"
                @click="handlePrepareShipping(scope.row)"
                v-hasPermi="['mall:orderLogistics:edit']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="发货" placement="top" v-if="scope.row.orderStatus === '22'">
              <el-button
                link
                type="primary"
                icon="Promotion"
                @click="handleShipping(scope.row)"
                v-hasPermi="['mall:orderLogistics:edit']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="完成发货" placement="top" v-if="scope.row.orderStatus === '31'">
              <el-button
                link
                type="primary"
                icon="Promotion"
                @click="handleFinishShipping(scope.row)"
                v-hasPermi="['mall:orderLogistics:edit']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="运营备注" placement="top">
              <el-button link type="primary" icon="MessageBox" @click="handleNote(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="关闭订单" placement="top" v-if="['21', '22', '31'].includes(scope.row.orderStatus)">
              <el-button link type="primary" icon="Close" @click="handleCloseOrder(scope.row)" v-hasPermi="['mall:goodsOrder:edit']"></el-button>
            </el-tooltip>
            <!-- <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mall:goodsOrder:remove']"></el-button>
            </el-tooltip> -->
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改商品订单对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="80%" append-to-body @close="cancel" draggable destroy-on-close>
      <el-row :gutter="10">
        <!-- 左侧锚点导航 -->
        <el-col :span="2">
          <el-anchor :default-active="currentAnchor">
            <el-anchor-link href="#basic" title="基础信息" />
            <el-anchor-link href="#payment" title="支付信息" />
            <el-anchor-link href="#logistic" title="物流信息" />
            <el-anchor-link href="#detail" title="商品明细" />
            <el-anchor-link href="#settlement" title="结算信息" />
          </el-anchor>
        </el-col>

        <!-- 右侧表单内容 -->
        <el-col :span="22">
          <el-form ref="goodsOrderFormRef" :model="form" :rules="rules" label-width="160px" label-position="top">
            <!-- 基础信息 -->
            <div id="basic" class="form-section">
              <div class="section-title">
                <span class="title-text">基础信息</span>
              </div>
              <el-descriptions class="margin-top" :column="4" border>
                <!-- <template #extra>
                  <el-button type="primary">Operation</el-button>
                </template> -->
                <el-descriptions-item label="订单编号">{{ form.orderCode }}</el-descriptions-item>
                <el-descriptions-item label="订单类型">
                  <dict-tag :options="mall_goods_order_type" :value="form.orderType" />
                </el-descriptions-item>
                <el-descriptions-item label="订单状态">
                  <dict-tag :options="mall_goods_order_status" :value="form.orderStatus" />
                </el-descriptions-item>
                <el-descriptions-item label="承做人">{{ form.ownerNickName }}</el-descriptions-item>
                <el-descriptions-item label="承做部门">{{ form.ownerDeptName }}</el-descriptions-item>
                <el-descriptions-item label="B端客户">{{ form.customerName }}</el-descriptions-item>
                <el-descriptions-item label="C端客户">{{ form.ccustomerPhone }}</el-descriptions-item>
                <el-descriptions-item label="C端期望日期">{{ form.shippingDate ? form.shippingDate.split(' ')[0] : '' }}</el-descriptions-item>
                <!-- <el-descriptions-item label="收件人姓名">{{ form.recipientName }}</el-descriptions-item>
                <el-descriptions-item label="收件人手机">{{ form.recipientPhone }}</el-descriptions-item>
                <el-descriptions-item label="收件省市">{{ form.provinceName }} {{ form.cityName }}{{ form.areaName }}</el-descriptions-item>
                <el-descriptions-item label="收件地址">{{ form.recipientAddress }}</el-descriptions-item>
                <el-descriptions-item label="C端客户备注">{{ form.cnote }}</el-descriptions-item> -->
                <el-descriptions-item label="B端客户备注">{{ form.bnote }}</el-descriptions-item>
                <el-descriptions-item label="运营标签">
                  <dict-tag :options="mall_goods_order_tags" :value="form.tags" />
                </el-descriptions-item>
                <el-descriptions-item label="运营备注">{{ form.remark }}</el-descriptions-item>
              </el-descriptions>
            </div>

            <!-- 商品明细 -->
            <div id="detail" class="form-section">
              <div class="section-title">
                <span class="title-text">商品明细</span>
              </div>
              <OrderDetail :goodsOrderId="form.id" />
            </div>

            <!-- 付款信息 -->
            <div id="payment" class="form-section">
              <div class="section-title">
                <span class="title-text">支付信息</span>
              </div>
              <div style="margin-bottom: 10px">
                <el-descriptions class="margin-top" :column="4" border v-if="form.orderType !== '01'">
                  <!-- <template #extra>
                  <el-button type="primary">Operation</el-button>
                </template> -->
                  <el-descriptions-item label="支付状态">
                    <dict-tag :options="mall_order_payment_status" :value="form.orderPaymentStatus" />
                  </el-descriptions-item>
                  <el-descriptions-item label="订单总金额(元)">{{ centToYuan(form.totalAmount) }}</el-descriptions-item>
                  <el-descriptions-item label="优惠金额(元)">{{ centToYuan(form.couponPrice) }}</el-descriptions-item>
                  <el-descriptions-item label="支付金额(元)">{{ centToYuan(form.paymentPrice) }}</el-descriptions-item>
                </el-descriptions>
              </div>
              <OrderPayment :goodsOrderId="form.id" />
            </div>

            <!-- 物流信息 -->
            <div id="logistic" class="form-section">
              <div class="section-title">
                <span class="title-text">物流信息</span>
              </div>
              <div>
                <el-descriptions class="margin-top" :column="4" border>
                  <!-- <template #extra>
                  <el-button type="primary">Operation</el-button>
                </template> -->
                  <el-descriptions-item width="120px" label="收件人姓名">{{ form.recipientName }}</el-descriptions-item>
                  <el-descriptions-item width="120px" label="收件人手机">{{ form.recipientPhone }}</el-descriptions-item>
                  <el-descriptions-item label="收件省市">{{ form.provinceName }} {{ form.cityName }}{{ form.areaName }}</el-descriptions-item>
                  <el-descriptions-item label="收件地址">{{ form.recipientAddress }}</el-descriptions-item>
                  <el-descriptions-item label="C端客户备注">
                    <el-tooltip :content="form.cnote" placement="top" :disabled="!form.cnote">
                      <el-text line-clamp="2">{{ form.cnote }}</el-text>
                    </el-tooltip>
                  </el-descriptions-item>
                </el-descriptions>
                <OrderLogistic :goodsOrderId="form.id" />
              </div>
            </div>

            <!-- 结算信息 -->
            <div id="settlement" class="form-section">
              <div class="section-title">
                <span class="title-text">结算信息</span>
              </div>
              <div>开发中，敬请期待</div>
            </div>
          </el-form>
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <!-- <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button> -->
        </div>
      </template>
    </el-dialog>

    <!-- 运营备注 -->
    <el-dialog :title="dialogToNote.title" v-model="dialogToNote.visible" width="600px" append-to-body destroy-on-close @close="cancelNote" draggable>
      <el-form ref="goodsOrderFormRef" :model="form" label-width="80px" label-position="top">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="订单编号" prop="orderCode">
              <el-input v-model="form.orderCode" placeholder="请输入订单编号" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="运营标签" prop="tags">
              <dict-select v-model="form.tags" dict-key="mall_goods_order_tags" multiple limit="5" placeholder="请选择标签" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="运营备注" prop="remark">
              <el-input type="textarea" :rows="4" maxlength="200" show-word-limit v-model="form.remark" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitNote">确 定</el-button>
          <el-button @click="cancelNote">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 审核订单，将订单流转到"发货中"状态 -->
    <el-dialog
      :title="dialogPrepareShipping.title"
      v-model="dialogPrepareShipping.visible"
      width="600px"
      append-to-body
      destroy-on-close
      @close="cancelPrepareShipping"
      draggable
    >
      <el-form ref="goodsOrderFormRef" :model="form" label-width="100px">
        <div class="mb-4">
          <el-text type="warning" size="small">
            订单处于"待发货"状态时，C端用户可以进行退款或者修改订单的操作；一旦进入"发货中"状态，则上述操作将不再允许。
          </el-text>
        </div>
        <div class="mb-4">
          <el-text type="danger" size="large"> 审核通过，则意味着订单已进入"发货中"状态。请谨慎操作! </el-text>
        </div>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="订单状态" prop="orderStatus">
              <dict-select v-model="form.orderStatus" dict-key="mall_goods_order_status" placeholder="请选择订单状态" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="审核时间">
              <el-date-picker
                v-model="form.statusChangeTime"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择审核时间"
                disabled
              />
            </el-form-item>
            <el-form-item label="运营备注" prop="remark">
              <el-input type="textarea" :rows="4" maxlength="200" show-word-limit v-model="form.remark" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitPrepareShipping">审核后发货</el-button>
          <el-button @click="cancelPrepareShipping">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 发货操作 -->
    <ShippingDialog ref="shippingDialogRef" @refresh="getList" />

    <!-- 完成发货 ,订单状态流转到"已完成"状态 -->
    <el-dialog
      :title="dialogFinishShipping.title"
      v-model="dialogFinishShipping.visible"
      width="600px"
      append-to-body
      destroy-on-close
      @close="cancelFinishShipping"
      draggable
    >
      <el-form ref="goodsOrderFormRef" :model="form" label-width="100px">
        <div class="mb-4">
          <el-text type="warning" size="small"> 完成收货，则意味着客户已经接收了商品物权，业务员将正式产生收款义务。 </el-text>
        </div>
        <div class="mb-4">
          <el-text type="danger" size="large"> 此操作将订单转换为最终状态，提交后不可逆，请谨慎操作！ </el-text>
        </div>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="订单编号" prop="orderCode">
              <el-input v-model="form.orderCode" placeholder="请输入订单编号" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="订单状态" prop="orderStatus">
              <dict-select v-model="form.orderStatus" dict-key="mall_goods_order_status" placeholder="请选择订单状态" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="订单完成时间">
              <el-date-picker v-model="form.statusChangeTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择订单完成时间" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="运营备注" prop="remark">
              <el-input type="textarea" :rows="4" maxlength="200" show-word-limit v-model="form.remark" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitFinishShipping">完成发货</el-button>
          <el-button @click="cancelFinishShipping">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <OrderDetailDialog ref="orderDetailDialogRef" />

    <!-- 关闭订单对话框 -->
    <el-dialog
      :title="dialogCloseOrder.title"
      v-model="dialogCloseOrder.visible"
      width="600px"
      append-to-body
      destroy-on-close
      @close="cancelCloseOrder"
      draggable
    >
      <el-form ref="goodsOrderFormRef" :model="form" label-width="80px" label-position="top">
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="订单编号" prop="orderCode" v-if="form.orderCode">
              <el-input v-model="form.orderCode" placeholder="请输入订单编号" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="运营备注" prop="remark">
              <el-input type="textarea" :rows="4" maxlength="200" show-word-limit v-model="form.remark" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitCloseOrder">确 定</el-button>
          <el-button @click="cancelCloseOrder">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 订单同步对话框 -->
    <el-dialog
      :title="dialogUploadOrder.title"
      v-model="dialogUploadOrder.visible"
      width="600px"
      append-to-body
      destroy-on-close
      @close="cancelUploadOrder"
      draggable
    >
      <div class="mb-4">
        <el-text type="warning" size="small">本操作将选中的订单同步到聚水潭系统。只有订单状态为"待发货"且计划发货时间为当天的订单才能同步。</el-text>
      </div>
      <div class="mb-4">
        <el-text type="primary"
          >已选择 <span class="text-danger">{{ uploadOrderIds.length }}</span> 个订单，其中符合同步条件的有
          <span class="text-success">{{ validUploadOrderIds.length }}</span> 个</el-text
        >
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitUploadOrder" :disabled="validUploadOrderIds.length === 0"
            >确认同步</el-button
          >
          <el-button @click="cancelUploadOrder">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GoodsOrder" lang="ts">
import {
  listGoodsOrder,
  getGoodsOrder,
  delGoodsOrder,
  addGoodsOrder,
  updateGoodsOrder,
  closeGoodsOrder,
  uploadGoodsOrder
} from '@/api/mall/goodsOrder';
import { GoodsOrderVO, GoodsOrderQuery, GoodsOrderForm } from '@/api/mall/goodsOrder/types';
import ShippingDialog from './components/ShippingDialog.vue';
import OrderDetailDialog from './components/OrderDetailDialog.vue';
import OrderDetail from './components/OrderDetail.vue';
import OrderPayment from './components/OrderPayment.vue';
import OrderLogistic from './components/OrderLogistic.vue';
import { parseTime } from '@/utils/ruoyi';
import dayjs from 'dayjs';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_goods_order_status, mall_goods_order_tags, mall_payment_method, mall_goods_order_type, mall_order_payment_status } = toRefs<any>(
  proxy?.useDict('mall_goods_order_status', 'mall_goods_order_tags', 'mall_payment_method', 'mall_goods_order_type', 'mall_order_payment_status')
);

const goodsOrderList = ref<GoodsOrderVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑
const shippingDialogRef = ref(null);

const orderDetailDialogRef = ref(null);
const goodsOrderId = ref<string | number>();

const dateRangeStatusChangeTime = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeShippingDate = ref<[DateModelType, DateModelType]>([
  parseTime(new Date().getTime() - 30 * 24 * 60 * 60 * 1000, '{y}-{m}-{d} 00:00:00'),
  parseTime(new Date().getTime() + 3 * 24 * 60 * 60 * 1000, '{y}-{m}-{d} 23:59:59')
]);
const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const goodsOrderFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const dialogPrepareShipping = reactive<DialogOption>({
  visible: false,
  title: '审核订单'
});

const dialogFinishShipping = reactive<DialogOption>({
  visible: false,
  title: '完成发货'
});

const dialogToNote = reactive<DialogOption>({
  visible: false,
  title: '运营备注'
});

const dialogCloseOrder = reactive<DialogOption>({
  visible: false,
  title: '关闭订单'
});

const dialogUploadOrder = reactive<DialogOption>({
  visible: false,
  title: '订单同步'
});

const currentAnchor = ref('#basic'); // 当前页面锚点

const uploadOrderIds = ref<Array<string | number>>([]);
const validUploadOrderIds = ref<Array<string | number>>([]);

const initFormData: GoodsOrderForm = {
  id: undefined,
  orderCode: undefined,
  orderType: undefined,
  orderStatus: undefined,
  statusChangeTime: undefined,
  customerId: undefined,
  ccustomerPhone: undefined,
  ownerId: undefined,
  ownerDeptId: undefined,
  totalAmount: undefined,
  couponPrice: undefined,
  paymentPrice: undefined,
  orderPaymentStatus: undefined,
  shippingDate: undefined,
  recipientName: undefined,
  recipientPhone: undefined,
  recipientProvince: undefined,
  recipientCity: undefined,
  recipientArea: undefined,
  recipientAddress: undefined,
  bnote: undefined,
  cnote: undefined,
  tags: undefined,
  remark: undefined
};
const data = reactive<PageData<GoodsOrderForm, GoodsOrderQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderCode: undefined,
    orderType: undefined,
    orderStatus: undefined,
    customerId: undefined,
    cCustomerPhone: undefined,
    ownerId: undefined,
    ownerDeptId: undefined,
    orderPaymentStatus: undefined,
    recipientName: undefined,
    recipientPhone: undefined,
    recipientProvince: undefined,
    recipientCity: undefined,
    recipientArea: undefined,
    recipientAddress: undefined,
    bnote: undefined,
    cnote: undefined,
    tags: undefined,
    updateBy: undefined,
    params: {
      statusChangeTime: undefined,
      shippingDate: undefined,
      updateTime: undefined
    }
  },
  rules: {}
});

const { queryParams, form, rules } = toRefs(data);

import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
const { loadUserList, userOptions } = useSysUserSelect();

import { useDeptSelect } from '@/hooks/useBusiness/deptSelect';
const { deptOptions, loadDeptTree } = useDeptSelect();

import { useProvinceSelect } from '@/api/setting/chinaProvince/provinceSelect';
const { provinceOptions, provinceLoading, loadProvinceList } = useProvinceSelect();

import { useCitySelect } from '@/api/setting/chinaCity/citySelect';
import { centToYuan } from '@/utils/moneyUtils';
const { cityOptions, cityLoading, loadCityList } = useCitySelect();

/** 查询商品订单列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeStatusChangeTime.value, 'StatusChangeTime');
  proxy?.addDateRange(queryParams.value, dateRangeShippingDate.value, 'ShippingDate');
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listGoodsOrder(queryParams.value);
  goodsOrderList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 取消运营备注按钮 */
const cancelNote = () => {
  reset();
  dialogToNote.visible = false;
};

/** 取消审核订单按钮 */
const cancelPrepareShipping = () => {
  reset();
  dialogPrepareShipping.visible = false;
};

/** 取消完成发货按钮 */
const cancelFinishShipping = () => {
  reset();
  dialogFinishShipping.visible = false;
};

/** 取消关闭订单按钮 */
const cancelCloseOrder = () => {
  reset();
  dialogCloseOrder.visible = false;
};

/** 取消上传订单按钮 */
const cancelUploadOrder = () => {
  reset();
  dialogUploadOrder.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  goodsOrderFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeStatusChangeTime.value = ['', ''];
  dateRangeShippingDate.value = ['', ''];
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: GoodsOrderVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加商品订单';
};

/** 修改按钮操作 */
const handleView = async (row?: GoodsOrderVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getGoodsOrder(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '查看商品订单';
};

/** 运营备注按钮操作 */
const handleNote = async (row?: GoodsOrderVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getGoodsOrder(_id);
  Object.assign(form.value, res.data);
  dialogToNote.visible = true;
};

/** 准备发货按钮操作 */
const handlePrepareShipping = async (row?: GoodsOrderVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getGoodsOrder(_id);
  Object.assign(form.value, res.data);
  form.value.orderStatus = '22';
  form.value.statusChangeTime = parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}');
  dialogPrepareShipping.visible = true;
};

/** 提交按钮 */
const submitForm = () => {
  goodsOrderFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGoodsOrder(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addGoodsOrder(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 提交备注按钮操作 */
const submitNote = () => {
  goodsOrderFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      await updateGoodsOrder(form.value).finally(() => (buttonLoading.value = false));
      proxy?.$modal.msgSuccess('操作成功');
      dialogToNote.visible = false;
      await getList();
    }
  });
};

/** 审核后发货按钮操作 */
const submitPrepareShipping = () => {
  goodsOrderFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      await updateGoodsOrder(form.value).finally(() => (buttonLoading.value = false));
      proxy?.$modal.msgSuccess('操作成功');
      dialogPrepareShipping.visible = false;
      await getList();
    }
  });
};

/** 完成发货按钮操作 */
const handleFinishShipping = async (row?: GoodsOrderVO) => {
  reset();
  const res = await getGoodsOrder(row?.id);
  Object.assign(form.value, res.data);
  form.value.orderStatus = '51'; // 已完成
  form.value.statusChangeTime = parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}');
  dialogFinishShipping.visible = true;
};

/** 提交完成发货按钮操作 */
const submitFinishShipping = () => {
  goodsOrderFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      await updateGoodsOrder(form.value).finally(() => (buttonLoading.value = false));
      proxy?.$modal.msgSuccess('操作成功');
      dialogFinishShipping.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: GoodsOrderVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除商品订单编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delGoodsOrder(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'mall/goodsOrder/export',
    {
      ...queryParams.value
    },
    `goodsOrder_${new Date().getTime()}.xlsx`
  );
};

/** 业务手册按钮操作 */
const handleBusinessManual = () => {
  window.open('https://rcn3q3ujmqdd.feishu.cn/docx/XZPydARS9oOwNAx23qDcXcXhnqh?from=from_copylink', '_blank');
};

/** 发货按钮操作 */
const handleShipping = (row: GoodsOrderVO) => {
  shippingDialogRef.value.openDialog(row);
};

/** 订单明细按钮操作 */
const handleOrderDetail = (row: GoodsOrderVO) => {
  orderDetailDialogRef.value.openDialog(row);
};

// 查询时，处理归属部门选择变化
const handleDeptChangeQuery = (data) => {
  queryParams.value.ownerDeptId = data;
  handleQuery();
};

// 选择省份时，加载城市列表
const handleSelectProvinceInQuery = () => {
  loadCityList(queryParams.value.recipientProvince);
  queryParams.value.recipientCity = undefined;
  handleQuery();
};

/** 关闭订单按钮操作 */
const handleCloseOrder = async (row?: GoodsOrderVO) => {
  reset();
  if (row) {
    // 单个关闭
    const _id = row.id;
    const res = await getGoodsOrder(_id);
    Object.assign(form.value, res.data);
  } else {
    // 批量关闭，ids已经在handleSelectionChange中设置
    if (ids.value.length === 0) {
      return proxy?.$modal.msgError('请选择要关闭的订单');
    }
    // 检查订单是否可以关闭
    const canCloseStatuses = ['21', '22', '31']; // 可关闭的订单状态

    // 过滤出可以关闭的订单
    const closableOrders = goodsOrderList.value
      .filter((item) => ids.value.includes(item.id))
      .filter((item) => canCloseStatuses.includes(item.orderStatus));

    if (closableOrders.length === 0) {
      return proxy?.$modal.msgError('没有可关闭的订单，请确保选中的订单包含待发货、发货中或已发货状态的订单');
    }

    // 记录原始选择的数量
    const originalCount = ids.value.length;

    // 更新ids为可关闭的订单IDs
    ids.value = closableOrders.map((item) => item.id);

    // 如果过滤后的数量与原始选择不同，给用户提示
    if (closableOrders.length < originalCount) {
      proxy?.$modal.msgWarning(`已自动过滤掉${originalCount - closableOrders.length}个不可关闭的订单`);
    }
  }
  dialogCloseOrder.visible = true;
};

/** 提交关闭订单 */
const submitCloseOrder = () => {
  goodsOrderFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      const _ids = form.value.id ? [form.value.id] : ids.value;
      await closeGoodsOrder({ ids: _ids, remark: form.value.remark || '' }).finally(() => (buttonLoading.value = false));
      proxy?.$modal.msgSuccess('订单关闭成功');
      dialogCloseOrder.visible = false;
      await getList();
    }
  });
};

/** 上传订单按钮操作 */
const handleUploadOrder = () => {
  // 判断是否选择了订单
  if (ids.value.length === 0) {
    return proxy?.$modal.msgError('请选择要同步的订单');
  }
  // 重置数据
  uploadOrderIds.value = [...ids.value];
  validUploadOrderIds.value = [];

  // 检查订单是否符合同步条件（状态为待发货且计划发货时间为当天）
  const today = dayjs().format('YYYY-MM-DD');

  // 遍历选中的订单，筛选符合条件的
  goodsOrderList.value.forEach((order) => {
    if (ids.value.includes(order.id)) {
      // 使用 dayjs 格式化日期，确保格式一致
      const shippingDate = order.shippingDate ? dayjs(order.shippingDate).format('YYYY-MM-DD') : '';

      if (order.orderStatus === '21' && shippingDate === today) {
        validUploadOrderIds.value.push(order.id);
      }
    }
  });

  dialogUploadOrder.visible = true;
};

/** 提交上传订单 */
const submitUploadOrder = async () => {
  if (validUploadOrderIds.value.length === 0) {
    return proxy?.$modal.msgError('没有符合条件的订单可以同步');
  }

  try {
    buttonLoading.value = true;
    await uploadGoodsOrder(validUploadOrderIds.value);
    proxy?.$modal.msgSuccess('订单同步成功');
    dialogUploadOrder.visible = false;
    await getList(); // 刷新列表
  } catch (error) {
    console.error('订单同步失败', error);
    proxy?.$modal.msgError('订单同步失败');
  } finally {
    buttonLoading.value = false;
  }
};

onMounted(() => {
  getList();
  loadUserList();
  loadDeptTree();
});
</script>

<style scoped lang="scss">
@import '@/assets/styles/anchorform.scss';

:deep(.warning-row) {
  background-color: #fef0f0 !important;
}
</style>
