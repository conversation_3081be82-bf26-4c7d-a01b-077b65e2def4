<template>
  <el-dialog :title="dialogTitle" v-model="visible" @close="handleClose" width="80%">
    <el-row :gutter="10">
      <!-- 左侧锚点导航 -->
      <el-col :span="2">
        <el-anchor>
          <el-anchor-link href="#title1" title="兑换方案" />
          <el-anchor-link href="#title2" title="方案明细" />
        </el-anchor>
      </el-col>
      <el-col :span="22">
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="80px" label-position="top" :disabled="isViewMode">
          <div id="title1" class="form-section">
            <div class="section-title">
              <span class="title-text">兑换方案</span>
            </div>
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="方案编码" prop="code">
                  <el-input disabled v-model="formData.code" placeholder="创建后自动生成" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="方案名称" prop="name">
                  <el-input v-model="formData.name" placeholder="请输入方案名称" maxlength="50" show-word-limit />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="状态" prop="status">
                  <el-select v-model="formData.status" placeholder="请选择状态">
                    <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="方案类型" prop="type">
                  <dict-select v-model="formData.type" dict-key="mall_exchange_type" placeholder="请选择方案类型" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="商品数量" prop="goodsCount">
                  <template #label>
                    <span>商品数量</span>
                    <el-tooltip content="商品数量即兑换方案的明细数量，你可以通过添加或删除“方案明细”来改变这个数值" placement="top">
                      <el-icon :size="12" class="el-icon--right">
                        <QuestionFilled />
                      </el-icon>
                    </el-tooltip>
                  </template>
                  <el-input-number style="width: 100%" v-model="formData.goodsCount" :min="0" placeholder="请输入商品数量" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="可选数量" prop="optionalQty">
                  <template #label>
                    <span>可选数量</span>
                    <el-tooltip content="如果你想配置如“4选3”的效果，则商品数量为4，可选数量为3；可选数量不能大于方案明细中商品数量" placement="top">
                      <el-icon :size="12" class="el-icon--right">
                        <QuestionFilled />
                      </el-icon>
                    </el-tooltip>
                  </template>
                  <el-input-number
                    style="width: 100%"
                    v-model="formData.optionalQty"
                    :min="1"
                    :max="formData.goodsCount || 1"
                    :precision="0"
                    placeholder="请输入可选数量"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="市场价（元）" prop="price">
                  <el-input-number
                    style="width: 100%"
                    v-model="formData.priceYuan"
                    :min="0"
                    :precision="2"
                    :step="0.01"
                    placeholder="请输入市场价（元）"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="最低售价（元）" prop="minSellPrice">
                  <el-input-number
                    style="width: 100%"
                    v-model="formData.minSellPriceYuan"
                    :min="0"
                    :precision="2"
                    :step="0.01"
                    placeholder="请输入最低参考售价（元）"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="成本价（元）" prop="costPrice">
                  <el-input-number
                    style="width: 100%"
                    v-model="formData.costPriceYuan"
                    :min="0"
                    :precision="2"
                    :step="0.01"
                    placeholder="请输入成本价（元）"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="兑换日期" prop="exchangeDateRange">
                  <el-date-picker
                    v-model="formData.exchangeDateRange"
                    type="daterange"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    style="width: 100%"
                    :disabledDate="disabledDate"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="方案描述" prop="desc">
                  <el-input type="textarea" :rows="3" maxlength="250" show-word-limit v-model="formData.desc" placeholder="请输入方案描述" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="备注" prop="remark">
                  <el-input type="textarea" :rows="3" maxlength="500" show-word-limit v-model="formData.remark" placeholder="请输入备注" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div id="title2" class="form-section">
            <div class="section-title">
              <span class="title-text">方案明细</span>
            </div>
            <el-table :data="formData.detail" style="width: 100%" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="55" />
              <!-- 商品名称、图片、数量、零售价、市场价、测算成本、描述 操作：移出、编辑 -->
              <el-table-column label="商品名称" prop="name" />
              <el-table-column label="图片" prop="image" width="80">
                <template #default="scope">
                  <ImagePreview :src="scope.row.images" width="50px" height="50px" isId />
                </template>
              </el-table-column>
              <el-table-column label="数量" prop="qty" width="100">
                <template #default="scope">
                  <el-input-number
                    v-model="scope.row.qty"
                    :min="1"
                    :precision="0"
                    :disabled="isViewMode"
                    controls-position="right"
                    size="small"
                    style="width: 100%"
                  />
                </template>
              </el-table-column>
              <el-table-column label="销售单位" prop="saleUnit" width="80">
                <template #default="scope">
                  <dict-tag :options="mall_goods_unit" :value="scope.row.saleUnit" />
                </template>
              </el-table-column>
              <el-table-column label="零售价" prop="retailPrice" width="120">
                <template #default="scope">
                  {{ centToYuan(scope.row.retailPrice) }}
                </template>
              </el-table-column>
              <el-table-column label="市场价" prop="marketPrice" width="120">
                <template #default="scope">
                  {{ centToYuan(scope.row.marketPrice) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150" fixed="right">
                <template #default="scope">
                  <el-button v-if="!isViewMode" type="danger" link @click="handleRemoveDetail(scope.$index)">移出</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 10px; text-align: right">
              <el-button v-if="!isViewMode" type="danger" :disabled="selectedDetails.length === 0" @click="handleBatchRemove">批量移除</el-button>
              <el-button v-if="!isViewMode" type="primary" @click="handleBatchAdd">批量添加</el-button>
            </div>
          </div>
        </el-form>
      </el-col>
    </el-row>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">{{ isViewMode ? '关闭' : '取消' }}</el-button>
        <el-button v-if="!isViewMode" type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 使用商品选择组件 -->
  <GoodsSelector ref="goodsSelectorRef" :multiple="true" :excludeIds="getExistingGoodsIds" title="选择商品" @select="handleGoodsSelected" />
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import GoodsSelector from './components/GoodsSelector.vue';
import { addExchangePlan, updateExchangePlan } from '@/api/mall/exchangePlan';
import { centToYuan, yuanToCent } from '@/utils/moneyUtils';
import { QuartzWatch, QuestionFilled } from '@element-plus/icons-vue';
const { proxy } = getCurrentInstance();
const emit = defineEmits(['submit']);
const { mall_exchange_type, sys_normal_disable, mall_goods_unit } = toRefs(
  proxy?.useDict('mall_exchange_type', 'sys_normal_disable', 'mall_goods_unit')
);
const formData = ref({
  code: '', // 方案编码
  name: '', // 方案名称
  status: '0', // 状态，
  type: '', // 方案类型，默认固定商品
  goodsCount: 0, // 商品数量
  optionalQty: 0, // 可选数量
  price: 0, // 市场价(分)
  priceYuan: 0, // 市场价(元)
  costPrice: 0, // 成本价(分)
  costPriceYuan: 0, // 成本价(元)
  minSellPrice: 0, // 最低参考售价(分)
  minSellPriceYuan: 0, // 最低参考售价(元)
  desc: '', // 方案描述
  remark: '', // 备注
  detail: [], // 方案明细
  exchangeStartDate: '', // 兑换开始日期
  exchangeEndDate: '' // 兑换结束日期
});

// 商品数量计算属性
const updateGoodsCount = () => {
  formData.value.goodsCount = formData.value.detail.length;
};

// 获取已存在的商品ID
const getExistingGoodsIds = computed(() => {
  return formData.value.detail.map((item) => item.id);
});

const type = ref('add');
const visible = ref(false);
const formRef = ref(null);
const isBatchMode = ref(false);
const isSubmitting = ref(false);
const selectedDetails = ref([]);
const goodsSelectorRef = ref(null);

// 规则定义
const rules = {
  name: [{ required: true, message: '请输入方案名称', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  type: [{ required: true, message: '请选择方案类型', trigger: 'change' }],
  optionalQty: [
    { required: true, message: '请输入可选数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '可选数量必须大于0', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value > formData.value.goodsCount) {
          callback(new Error('可选数量不能大于商品数量'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  price: [
    { required: true, message: '请输入市场价', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value < formData.value.minSellPrice) {
          callback(new Error('市场价不能小于最低参考售价'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  minSellPrice: [
    { required: true, message: '请输入最低参考售价', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value < formData.value.costPrice) {
          callback(new Error('最低参考售价不能小于成本价'));
        } else if (value > formData.value.price) {
          callback(new Error('最低参考售价不能大于市场价'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  costPrice: [
    { required: true, message: '请输入成本价', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value > formData.value.minSellPrice) {
          callback(new Error('成本价不能大于最低参考售价'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  exchangeDateRange: [{ required: true, message: '请选择兑换日期', trigger: 'blur' }],
  desc: [{ required: true, message: '请输入方案描述', trigger: 'blur' }]
};

// 计算属性：对话框标题
const dialogTitle = computed(() => {
  if (type.value === 'add') return '新增兑换方案';
  if (type.value === 'edit') return '编辑兑换方案';
  return '查看兑换方案';
});

// 监听priceYuan变化，自动转换为分
watch(
  () => formData.value.priceYuan,
  (newVal) => {
    formData.value.price = yuanToCent(newVal);
  },
  { immediate: true }
);

// 监听costPriceYuan变化，自动转换为分
watch(
  () => formData.value.costPriceYuan,
  (newVal) => {
    formData.value.costPrice = yuanToCent(newVal);
  },
  { immediate: true }
);

// 监听minSellPriceYuan变化，自动转换为分
watch(
  () => formData.value.minSellPriceYuan,
  (newVal) => {
    formData.value.minSellPrice = yuanToCent(newVal);
  },
  { immediate: true }
);

// 计算属性：是否为查看模式
const isViewMode = computed(() => {
  return type.value === 'view';
});

// 打开对话框
const dialogOpen = (data) => {
  visible.value = true;
  type.value = data.type;
  if (data.data) {
    formData.value = JSON.parse(JSON.stringify(data.data));

    // 将价格从分转为元显示
    formData.value.priceYuan = centToYuan(formData.value.price);
    formData.value.costPriceYuan = centToYuan(formData.value.costPrice);
    formData.value.minSellPriceYuan = centToYuan(formData.value.minSellPrice);

    // 处理日期区间
    if (formData.value.exchangeStartDate && formData.value.exchangeEndDate) {
      formData.value.exchangeDateRange = [formData.value.exchangeStartDate, formData.value.exchangeEndDate];
    }

    // 处理detail数据，确保每个商品都有正确的retailPrice和marketPrice属性
    if (formData.value.detail && formData.value.detail.length > 0) {
      // 确保是数组类型
      if (typeof formData.value.detail === 'string') {
        try {
          formData.value.detail = JSON.parse(formData.value.detail);
        } catch (e) {
          console.error('解析detail数据失败:', e);
          formData.value.detail = [];
        }
      }

      // 处理每个商品明细，添加或映射必要的属性
      formData.value.detail = formData.value.detail.map((item) => {
        return {
          ...item,
          // 确保存在零售价和市场价字段
          retailPrice: item.retailPrice || item.currentSalePrice || 0,
          marketPrice: item.marketPrice || item.currentMarketPrice || 0,
          // 确保存在销售单位信息
          saleUnit: item.saleUnit || '',
          saleUnitLabel: item.saleUnitLabel || ''
        };
      });

      formData.value.goodsCount = formData.value.detail.length;
    } else {
      formData.value.goodsCount = 0;
      formData.value.detail = [];
    }
  } else {
    // 重置表单
    formData.value = {
      code: '',
      name: '',
      status: '',
      type: '',
      goodsCount: 0,
      optionalQty: 0,
      price: undefined,
      priceYuan: undefined,
      desc: '',
      remark: '',
      sort: 0,
      detail: []
    };
  }
};

// 关闭对话框
const handleClose = () => {
  visible.value = false;
};

// 处理表格多选
const handleSelectionChange = (selection) => {
  selectedDetails.value = selection;
};

// 批量移除
const handleBatchRemove = () => {
  if (selectedDetails.value.length === 0) {
    return;
  }

  // 确认删除
  ElMessageBox.confirm('确定要移除选中的商品吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 获取所有选中项的ID
      const selectedIds = selectedDetails.value.map((item) => item.id);

      // 过滤掉被选中的项
      formData.value.detail = formData.value.detail.filter((item) => !selectedIds.includes(item.id));

      // 更新商品数量
      updateGoodsCount();
      ElMessage.success('批量移除成功');
    })
    .catch(() => {
      // 取消操作
    });
};

// 批量添加商品
const handleBatchAdd = () => {
  goodsSelectorRef.value.open();
};

// 处理商品选择回调
const handleGoodsSelected = (goods) => {
  // 批量添加所有选择的商品
  const goodsToAdd = goods.map((item) => ({
    id: item.id,
    name: item.name,
    images: item.images,
    retailPrice: item.currentSalePrice,
    marketPrice: item.currentMarketPrice,
    qty: 1, // 默认数量为1，而不是库存数量
    saleUnit: item.saleUnit, // 添加销售单位
    saleUnitLabel: item.saleUnitLabel // 添加销售单位标签
  }));

  // 添加到商品明细
  formData.value.detail = [...formData.value.detail, ...goodsToAdd];
  updateGoodsCount();
  // 更新可选数量的最大值
  if (formData.value.optionalQty > formData.value.goodsCount) {
    formData.value.optionalQty = formData.value.goodsCount;
  }
  ElMessage.success(`成功添加${goodsToAdd.length}个商品`);
};

// 移除明细
const handleRemoveDetail = (index) => {
  formData.value.detail.splice(index, 1);
  updateGoodsCount();
  // 更新可选数量的最大值
  if (formData.value.optionalQty > formData.value.goodsCount) {
    formData.value.optionalQty = formData.value.goodsCount;
  }
  ElMessage.success('商品移除成功');
};

// 提交表单
const handleSubmit = async () => {
  // 防止重复点击
  if (isSubmitting.value) return;
  isSubmitting.value = true;
  if (!formRef.value) return;
  console.log(formData.value);
  try {
    await formRef.value.validate();

    // 检查方案明细是否为空
    if (formData.value.detail.length === 0) {
      ElMessage.warning('请至少添加一个商品明细');
      return;
    }

    // 处理提交的数据
    const submitData = {
      name: formData.value.name,
      code: formData.value.code,
      status: formData.value.status,
      sort: formData.value.sort,
      type: formData.value.type,
      price: formData.value.price, // 使用分为单位的价格提交
      optionalQty: formData.value.optionalQty,
      costPrice: formData.value.costPrice,
      minSellPrice: formData.value.minSellPrice,
      desc: formData.value.desc,
      detail: formData.value.detail.map((item) => ({
        id: item.id,
        qty: item.qty,
        sort: item.sort || 0,
        saleUnit: item.saleUnit,
        saleUnitLabel: item.saleUnitLabel
      })),
      exchangeStartDate: formData.value.exchangeDateRange[0],
      exchangeEndDate: formData.value.exchangeDateRange[1],
      remark: formData.value.remark
    };

    // 如果是编辑，添加id
    if (type.value === 'edit' && formData.value.id) {
      submitData.id = formData.value.id;
    }

    // emit('submit', submitData);
    const apiCall = submitData.id ? updateExchangePlan : addExchangePlan;
    const res = await apiCall(submitData);
    console.log('res', res);
    if (res.code === 200) {
      ElMessage.success('保存成功');
      emit('submit', submitData);
      handleClose();
    } else {
      ElMessage.error('保存失败');
    }
  } catch (error) {
    console.error('表单验证失败', error);
  } finally {
    isSubmitting.value = false;
  }
};

defineExpose({
  dialogOpen
});
</script>

<style scoped>
@import '@/assets/styles/anchorform.scss';

.goods-select-container {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}
</style>
