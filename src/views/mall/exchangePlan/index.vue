<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="方案名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="方案编码" prop="code">
              <el-input v-model="queryParams.code" placeholder="请输入" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="方案状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择" clearable @change="handleQuery">
                <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="方案类型" prop="type">
              <el-select v-model="queryParams.type" placeholder="请选择" clearable @change="handleQuery">
                <el-option v-for="dict in mall_exchange_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="兑换开始" prop="exchangeStartDate">
              <el-date-picker
                v-model="exchangeStartDateRange"
                type="daterange"
                value-format="YYYY-MM-DD"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
                @change="handleQuery"
              />
            </el-form-item>

            <el-form-item label="兑换结束" prop="exchangeEndDate">
              <el-date-picker
                v-model="exchangeEndDateRange"
                type="daterange"
                value-format="YYYY-MM-DD"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <!-- <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button> -->
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mall:exchangePlan:add']">新增</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="planList" border>
        <el-table-column type="index" width="55" align="center" />
        <el-table-column label="方案编码" prop="code" width="160"></el-table-column>
        <el-table-column label="方案名称" prop="name" min-width="200">
          <template #default="scope">
            <el-tooltip :content="scope.row.name || ''" placement="top" :show-after="200" popper-class="tooltip-custom-width" :max-width="300">
              <div class="two-line-ellipsis">{{ scope.row.name || '-' }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="方案类型" prop="type">
          <template #default="scope">
            <dict-tag :options="mall_exchange_type" :value="scope.row.type" />
          </template>
        </el-table-column>
        <el-table-column label="商品数量" prop="goodsCount" min-width="80">
          <template #default="scope">
            <span>{{ scope.row.detail ? scope.row.detail.length : 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="可选商品数量" prop="optionalQty" width="120"></el-table-column>
        <el-table-column label="市场价(元)" prop="price" width="120">
          <template #default="scope">
            <span>{{ centToYuan(scope.row.price) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="最低参考售价(元)" prop="minSellPrice" width="150">
          <template #default="scope">
            <span>{{ centToYuan(scope.row.minSellPrice) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="成本价(元)" prop="costPrice" width="120">
          <template #default="scope">
            <span>{{ centToYuan(scope.row.costPrice) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="兑换开始日期" prop="exchangeStartDate" width="150">
          <template #default="scope">
            <span>{{ parseTime(scope.row.exchangeStartDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="兑换结束日期" prop="exchangeEndDate" width="150">
          <template #default="scope">
            <span>{{ parseTime(scope.row.exchangeEndDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="方案描述" prop="desc" min-width="250" show-overflow-tooltip>
          <template #default="scope">
            <div>{{ scope.row.desc }}</div>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="150">
          <template #default="scope">
            <el-tooltip
              :content="scope.row.remark || '暂无备注'"
              placement="top"
              :show-after="200"
              popper-class="tooltip-custom-width"
              :max-width="300"
            >
              <div class="ellipsis-text">{{ scope.row.remark || '...' }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="更新人" prop="updateByName" width="150" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ scope.row.updateByName || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTime" width="160">
          <template #default="scope">
            <span>{{ scope.row.updateTime || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" width="160" fixed="right">
          <template #default="scope">
            <el-button link type="primary" has-perm="mall:exchangePlan:edit" @click="handleUpdate(scope.row)">编辑</el-button>
            <el-button link type="primary" has-perm="mall:exchangePlan:view" @click="handleView(scope.row)">查看</el-button>
            <el-button link type="primary" has-perm="mall:exchangePlan:remove" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handlePagination"
      />
    </el-card>

    <CardModelDialog ref="cardModelDialogRef" @submit="handleSubmit" />
  </div>
</template>

<script setup name="ExchangePlan" lang="ts">
import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
import CardModelDialog from './Dialog.vue';
import { listExchangePlan, delExchangePlan, getExchangePlan, addExchangePlan, updateExchangePlan } from '@/api/mall/exchangePlan';
import { centToYuan } from '@/utils/moneyUtils';
import { ExchangePlanVO } from '@/api/mall/exchangePlan/types';

const { proxy } = getCurrentInstance();
const { loadUserList, userOptions } = useSysUserSelect();
const showSearch = ref(true);
const loading = ref(false);
let cardModelDialogRef = ref(null);
const queryFormRef = ref(null);
const showMoreCondition = ref(false);
const isSubmitting = ref(false);
const dateRangeUpdateTime = refThrottled(['', '']);
const { mall_exchange_type, sys_normal_disable } = toRefs(proxy?.useDict('mall_exchange_type', 'sys_normal_disable'));

const total = ref(0);
const planList = ref([]);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  name: undefined,
  code: undefined,
  status: undefined,
  type: undefined
});

const route = useRoute();
const router = useRouter();

// 定义日期范围变量
const exchangeStartDateRange = ref(['', '']);
const exchangeEndDateRange = ref(['', '']);

// 获取列表
const getList = async () => {
  loading.value = true;
  try {
    const params = {
      ...queryParams.value,
      params: {}
    };

    if (
      exchangeStartDateRange.value &&
      exchangeStartDateRange.value.length === 2 &&
      exchangeStartDateRange.value[0] &&
      exchangeStartDateRange.value[1]
    ) {
      params.params.exchangeStartDateBegin = exchangeStartDateRange.value[0];
      params.params.exchangeStartDateEnd = exchangeStartDateRange.value[1];
    }

    if (exchangeEndDateRange.value && exchangeEndDateRange.value.length === 2 && exchangeEndDateRange.value[0] && exchangeEndDateRange.value[1]) {
      params.params.exchangeEndDateBegin = exchangeEndDateRange.value[0];
      params.params.exchangeEndDateEnd = exchangeEndDateRange.value[1];
    }

    const response = await listExchangePlan(params);

    // const response = await listExchangePlan({
    //   pageNum: queryParams.value.pageNum,
    //   pageSize: queryParams.value.pageSize,
    //   name: queryParams.value.name,
    //   code: queryParams.value.code,
    //   status: queryParams.value.status,
    //   type: queryParams.value.type
    // });
    planList.value = response.rows || [];
    total.value = response.total || 0;
  } catch (error) {
    console.error('获取方案列表失败:', error);
    proxy.$modal.msgError('获取方案列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

// 重置查询
const resetQuery = () => {
  queryFormRef.value.resetFields();
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    code: undefined,
    status: undefined,
    type: undefined,
    exchangeEndDateRange: ['', ''],
    exchangeStartDateRange: ['', '']
  };
  getList();
};

// 新增
const handleAdd = () => {
  cardModelDialogRef.value.dialogOpen({
    type: 'add'
  });
};

// 编辑
const handleUpdate = (row) => {
  getExchangePlan(row.id).then((res) => {
    cardModelDialogRef.value.dialogOpen({
      type: 'edit',
      data: res.data
    });
  });
};

// 查看
const handleView = (row) => {
  getExchangePlan(row.id).then((res) => {
    cardModelDialogRef.value.dialogOpen({
      type: 'view',
      data: res.data
    });
  });
};

// 删除
const handleDelete = (row) => {
  const planId = row.id;
  proxy.$modal
    .confirm('是否确认删除该兑换方案？')
    .then(function () {
      return delExchangePlan(planId);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => {});
};

const handleSubmit = async () => {
  getList();
};

// 分页处理
const handlePagination = (val) => {
  queryParams.value.pageNum = val.page;
  queryParams.value.pageSize = val.limit;
  getList();
};

// 监听路由参数
const handleOpenDialog = () => {
  // 打开编辑弹窗
  const openEdit = route.query.openEdit;
  const id = route.query.id;
  if (openEdit === 'true') {
    nextTick(() => {
      handleUpdate({ id: id } as ExchangePlanVO);
      router.replace({ query: {} });
    });
  }
};
// 在组件挂载时获取数据
onMounted(() => {
  handleOpenDialog();
  getList();
});
</script>

<style lang="scss" scoped>
.ellipsis-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px; // 可以根据实际情况调整宽度
}

/* 两行截断样式 */
.two-line-ellipsis {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  max-width: 120px; // 可以根据实际情况调整宽度
}
</style>

<style>
/* 自定义悬浮提示框样式 */
.tooltip-custom-width {
  max-width: 300px !important;
  word-break: break-all;
  white-space: normal;
  line-height: 1.5;
  padding: 8px 12px;
}
</style>
