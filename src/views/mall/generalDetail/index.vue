<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="详情名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" clearable @change="handleQuery">
                <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新人" prop="updateBy" v-if="showMoreCondition">
              <el-select v-model="queryParams.updateBy" placeholder="请输入更新人" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mall:generalDetail:add']">新增</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="generalDetailList" @selection-change="handleSelectionChange" border>
        <el-table-column type="index" width="55" label="序号" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="详情名称" prop="name">
          <template #header>
            <span>详情名称</span>
            <el-tooltip content="取一个通俗易懂的名称吧，单击了解更多" placement="top">
              <el-icon @click="handleHelpManual">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </template>
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="100">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="排序" prop="sort" width="100" />
        <el-table-column label="备注" prop="remark" show-overflow-tooltip />
        <el-table-column label="更新人昵称" prop="updateNickName" width="150" />
        <el-table-column label="更新时间" prop="updateTime" width="180" />
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="150">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mall:generalDetail:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mall:generalDetail:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改通用详情对话框 -->
    <el-dialog destroy-on-close :title="dialog.title" v-model="dialog.visible" width="60%" append-to-body>
      <el-form ref="generalDetailFormRef" :model="form" :rules="rules" label-width="80px" label-position="top">
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="名称" prop="name">
              <template #label>
                <span>名称</span>
                <el-tooltip content="取一个通俗易懂的名称吧，单击了解更多" placement="top">
                  <el-icon @click="handleHelpManual">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </template>
              <el-input v-model="form.name" placeholder="请输入名称" maxlength="20" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model="form.sort" placeholder="请输入排序" :min="0" :max="999" controls-position="right" />
              <el-tooltip content="当前通用详情的数量" placement="top">
                <el-button link type="primary" icon="MagicStick" @click="getCount()" />
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="详情" prop="detail">
          <editor v-model="form.detail" :min-height="192" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" :rows="5" maxlength="500" show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-checkbox v-if="!dialogEditStatus" v-model="continueCreate" style="float: left; margin-top: 8px">继续新建</el-checkbox>
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="GeneralDetail" lang="ts">
import {
  listGeneralDetail,
  getGeneralDetail,
  delGeneralDetail,
  addGeneralDetail,
  updateGeneralDetail,
  countGeneralDetail
} from '@/api/mall/generalDetail';
import { GeneralDetailVO, GeneralDetailQuery, GeneralDetailForm } from '@/api/mall/generalDetail/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_normal_disable } = toRefs<any>(proxy?.useDict('sys_normal_disable'));

const generalDetailList = ref<GeneralDetailVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑
const continueCreate = ref(true); // 是否继续创建，对应复选框的值

const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const generalDetailFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: GeneralDetailForm = {
  id: undefined,
  name: undefined,
  status: '0',
  sort: 999,
  detail: undefined,
  remark: undefined
};
const data = reactive<PageData<GeneralDetailForm, GeneralDetailQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    status: undefined,
    updateBy: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '状态不能为空', trigger: 'change' }],
    sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
    detail: [{ required: true, message: '详情不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
const { loadUserList, userOptions } = useSysUserSelect();

/** 查询通用详情列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listGeneralDetail(queryParams.value);
  generalDetailList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  generalDetailFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: GeneralDetailVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加通用详情';
  dialogEditStatus.value = false;
  getCount();
};

/** 修改按钮操作 */
const handleUpdate = async (row?: GeneralDetailVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getGeneralDetail(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改通用详情';
  dialogEditStatus.value = true;
};

/** 提交按钮 */
const submitForm = () => {
  generalDetailFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateGeneralDetail(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addGeneralDetail(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      if (continueCreate.value && !dialogEditStatus.value) {
        reset();
        dialogEditStatus.value = false;
        dialog.title = '添加通用详情';
        getList();
        getCount();
      } else {
        dialog.visible = false;
        dialogEditStatus.value = false;
      }
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: GeneralDetailVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除通用详情编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delGeneralDetail(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'mall/generalDetail/export',
    {
      ...queryParams.value
    },
    `generalDetail_${new Date().getTime()}.xlsx`
  );
};

// 帮助文档
const handleHelpManual = () => {
  const routeUrl = `https://rcn3q3ujmqdd.feishu.cn/docx/D0rgdZqQxoTS8PxbEIZcSamLnAg?from=from_copylink`;
  window.open(routeUrl, '_blank');
};

/** 获取已经存在的通用详情的数量 */
const getCount = async () => {
  const res = await countGeneralDetail();
  if (res) {
    initFormData.sort = res.data + (dialogEditStatus.value ? 0 : 1);
  }
  form.value.sort = initFormData.sort;
};

const route = useRoute();
const router = useRouter();
// 监听要打开哪个弹窗
const handleOpenDialog = () => {
  // 1. 打开新增弹窗
  const openAdd = route.query.openAdd;
  if (openAdd === 'true') {
    nextTick(() => {
      handleAdd();
      router.replace({ query: {} });
    });
  }
  // 2. 打开修改弹窗
  const id = route.query.id;
  const openEdit = route.query.openEdit;
  if (id && openEdit === 'true') {
    nextTick(() => {
      handleUpdate({ id: id } as GeneralDetailVO);
      router.replace({ query: {} });
    });
  }
};

onMounted(() => {
  getList();
  loadUserList();
  handleOpenDialog();
});
</script>
