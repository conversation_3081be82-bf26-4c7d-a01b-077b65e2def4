<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="客户" prop="customerId">
              <!-- <el-input v-model="queryParams.customerId" placeholder="请输入客户" clearable @keyup.enter="handleQuery" /> -->
              <customer-select v-model="queryParams.customerId" placeholder="请选择客户档案" @change="handleQuery" />
            </el-form-item>
            <el-form-item label="公司名称" prop="companyName">
              <el-input v-model="queryParams.companyName" placeholder="请输入公司名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" clearable @change="handleQuery">
                <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="接收电话" prop="phone" v-if="showMoreCondition">
              <el-input v-model="queryParams.phone" placeholder="请输入接收电话" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="接收邮箱" prop="email" v-if="showMoreCondition">
              <el-input v-model="queryParams.email" placeholder="请输入接收邮箱" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="更新人" prop="updateBy" v-if="showMoreCondition">
              <!-- <el-input v-model="queryParams.updateBy" placeholder="请输入更新人" clearable @keyup.enter="handleQuery" /> -->
              <el-select v-model="queryParams.updateBy" placeholder="请输入更新人" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['crm:taxInvoiceTitle:add']">新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['crm:taxInvoiceTitle:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['crm:taxInvoiceTitle:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['crm:taxInvoiceTitle:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="taxInvoiceTitleList" @selection-change="handleSelectionChange" border>
        <el-table-column type="index" width="55" label="序号" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="客户" prop="customerId" :min-width="200">
          <template #default="scope">
            <el-link type="primary" @click="openCustomerDetailInView(scope.row.customerId)">
              {{ scope.row.customerName }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="接收电话" prop="phone" :min-width="120" />
        <el-table-column label="接收邮箱" prop="email" :min-width="200" />
        <el-table-column label="公司名称" prop="companyName" :min-width="250" show-overflow-tooltip />
        <el-table-column label="状态" prop="status" :min-width="80">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <!-- <el-table-column label="排序" prop="sort" :min-width="80" /> -->
        <el-table-column label="备注" prop="remark" :min-width="120" show-overflow-tooltip />
        <el-table-column label="更新人" prop="updateNickName" min-width="150" />
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="150">
          <template #default="scope">
            <el-tooltip content="查看" placement="top">
              <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['crm:taxInvoiceTitle:view']"></el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['crm:taxInvoiceTitle:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['crm:taxInvoiceTitle:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改税务发票抬头对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="50%" append-to-body>
      <el-form ref="taxInvoiceTitleFormRef" :model="form" :rules="rules" label-position="top">
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="客户" prop="customerId">
              <!-- <el-input v-model="form.customerId" placeholder="请输入客户" /> -->
              <template #label>
                <span>
                  客户档案
                  <el-tooltip content="打开客户档案" placement="top" v-if="form.customerId">
                    <el-icon @click="openCustomerDetailInView(form.customerId)">
                      <Link />
                    </el-icon>
                  </el-tooltip>
                  <el-tooltip content="新增客户档案" placement="top" v-else>
                    <el-icon @click="openCustomerAddDialog()">
                      <Plus />
                    </el-icon>
                  </el-tooltip>
                </span>
              </template>
              <customer-select v-model="form.customerId" :disabled="dialogEditStatus || isView" @change="handleCustomerChange" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="公司名称" prop="companyName">
              <el-input v-model="form.companyName" placeholder="请输入公司名称" maxlength="50" show-word-limit :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="税号" prop="taxNo">
              <el-input v-model="form.taxNo" placeholder="请输入税号" maxlength="20" show-word-limit :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单位地址" prop="address">
              <el-input v-model="form.address" placeholder="请输入单位地址" maxlength="100" show-word-limit :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单位电话" prop="telephone">
              <el-input v-model="form.telephone" placeholder="请输入单位电话" maxlength="20" show-word-limit :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开户银行" prop="bank">
              <dict-select v-model="form.bank" dict-key="mall_bank_name" placeholder="请选择开户银行" :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="银行账号" prop="bankAccount">
              <el-input v-model="form.bankAccount" placeholder="请输入银行账号" maxlength="50" show-word-limit :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="接收电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入接收电话" maxlength="11" show-word-limit :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="接收邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入接收邮箱" maxlength="50" show-word-limit :disabled="isView" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="6">
            <el-form-item label="排序" prop="sort">
              <el-input-number
                v-model="form.sort"
                placeholder="请输入排序"
                :min="0"
                :max="999"
                controls-position="right"
                :disabled="!form.customerId || isView"
              />
              <el-tooltip content="当前客户的抬头数量" placement="top" v-if="form.customerId && !isView">
                <el-button link type="primary" icon="MagicStick" @click="getTaxInvoiceTitleCount(form.customerId)" />
              </el-tooltip>
            </el-form-item>
          </el-col> -->
          <el-col :span="6">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status" :disabled="isView">
                <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" maxlength="200" show-word-limit :disabled="isView" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TaxInvoiceTitle" lang="ts">
import {
  listTaxInvoiceTitle,
  getTaxInvoiceTitle,
  delTaxInvoiceTitle,
  addTaxInvoiceTitle,
  updateTaxInvoiceTitle,
  countTaxInvoiceTitle
} from '@/api/crm/taxInvoiceTitle';
import { TaxInvoiceTitleVO, TaxInvoiceTitleQuery, TaxInvoiceTitleForm } from '@/api/crm/taxInvoiceTitle/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_normal_disable, mall_bank_name } = toRefs<any>(proxy?.useDict('sys_normal_disable', 'mall_bank_name'));

const taxInvoiceTitleList = ref<TaxInvoiceTitleVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑

import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
import { fa } from 'element-plus/es/locale/index.mjs';
const { loadUserList, userOptions } = useSysUserSelect();

const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const taxInvoiceTitleFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: TaxInvoiceTitleForm = {
  id: undefined,
  customerId: undefined,
  phone: undefined,
  email: undefined,
  companyName: undefined,
  taxNo: undefined,
  address: undefined,
  telephone: undefined,
  bank: undefined,
  bankAccount: undefined,
  status: '0',
  sort: undefined,
  remark: undefined
};
const data = reactive<PageData<TaxInvoiceTitleForm, TaxInvoiceTitleQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    customerId: undefined,
    phone: undefined,
    email: undefined,
    companyName: undefined,
    status: undefined,
    sort: undefined,
    updateBy: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    customerId: [{ required: true, message: '客户不能为空', trigger: 'blur' }],
    phone: [
      {
        pattern: /^1[3-9]\d{9}$/,
        message: '请输入正确的手机号码',
        trigger: 'blur'
      }
    ],
    email: [
      {
        type: 'email',
        message: '请输入正确的邮箱地址',
        trigger: 'blur'
      }
    ],
    companyName: [{ required: true, message: '公司名称不能为空', trigger: 'blur' }],
    taxNo: [
      { required: true, message: '税号不能为空', trigger: 'blur' },
      {
        pattern: /^[0-9A-Z]{15}$|^[0-9A-Z]{18}$|^[0-9A-Z]{20}$/,
        message: '请输入15位、18位或20位的税号',
        trigger: 'blur'
      }
    ],
    status: [{ required: true, message: '状态不能为空', trigger: 'change' }],
    sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 获取路由实例
const route = useRoute();
const router = useRouter();

/** 查询税务发票抬头列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listTaxInvoiceTitle(queryParams.value);
  taxInvoiceTitleList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  isView.value = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  taxInvoiceTitleFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: TaxInvoiceTitleVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加税务发票抬头';
  dialogEditStatus.value = false;
};

/** 修改按钮操作 */
const handleUpdate = async (row?: TaxInvoiceTitleVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getTaxInvoiceTitle(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改税务发票抬头';
  dialogEditStatus.value = true;
  isView.value = false;
};

/** 提交按钮 */
const submitForm = () => {
  taxInvoiceTitleFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateTaxInvoiceTitle(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addTaxInvoiceTitle(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 查看按钮操作 */
const isView = ref(false); // 是否是查看模式, 查看模式下，表单组件处于禁用状态
const handleView = async (row?: TaxInvoiceTitleVO) => {
  reset();
  const res = await getTaxInvoiceTitle(row?.id);
  Object.assign(form.value, res.data);
  isView.value = true;
  dialog.visible = true;
  dialog.title = '查看税务发票抬头';
  dialogEditStatus.value = true;
};

/** 删除按钮操作 */
const handleDelete = async (row?: TaxInvoiceTitleVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除税务发票抬头编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delTaxInvoiceTitle(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'crm/taxInvoiceTitle/export',
    {
      ...queryParams.value
    },
    `taxInvoiceTitle_${new Date().getTime()}.xlsx`
  );
};

// 打开客户详情(查看页面)
const openCustomerDetailInView = (id: string | number) => {
  const routeUrl = `/crm/bizMg/customer/?id=${id}&openView=true`;
  window.open(routeUrl, '_blank');
};

// 打开客户弹窗
const openCustomerAddDialog = () => {
  const routeUrl = `/crm/bizMg/customer/?openAdd=true`;
  window.open(routeUrl, '_blank');
};

/** 获取某客户的抬头数量 */
const getTaxInvoiceTitleCount = async (customerId: string | number) => {
  const res = await countTaxInvoiceTitle(customerId);
  if (res) {
    initFormData.sort = res.data + (dialogEditStatus.value ? 0 : 1);
  }
  form.value.sort = initFormData.sort;
};

/** 客户ID改变 */
const handleCustomerChange = () => {
  getTaxInvoiceTitleCount(form.value.customerId);
};

// 外部跳转逻辑维护
const handleOpenDialog = () => {
  // 1. 打开开票抬头新增
  const customerId = route.query.customerId;
  const openAdd = route.query.openAdd;
  if (customerId && openAdd === 'true') {
    nextTick(() => {
      // 打开新增弹窗，并预填客户ID
      handleAdd();
      // 设置表单中的客户ID
      form.value.customerId = customerId as string;
      // 清除URL参数
      router.replace({ query: {} });
      getTaxInvoiceTitleCount(form.value.customerId);
    });
  }
  // 2. 打开开票抬头详情
  const id = route.query.id;
  const openEdit = route.query.openEdit;
  if (id && openEdit === 'true') {
    nextTick(() => {
      handleUpdate({ id: id } as TaxInvoiceTitleVO);
      router.replace({ query: {} });
    });
  }
  // 3. 打开开票抬头详情(查看页面)
  const openView = route.query.openView;
  if (id && openView === 'true') {
    nextTick(() => {
      handleView({ id: id } as TaxInvoiceTitleVO);
      router.replace({ query: {} });
    });
  }
};

onMounted(() => {
  getList();
  loadUserList();
  handleOpenDialog();
});
</script>
