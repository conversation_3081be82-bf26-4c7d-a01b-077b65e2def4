// 税票抬头选择组件的支撑Hook
// 可以传入客户id，则只查询该客户的税票抬头
// 适合小数据量的查询场景

import { ref, computed } from 'vue';
import { listTaxInvoiceTitle } from '@/api/crm/taxInvoiceTitle';
import type { TaxInvoiceTitleVO, TaxInvoiceTitleQuery } from '@/api/crm/taxInvoiceTitle/types';

/**
 * 税票抬头选择Hook
 * @param props 参数选项，immediate表示是否立即加载数据
 * @returns 相关状态和方法
 */
export const useTaxInvoiceTitleSelect = (props = { immediate: false }) => {
  // 数据状态
  const taxInvoiceTitleOptions = ref<TaxInvoiceTitleVO[]>([]);
  const taxInvoiceTitleLoading = ref(false);
  const searchKeyword = ref('');

  /**
   * 过滤开票抬头选项
   * 根据搜索关键词过滤客户名称
   */
  const filteredOptions = computed(() => {
    if (!searchKeyword.value) return taxInvoiceTitleOptions.value;

    return taxInvoiceTitleOptions.value.filter((item) => item.customerName?.toLowerCase().includes(searchKeyword.value.toLowerCase()));
  });

  /**
   * 加载税票抬头列表
   * @param params 查询参数，可以是客户ID或查询参数对象
   */
  const loadTaxInvoiceTitleList = async (params?: string | number | TaxInvoiceTitleQuery) => {
    try {
      // 显示加载状态
      taxInvoiceTitleLoading.value = true;

      // 构建查询参数
      let queryParams: TaxInvoiceTitleQuery = { pageNum: 1, pageSize: 20 };

      // 根据传入参数类型设置查询条件
      if (typeof params === 'string' || typeof params === 'number') {
        // 如果传入的是字符串或数字，则视为customerId
        queryParams.customerId = params;
      } else if (params && typeof params === 'object') {
        // 如果传入的是对象，则合并查询条件
        queryParams = { ...queryParams, ...params };
      }

      // 调用API获取数据
      const res = await listTaxInvoiceTitle(queryParams);
      taxInvoiceTitleOptions.value = res.rows;
    } catch (error) {
      console.error('加载税票抬头列表失败:', error);
      taxInvoiceTitleOptions.value = [];
    } finally {
      // 无论成功失败都结束加载状态
      taxInvoiceTitleLoading.value = false;
    }
  };

  // 如果需要立即加载数据
  if (props.immediate) {
    loadTaxInvoiceTitleList();
  }

  // 返回状态和方法
  return {
    // 数据状态
    taxInvoiceTitleOptions,
    filteredOptions,
    taxInvoiceTitleLoading,
    searchKeyword,

    // 操作方法
    loadTaxInvoiceTitleList
  };
};
