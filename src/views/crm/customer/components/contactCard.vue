<template>
  <el-row :gutter="10">
    <el-col :span="6" v-for="contact in contactOptions" :key="contact.id">
      <div class="contact-card">
        <div class="contact-card-header" style="display: flex; justify-content: space-between; align-items: center">
          <span>
            <el-tooltip :content="`职务：${contact.position}`" placement="top">
              <el-icon><User /></el-icon>
            </el-tooltip>
            <span style="margin-left: 5px">{{ contact.name }}</span>
            <el-tooltip :content="contact.remark || '<未对该联系人进行备注>'" placement="top">
              <el-icon class="el-icon--right">
                <InfoFilled />
              </el-icon>
            </el-tooltip>
          </span>
          <el-tooltip content="联系人扮演的角色" placement="top">
            <dict-tag :options="dict_contact_role" :value="contact.role" />
          </el-tooltip>
        </div>
        <hr style="border: none; border-top: 1px solid rgb(222, 223, 224)" />
        <div class="contact-card-content">
          <div class="contact-card-content-item">
            <span>
              <el-tooltip content="性别" placement="top">
                <el-icon v-if="contact.gender === '0'" class="icon-style"><Female /></el-icon>
                <el-icon v-else-if="contact.gender === '1'" class="icon-style"><Male /></el-icon>
                <el-icon v-else-if="contact.gender === '2'" class="icon-style"><Guide /></el-icon>
              </el-tooltip>
            </span>
            <el-tooltip content="手机号" placement="top">
              <span style="margin-left: 5px">{{ contact.phone || '--' }}</span>
            </el-tooltip>
          </div>
          <div class="contact-card-content-item">
            <el-space direction="horizontal">
              <el-tooltip content="对我方立场" placement="top">
                <span>
                  <dict-tag :options="dict_position_on_me" :value="contact.positionOnMe || ''" />
                </span>
              </el-tooltip>
              <el-tooltip content="交往程度" placement="top">
                <span>
                  <dict-tag :options="dict_contact_communicate" :value="contact.communicate || ''" />
                </span>
              </el-tooltip>
              <el-tooltip content="在职状态" placement="top" v-if="contact.status !== '1'">
                <span>
                  <dict-tag :options="dict_contact_status" :value="contact.status || ''" />
                </span>
              </el-tooltip>
            </el-space>
          </div>
          <div class="contact-card-content-item">
            <span v-hasPermi="['crm:contact:view']">
              <el-tooltip content="查看" placement="top">
                <span>
                  <el-icon class="el-icon--right" @click="openContactDetailInView(contact.id)"><View /></el-icon>
                </span>
              </el-tooltip>
            </span>
            <span v-hasPermi="['crm:contact:edit']">
              <el-tooltip content="修改" placement="top">
                <span>
                  <el-icon class="el-icon--right" @click="openContactDetailInEdit(contact.id)"><Edit /></el-icon>
                </span>
              </el-tooltip>
            </span>
            <el-tooltip content="更新时间" placement="top">
              <span style="margin-left: 5px; color: #999; font-size: 12px; margin-top: 5px">{{ contact.updateTime }}</span>
            </el-tooltip>
          </div>
        </div>
      </div>
    </el-col>
    <el-col :span="6">
      <div class="contact-card" style="display: flex; justify-content: center; align-items: center">
        <el-tooltip content="添加联系人" placement="top">
          <el-button type="primary" text @click="handleAddContact">
            <el-icon size="30" style="color: #9ecffd"><Plus /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="刷新联系人" placement="top">
          <el-button type="primary" text @click="loadContactList(props.customerId)">
            <el-icon size="30" style="color: #9ecffd"><RefreshLeft /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import { useContactSelect } from '@/views/crm/contact/contactSelect';
const { contactOptions, contactLoading, loadContactList } = useContactSelect();

// 接收客户ID，也即定义组件属性
const props = defineProps<{
  customerId: string | number;
}>();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { dict_contact_role, dict_position_on_me, dict_contact_communicate, dict_contact_status } = toRefs<any>(
  proxy?.useDict('dict_contact_role', 'dict_position_on_me', 'dict_contact_communicate', 'dict_contact_status')
);

/** 添加联系人按钮操作 */
const handleAddContact = () => {
  // 确保当前客户已保存且有ID
  if (!props.customerId) {
    proxy?.$modal.msgError('请先保存客户信息');
    return;
  }

  // 打开联系人新增页面
  const routeUrl = `/crm/bizMg/contact?customerId=${props.customerId}&openAdd=true`;
  window.open(routeUrl, '_blank');
};

// 打开联系人详情(编辑页面)
const openContactDetailInEdit = (id: string | number) => {
  const routeUrl = `/crm/bizMg/contact/?id=${id}&openEdit=true`;
  window.open(routeUrl, '_blank');
};

// 打开联系人详情(查看页面)
const openContactDetailInView = (id: string | number) => {
  const routeUrl = `/crm/bizMg/contact/?id=${id}&openView=true`;
  window.open(routeUrl, '_blank');
};

onMounted(async () => {
  await loadContactList(props.customerId);
});
</script>

<style scoped lang="scss">
@import '@/assets/styles/anchorform.scss';

.contact-card {
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  padding: 10px;
  height: 150px;
  margin-bottom: 10px;

  .contact-card-header {
    font-size: 14px;
    font-weight: 600;
  }

  .contact-card-content {
    margin-top: 10px;

    .contact-card-content-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }
  }
}
</style>
