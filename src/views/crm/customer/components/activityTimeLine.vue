<template>
  <el-timeline>
    <el-timeline-item :timestamp="currentTime" placement="top" type="primary">
      <el-tooltip content="添加跟进记录" placement="top" icon="MoreFilled">
        <el-button type="primary" text @click="handleAddActivity">
          <el-icon size="16" style="color: #9ecffd"><Plus /></el-icon>
        </el-button>
      </el-tooltip>
      <el-tooltip content="刷新跟进记录" placement="top">
        <el-button type="primary" text @click="loadActivityList(customerId)">
          <el-icon size="16" style="color: #9ecffd"><RefreshLeft /></el-icon>
        </el-button>
      </el-tooltip>
    </el-timeline-item>
    <!-- 跟进记录 -->
    <el-timeline-item v-for="activity in activityOptions" :key="activity.id" :timestamp="activity.activityAt" placement="top">
      <div class="line-content">
        <span style="color: #409eff">{{ activity.activityByNickName }}</span>
        <span class="assist-text">通过</span>
        <dict-tag :options="dict_activity_way" :value="activity.activityWay" />
        <span class="assist-text">与客户</span>
        <template v-if="activity.contactName">
          <span class="assist-text">联系人</span>
          <el-tag type="success">{{ activity.contactName }}</el-tag>
        </template>
        <span class="assist-text">进行跟进。</span>
        <span v-hasPermi="['crm:activity:edit']">
          <el-tooltip content="修改跟进记录" placement="top">
            <span>
              <el-icon class="el-icon--right" @click="openActivityDetailInEdit(activity.id)">
                <Edit />
              </el-icon>
            </span>
          </el-tooltip>
        </span>
        <span v-hasPermi="['crm:activity:view']">
          <el-tooltip content="查看跟进记录" placement="top">
            <span>
              <el-icon class="el-icon--right" @click="openActivityDetailInView(activity.id)">
                <View />
              </el-icon>
            </span>
          </el-tooltip>
        </span>
      </div>
      <div class="line-content">
        <dict-tag :options="dict_activity_type" :value="activity.type" />
        <el-tooltip content="纪要详情" placement="top" v-if="activity.remark">
          <el-icon @click="openLink(activity.remark)"><Link /></el-icon>
        </el-tooltip>
        <span>{{ activity.logDesc }}</span>
      </div>
    </el-timeline-item>
  </el-timeline>
</template>

<script setup lang="ts">
import { ref, onMounted, getCurrentInstance, toRefs, type ComponentInternalInstance } from 'vue';
import { useActivitySelect } from '@/views/crm/activity/detail/activitySelect';

const props = defineProps<{
  customerId: string | number;
}>();

const { activityOptions, activityLoading, loadActivityList } = useActivitySelect();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { dict_activity_to, dict_activity_way, dict_activity_type } = toRefs<any>(
  proxy?.useDict('dict_activity_to', 'dict_activity_way', 'dict_activity_type')
);

const handleAddActivity = () => {
  const routeUrl = `/crm/bizMg/activity?customerId=${props.customerId}&openAdd=true`;
  window.open(routeUrl, '_blank');
};

// 当前时间获取
const currentTime = ref('');
const updateCurrentTime = () => {
  const now = new Date();
  const hours = String(now.getHours()).padStart(2, '0'); // 获取小时并补零
  const minutes = String(now.getMinutes()).padStart(2, '0'); // 获取分钟并补零
  const seconds = String(now.getSeconds()).padStart(2, '0'); // 获取秒并补零
  currentTime.value = `${hours}:${minutes}:${seconds}`; // 格式化为 HH:MM:SS
};

// 新窗口打开外部链接
const openLink = (url: string) => {
  window.open(url, '_blank');
};

// 打开跟进记录详情(编辑页面)
const openActivityDetailInEdit = (id: string | number) => {
  const routeUrl = `/crm/bizMg/activity?id=${id}&openEdit=true`;
  window.open(routeUrl, '_blank');
};

// 打开跟进记录详情(查看页面)
const openActivityDetailInView = (id: string | number) => {
  const routeUrl = `/crm/bizMg/activity?id=${id}&openView=true`;
  window.open(routeUrl, '_blank');
};

onMounted(() => {
  updateCurrentTime();
  setInterval(updateCurrentTime, 1000);
  loadActivityList(props.customerId);
});
</script>

<style scoped lang="scss">
.activity-time-line {
  width: 100%;
  height: 100%;
}

.assist-text {
  color: #b2b3b7;
}

.line-content {
  display: flex;
  flex-direction: row;
  gap: 3px;
  margin-top: 5px;
  margin-bottom: 5px;
}
</style>
