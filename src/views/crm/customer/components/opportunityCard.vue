<template>
  <el-row :gutter="10">
    <el-col :span="6" v-for="opportunity in opportunityOptions" :key="opportunity.id">
      <div class="contact-card">
        <div class="contact-card-header">
          <span class="contact-card-header-title">{{ opportunity.name }}</span>
          <el-tooltip content="商机阶段" placement="top">
            <span>
              <dict-tag :options="dict_opportunity_phase" :value="opportunity.phase" />
            </span>
          </el-tooltip>
        </div>
        <hr style="border: none; border-top: 1px solid rgb(222, 223, 224)" />
        <div class="contact-card-content">
          <div class="contact-cart-contend-item">
            <el-tooltip content="预计成交日期" placement="top">
              <span>{{ parseTime(opportunity.expectDate, '{y}-{m}-{d}') }}</span>
            </el-tooltip>
            <span class="assist-text">成交,预估</span>
            <span style="margin-right: 10px">{{ convertToWan(opportunity.expectAmount) }}</span>
          </div>
          <div class="contact-card-content-item">
            <el-tooltip content="采购用途" placement="top">
              <span>
                <dict-tag :options="dict_demand_purpose" :value="opportunity.demandPurpose" class="mr-[5px]" />
              </span>
            </el-tooltip>
            <el-tooltip content="意向产品" placement="top">
              <span>
                <dict-tag :options="dict_crm_pro" :value="opportunity.productKey" />
              </span>
            </el-tooltip>
          </div>
          <div class="contact-card-content-item">
            <span v-hasPermi="['crm:opportunity:view']">
              <el-tooltip content="查看需求描述" placement="top">
                <span>
                  <el-icon class="el-icon--right" @click="openOpportunityDetailInView(opportunity.id)"><View /></el-icon>
                </span>
              </el-tooltip>
            </span>
            <template v-if="opportunity.saleStrategy">
              <el-tooltip content="销售策略详情" placement="top">
                <span>
                  <el-icon @click="openLink(opportunity.saleStrategy)"><Link /></el-icon>
                </span>
              </el-tooltip>
            </template>
            <span v-else v-hasPermi="['crm:opportunity:edit']">
              <el-tooltip content="未粘贴销售策略，请粘贴" placement="top">
                <span>
                  <el-icon @click="openOpportunityDetailInEdit(opportunity.id)"><Link /></el-icon>
                </span>
              </el-tooltip>
            </span>
            <span v-hasPermi="['crm:opportunity:edit']">
              <el-tooltip :content="opportunity.demandDesc || '<未对该商机进行备注>'" placement="top">
                <span>
                  <el-icon class="el-icon--right" @click="openOpportunityDetailInEdit(opportunity.id)">
                    <InfoFilled />
                  </el-icon>
                </span>
              </el-tooltip>
            </span>
          </div>
        </div>
      </div>
    </el-col>
    <!-- 添加和刷新操作 -->
    <el-col :span="6">
      <div class="contact-card" style="display: flex; justify-content: center; align-items: center">
        <el-tooltip content="添加销售机会" placement="top">
          <el-button type="primary" text @click="handleAddOpportunity">
            <el-icon size="30" style="color: #9ecffd"><Plus /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="刷新销售机会" placement="top">
          <el-button type="primary" text @click="loadOpportunityList(props.customerId)">
            <el-icon size="30" style="color: #9ecffd"><RefreshLeft /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import { ref, onMounted, getCurrentInstance, toRefs, type ComponentInternalInstance } from 'vue';
import { parseTime } from '@/utils/ruoyi';
import { useOpportunitySelect } from '@/views/crm/opportunity/detail/opportunitySelect';
import { convertToWan } from '@/utils/convertToWan';

const { opportunityOptions, opportunityLoading, loadOpportunityList } = useOpportunitySelect();

const props = defineProps({
  customerId: {
    type: [String, Number],
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { dict_opportunity_phase, dict_demand_purpose, dict_crm_pro } = toRefs<any>(
  proxy?.useDict('dict_opportunity_phase', 'dict_demand_purpose', 'dict_crm_pro')
);

const handleAddOpportunity = () => {
  console.log(props.customerId);
  const routeUrl = `/crm/bizMg/opportunity?customerId=${props.customerId}&openAdd=true`;
  window.open(routeUrl, '_blank');
};

// 新窗口打开链接
const openLink = (url: string) => {
  window.open(url, '_blank');
};

// 打开销售机会详情(编辑页面)
const openOpportunityDetailInEdit = (id: string | number) => {
  const routeUrl = `/crm/bizMg/opportunity/?id=${id}&openEdit=true`;
  window.open(routeUrl, '_blank');
};

// 打开销售机会详情(查看页面)
const openOpportunityDetailInView = (id: string | number) => {
  const routeUrl = `/crm/bizMg/opportunity/?id=${id}&openView=true`;
  window.open(routeUrl, '_blank');
};

onMounted(() => {
  loadOpportunityList(props.customerId);
});
</script>

<style scoped lang="scss">
.contact-card {
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  padding: 10px;
  height: 150px;
  margin-bottom: 10px;

  .contact-card-header {
    font-size: 14px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .contact-card-content {
    margin-top: 10px;

    .contact-card-content-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      margin-top: 10px;
      span {
        margin-right: 2px;
      }
    }
  }
  .assist-text {
    color: #b2b3b7;
    margin-right: 5px;
  }
}
</style>
