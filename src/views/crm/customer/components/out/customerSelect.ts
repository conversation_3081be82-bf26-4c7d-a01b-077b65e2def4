// 客户选择组件的支撑，可以传入客户id，则只查询该客户的销售机会
// 这里只适合小数据量的查询

import { ref } from 'vue';
import { listCustomer } from '@/api/crm/customer';
import type { CustomerVO } from '@/api/crm/customer/types';

export const useCustomerSelect = () => {
  const customerOptions = ref<CustomerVO[]>([]);
  const customerLoading = ref(false);

  const loadCustomerList = async (name?: string) => {
    try {
      customerLoading.value = true;
      const res = await listCustomer({ name: name, pageNum: 1, pageSize: 10 });
      customerOptions.value = res.rows;
    } finally {
      customerLoading.value = false;
    }
  };

  return {
    customerOptions,
    customerLoading,
    loadCustomerList
  };
};
