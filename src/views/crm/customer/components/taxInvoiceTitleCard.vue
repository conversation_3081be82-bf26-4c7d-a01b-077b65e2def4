<template>
  <el-row :gutter="10">
    <el-col :span="6" v-for="taxInvoiceTitle in taxInvoiceTitleOptions" :key="taxInvoiceTitle.id">
      <div class="contact-card">
        <div class="contact-card-header">
          <el-tooltip content="开票抬头" placement="top">
            <span class="contact-card-header-title">
              <el-text class="w-170px" truncated>
                {{ taxInvoiceTitle.companyName }}
              </el-text>
            </span>
          </el-tooltip>

          <el-tooltip content="状态" placement="top">
            <dict-tag :options="sys_normal_disable" :value="taxInvoiceTitle.status" />
          </el-tooltip>
        </div>
        <hr style="border: none; border-top: 1px solid rgb(222, 223, 224)" />
        <div class="contact-card-content">
          <div class="contact-card-content-item">
            <el-tooltip content="税号" placement="top">
              <span>{{ taxInvoiceTitle.taxNo }}</span>
            </el-tooltip>
          </div>
          <div class="contact-card-content-item">
            <el-tooltip content="开户银行" placement="top">
              <dict-tag :options="mall_bank_name" :value="taxInvoiceTitle.bank" />
            </el-tooltip>
            <el-tooltip content="银行账号" placement="top">
              <el-tooltip :content="`银行账号：${taxInvoiceTitle.bankAccount}`" placement="top" v-if="taxInvoiceTitle.bankAccount">
                <el-icon size="16" style="color: #b2b3b7"><Postcard /></el-icon>
              </el-tooltip>
            </el-tooltip>
            <el-tooltip :content="`接收发票邮箱：${taxInvoiceTitle.email}`" placement="top" v-if="taxInvoiceTitle.email">
              <el-icon size="16" style="color: #b2b3b7"><Message /></el-icon>
            </el-tooltip>
            <el-tooltip :content="`接收发票手机：${taxInvoiceTitle.phone}`" placement="top" v-if="taxInvoiceTitle.phone">
              <el-icon size="16" style="color: #b2b3b7"><Iphone /></el-icon>
            </el-tooltip>
          </div>
          <div class="contact-card-content-item">
            <span v-hasPermi="['crm:taxInvoiceTitle:edit']">
              <el-tooltip content="编辑" placement="top">
                <el-icon class="el-icon--right" @click="handleEditTaxInvoiceTitle(taxInvoiceTitle.id)">
                  <Edit />
                </el-icon>
              </el-tooltip>
            </span>
            <span v-hasPermi="['crm:taxInvoiceTitle:view']">
              <el-tooltip content="查看" placement="top">
                <el-icon class="el-icon--right" @click="handleViewTaxInvoiceTitle(taxInvoiceTitle.id)">
                  <View />
                </el-icon>
              </el-tooltip>
            </span>
          </div>
        </div>
      </div>
    </el-col>
    <!-- 添加和刷新操作 -->
    <el-col :span="6">
      <div class="contact-card" style="display: flex; justify-content: center; align-items: center">
        <el-tooltip content="添加开票抬头" placement="top">
          <el-button type="primary" text @click="handleAddTaxInvoiceTitle">
            <el-icon size="30" style="color: #9ecffd"><Plus /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="刷新开票抬头" placement="top">
          <el-button type="primary" text @click="loadTaxInvoiceTitleList(props.customerId)">
            <el-icon size="30" style="color: #9ecffd"><RefreshLeft /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </el-col>
  </el-row>
</template>
<script setup lang="ts">
import { ref, onMounted, getCurrentInstance, toRefs, type ComponentInternalInstance } from 'vue';
import { useTaxInvoiceTitleSelect } from '@/views/crm/taxInvoiceTitle/detail/taxInvoiceTitleSelect';
import { Postcard } from '@element-plus/icons-vue';

const { taxInvoiceTitleOptions, taxInvoiceTitleLoading, loadTaxInvoiceTitleList } = useTaxInvoiceTitleSelect();

const props = defineProps({
  customerId: {
    type: [String, Number],
    required: true
  }
});

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_bank_name, sys_normal_disable } = toRefs<any>(proxy?.useDict('mall_bank_name', 'sys_normal_disable'));

// 添加开票抬头
const handleAddTaxInvoiceTitle = () => {
  const routeUrl = `/crm/bizMg/taxInvoiceTitle?customerId=${props.customerId}&openAdd=true`;
  window.open(routeUrl, '_blank');
};

// 编辑开票抬头
const handleEditTaxInvoiceTitle = (id: string | number) => {
  const routeUrl = `/crm/bizMg/taxInvoiceTitle?id=${id}&openEdit=true`;
  window.open(routeUrl, '_blank');
};

// 查看开票抬头
const handleViewTaxInvoiceTitle = (id: string | number) => {
  const routeUrl = `/crm/bizMg/taxInvoiceTitle?id=${id}&openView=true`;
  window.open(routeUrl, '_blank');
};

onMounted(() => {
  loadTaxInvoiceTitleList(props.customerId);
});
</script>

<style scoped lang="scss">
.contact-card {
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  padding: 10px;
  height: 150px;
  margin-bottom: 10px;

  .contact-card-header {
    font-size: 14px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .contact-card-content {
    margin-top: 10px;

    .contact-card-content-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      margin-top: 10px;
      gap: 5px;
      span {
        margin-right: 2px;
      }
    }
  }
  .assist-text {
    color: #b2b3b7;
    margin-right: 5px;
  }
}
</style>
