<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px" class="search-form-container">
            <el-form-item label="客户群组" prop="customerGroup" v-if="showMoreCondition">
              <el-select v-model="queryParams.customerGroup" placeholder="请选择客户群组" clearable @change="handleQuery">
                <el-option v-for="dict in dict_customer_group" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>

            <el-form-item label="客户名称" prop="name">
              <template #label>
                <span>
                  客户名称
                  <el-tooltip content="最终请维护与ERP中的名称保持一致，即合同签订主体。" placement="top">
                    <el-icon class="el-icon--right">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-input v-model="queryParams.name" placeholder="请输入客户名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>

            <el-form-item label="客户别称" prop="aliasName" v-if="showMoreCondition">
              <el-input v-model="queryParams.aliasName" placeholder="请输入客户别称" clearable @keyup.enter="handleQuery" />
            </el-form-item>

            <el-form-item label="销售场景" prop="saleScene" v-if="showMoreCondition">
              <dict-select
                v-model="queryParams.saleScene"
                dict-key="dict_crm_tag"
                placeholder="请选择销售场景"
                clearable
                @change="handleQuery"
                :show-footer="false"
              />
            </el-form-item>

            <el-form-item label="客户企业规模" prop="companyScale" v-if="showMoreCondition">
              <dict-select
                v-model="queryParams.companyScale"
                dict-key="dict_customer_scale"
                placeholder="请选择客户企业规模"
                clearable
                @change="handleQuery"
              />
            </el-form-item>

            <el-form-item label="意向产品" prop="productKey" v-if="showMoreCondition">
              <dict-select v-model="queryParams.productKey" dict-key="dict_crm_pro" placeholder="请选择意向产品" clearable @change="handleQuery" />
            </el-form-item>
            <!-- <el-form-item label="客户唯一编码" prop="openCode" v-if="showMoreCondition">
              <el-input v-model="queryParams.openCode" placeholder="请输入客户唯一编码" clearable @keyup.enter="handleQuery" />
            </el-form-item> -->

            <el-form-item label="客户状态" prop="status">
              <template #label>
                <span>
                  客户状态
                  <el-tooltip content="了解更多" placement="top">
                    <el-icon @click="openLink('https://rcn3q3ujmqdd.feishu.cn/docx/KvJxdPIQdoPSchxGEj2c95t8nfh?from=from_copylink')">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-select v-model="queryParams.status" placeholder="请选择客户状态" clearable @change="handleQuery">
                <el-option v-for="dict in dict_customer_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>

            <el-form-item label="所在行业" prop="industry">
              <el-select v-model="queryParams.industry" placeholder="请选择所在行业" clearable @change="handleQuery">
                <el-option v-for="dict in dict_industry" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>

            <el-form-item label="采购用途" prop="demandPurpose">
              <el-select v-model="queryParams.demandPurpose" placeholder="请选择采购用途" clearable @change="handleQuery">
                <el-option v-for="dict in dict_demand_purpose" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>

            <el-form-item label="客户级别" prop="level">
              <el-select v-model="queryParams.level" placeholder="请选择客户级别" clearable @change="handleQuery">
                <el-option v-for="dict in dict_customer_level" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>

            <el-form-item label="客户类型" prop="type">
              <el-select v-model="queryParams.type" placeholder="请选择客户类型" clearable @change="handleQuery">
                <el-option v-for="dict in dict_customer_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>

            <el-form-item label="是否已签合同" prop="isSignedContract" v-if="showMoreCondition">
              <dict-select
                v-model="queryParams.isSignedContract"
                dict-key="sys_yes_no"
                placeholder="请选择是否已签合同"
                clearable
                @change="handleQuery"
                :show-footer="false"
              />
            </el-form-item>

            <el-form-item label="承做部门" prop="ownerDept" v-if="showMoreCondition">
              <el-tree-select
                v-model="queryParams.ownerDept"
                :data="deptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                placeholder="请选择承做部门"
                check-strictly
                filterable
                clearable
                @change="handleDeptChangeQuery"
              />
            </el-form-item>

            <el-form-item label="承做人" prop="owner" v-if="showMoreCondition">
              <el-select v-model="queryParams.owner" placeholder="请输入承做人" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>

            <el-form-item label="协做人" prop="coOwner" v-if="showMoreCondition">
              <el-select v-model="queryParams.coOwner" placeholder="请输入协做人" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>

            <el-form-item label="下次跟进日期" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeFollowUpDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>

            <el-form-item label="客户公海" prop="highSeaId" v-if="showMoreCondition">
              <high-sea-select v-model="queryParams.highSeaId" @change="handleQuery" />
            </el-form-item>

            <el-form-item label="客户来源" prop="source" v-if="showMoreCondition">
              <el-select v-model="queryParams.source" placeholder="请选择客户来源" clearable @change="handleQuery">
                <el-option v-for="dict in dict_crm_resource" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>

            <el-form-item label="客户编码" prop="customerCode" v-if="showMoreCondition">
              <template #label>
                <span>客户编码</span>
                <el-tooltip content="取自授信系统，单击了解更多" placement="top">
                  <el-icon
                    class="el-icon--right"
                    @click="openLink('https://rcn3q3ujmqdd.feishu.cn/docx/IoLGd4mTMoRIzNx3mtzcjf0unRh?from=from_copylink')"
                  >
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </template>
              <el-input v-model="queryParams.customerCode" placeholder="请输入客户编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>

            <el-form-item label="省份" prop="provinceId" v-if="showMoreCondition">
              <el-select
                v-model="queryParams.provinceId"
                placeholder="请选择省份"
                clearable
                @change="handleSelectProvinceInQuery"
                @visible-change="loadProvinceList"
                filterable
              >
                <el-option v-for="item in provinceOptions" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>

            <el-form-item label="城市" prop="cityId" v-if="showMoreCondition">
              <el-select
                v-model="queryParams.cityId"
                placeholder="请选择城市"
                clearable
                @change="handleQuery"
                filterable
                :disabled="!queryParams.provinceId"
              >
                <el-option v-for="item in cityOptions" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>

            <el-form-item label="更新人" prop="updateBy" v-if="showMoreCondition">
              <el-select v-model="queryParams.updateBy" placeholder="请输入更新人" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>

            <el-form-item label="更新时间" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="排序" prop="orderByColumn">
              <el-select v-model="queryParams.orderByColumn" placeholder="请选择排序" clearable @change="handleQuery">
                <el-option label="成交额" value="lastYearDealAmount" />
                <el-option label="下次跟进日期" value="followUpDate" />
                <el-option label="客户级别" value="level" />
              </el-select>
            </el-form-item>
            <el-form-item label="排序方式" prop="orderByColumn">
              <el-select v-model="queryParams.isAsc" placeholder="请选择排序" clearable @change="handleQuery">
                <el-option v-for="item in sys_sort_type" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>

            <el-form-item class="search-buttons">
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['crm:customer:add']">新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['crm:customer:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['crm:customer:remove']"
              >删除</el-button
            >
          </el-col> -->
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['crm:customer:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="customerList" @selection-change="handleSelectionChange" border :row-class-name="tableRowClassName">
        <!-- <el-table-column type="selection" width="40" align="center" /> -->
        <el-table-column label="#" type="index" align="center" fixed="left" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="客户名称" prop="name" min-width="180" show-overflow-tooltip fixed="left">
          <template #header>
            <span>
              客户名称
              <el-tooltip content="最终请维护与ERP中的名称保持一致，即合同签订主体。" placement="top">
                <el-icon class="el-icon--right">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </span>
          </template>
          <template #default="scope">
            <el-tooltip :content="`唯一编码：${scope.row.openCode}`" placement="top">
              <span>{{ scope.row.name }}</span>
            </el-tooltip>
            <div style="color: #999; font-size: 12px">{{ scope.row.aliasName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="客户状态" prop="status" min-width="100">
          <template #header>
            <span>
              客户状态
              <el-tooltip content="了解更多" placement="top">
                <el-icon @click="openLink('https://rcn3q3ujmqdd.feishu.cn/docx/KvJxdPIQdoPSchxGEj2c95t8nfh?from=from_copylink')">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </span>
          </template>
          <template #default="scope">
            <dict-tag :options="dict_customer_status" :value="scope.row.status || ''" />
          </template>
        </el-table-column>
        <el-table-column label="客户编码" prop="customerCode" min-width="150">
          <template #header>
            <span>客户编码</span>
            <el-tooltip content="取自授信系统，单击了解更多" placement="top">
              <el-icon class="el-icon--right" @click="openLink('https://rcn3q3ujmqdd.feishu.cn/docx/IoLGd4mTMoRIzNx3mtzcjf0unRh?from=from_copylink')">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </template>
          <template #default="scope">
            <span>{{ scope.row.customerCode }}</span>
          </template>
        </el-table-column>
        <el-table-column label="联系人数量" prop="contactNum" min-width="100">
          <template #default="scope">
            <span :style="{ color: scope.row.contactNum === 0 ? 'red' : '', fontSize: scope.row.contactNum === 0 ? '18px' : '' }">{{
              scope.row.contactNum
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="承做人" prop="ownerNickName" min-width="150">
          <template #default="scope">
            <span>{{ scope.row.ownerNickName || '- -' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="销售场景" prop="saleScene" min-width="150" show-overflow-tooltip>
          <template #default="scope">
            <dict-tag :options="dict_crm_tag" :value="scope.row.saleScene" />
          </template>
        </el-table-column>
        <el-table-column label="客户规模" prop="companyScale" min-width="100">
          <template #default="scope">
            <dict-tag :options="dict_customer_scale" :value="scope.row.companyScale || ''" />
          </template>
        </el-table-column>
        <el-table-column label="意向产品" prop="productKey" min-width="200">
          <template #default="scope">
            <dict-tag :options="dict_crm_pro" :value="scope.row.productKey || []" />
          </template>
        </el-table-column>
        <el-table-column label="客户级别" prop="level" min-width="100">
          <template #default="scope">
            <dict-tag :options="dict_customer_level" :value="scope.row.level || ''" />
          </template>
        </el-table-column>
        <el-table-column label="客户类型" prop="type" min-width="100">
          <template #default="scope">
            <dict-tag :options="dict_customer_type" :value="scope.row.type || ''" />
          </template>
        </el-table-column>
        <el-table-column label="客户来源" prop="source" min-width="100">
          <template #default="scope">
            <dict-tag :options="dict_crm_resource" :value="scope.row.source || ''" />
          </template>
        </el-table-column>
        <el-table-column label="所在行业" prop="industry" min-width="120">
          <template #default="scope">
            <dict-tag :options="dict_industry" :value="scope.row.industry || ''" />
          </template>
        </el-table-column>
        <el-table-column label="采购用途" prop="demandPurpose" min-width="200">
          <template #default="scope">
            <dict-tag :options="dict_demand_purpose" :value="scope.row.demandPurpose || []" />
          </template>
        </el-table-column>
        <el-table-column label="下次跟进日期" prop="followUpDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.followUpDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="公海" prop="highSeaId" min-width="200" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ findHighSeaName(scope.row.highSeaId) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="上一年成交额(元)" prop="lastYearDealAmount" min-width="150">
          <template #default="scope">
            <span>{{ scope.row.lastYearDealAmount || '- -' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="协做人" prop="coOwner" min-width="150">
          <template #default="scope">
            <span>{{ scope.row.coOwnerNickName || '- -' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="承做部门" prop="ownerDept" min-width="150">
          <template #default="scope">
            <span>{{ findDeptLabel(scope.row.ownerDept) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="省份" prop="provinceName" />
        <el-table-column label="城市" prop="cityName" />
        <el-table-column label="备注" prop="remark" width="150" show-overflow-tooltip />
        <el-table-column label="更新人" prop="updateBy" width="150">
          <template #default="scope">
            <span>{{ scope.row.updateByNickName || '- -' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="150">
          <template #default="scope">
            <el-tooltip content="查看" placement="top">
              <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['crm:customer:view']" />
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['crm:customer:edit']" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top" v-if="canOperated(scope.row.createTime)">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['crm:customer:remove']" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改客户档案对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="80%" append-to-body @close="cancel">
      <el-row :gutter="10">
        <!-- 左侧锚点导航 -->
        <el-col :span="4">
          <el-anchor :default-active="currentAnchor">
            <el-anchor-link href="#basic" title="基础信息" />
            <el-anchor-link href="#owner" title="承做信息" />
            <el-anchor-link href="#business" title="工商信息" />
            <el-anchor-link href="#address" title="地址信息" />
            <el-anchor-link href="#contact" title="联系信息" v-if="dialogEditStatus && form.id" />
            <el-anchor-link href="#followUp" title="跟进记录" v-if="dialogEditStatus && form.id" />
            <el-anchor-link href="#opportunity" title="销售机会" v-if="dialogEditStatus && form.id" />
            <el-anchor-link href="#taxInvoiceTitle" title="开票抬头" v-if="dialogEditStatus && form.id" />
            <el-anchor-link href="#collection" title="收款计划" v-if="dialogEditStatus" />
            <el-anchor-link href="#contract" title="合同信息" v-if="dialogEditStatus" />
            <el-anchor-link href="#note" title="备注信息" />
          </el-anchor>
        </el-col>

        <!-- 右侧表单内容 -->
        <el-col :span="20">
          <el-form ref="customerFormRef" :model="form" :rules="rules" label-width="80px" label-position="top" scroll-to-error>
            <!-- 基础信息 -->
            <div id="basic" class="form-section">
              <div class="section-title">
                <span class="title-text">基础信息</span>
              </div>
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-form-item label="客户名称" prop="name">
                    <template #label>
                      <span>
                        客户名称
                        <el-tooltip content="最终请维护与ERP中的名称保持一致,即合同签订主体。" placement="top">
                          <el-icon class="el-icon--right">
                            <QuestionFilled />
                          </el-icon>
                        </el-tooltip>
                      </span>
                    </template>
                    <el-input v-model="form.name" placeholder="请输入客户名称" :disabled="isView" minlength="2" maxlength="50" show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="销售场景" prop="saleScene">
                    <dict-select v-model="form.saleScene" dict-key="dict_crm_tag" placeholder="请选择销售场景" :disabled="isView" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="客户群组" prop="customerGroup">
                    <dict-select v-model="form.customerGroup" dict-key="dict_customer_group" placeholder="请选择客户群组" :disabled="isView" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="客户别称" prop="aliasName">
                    <el-input v-model="form.aliasName" placeholder="请输入客户别称" :disabled="isView" minlength="2" maxlength="20" show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="客户状态" prop="status">
                    <template #label>
                      <span>
                        客户状态
                        <el-tooltip content="了解更多" placement="top">
                          <el-icon @click="openLink('https://rcn3q3ujmqdd.feishu.cn/docx/KvJxdPIQdoPSchxGEj2c95t8nfh?from=from_copylink')">
                            <QuestionFilled />
                          </el-icon>
                        </el-tooltip>
                      </span>
                    </template>
                    <el-select v-model="form.status" placeholder="请选择客户状态" :disabled="isView" v-if="!dialogEditStatus">
                      <el-option v-for="dict in dict_customer_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                    </el-select>
                    <el-select v-model="form.status" placeholder="请选择客户状态" :disabled="isView" v-else>
                      <el-option v-for="item in filteredStatus" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="ERP编码" prop="customerCode" :required="form.status === '11'">
                    <template #label>
                      <span>
                        <!-- ERP编码 -->
                        客户编码
                        <el-tooltip content="取自授信系统，单击了解更多" placement="top">
                          <el-icon
                            class="el-icon--right"
                            @click="openLink('https://rcn3q3ujmqdd.feishu.cn/docx/IoLGd4mTMoRIzNx3mtzcjf0unRh?from=from_copylink')"
                          >
                            <QuestionFilled />
                          </el-icon>
                        </el-tooltip>
                      </span>
                    </template>
                    <el-input
                      v-model="form.customerCode"
                      placeholder="ERP中对应的编码"
                      :disabled="form.status !== '11' || isView"
                      maxlength="50"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="客户级别" prop="level">
                    <template #label>
                      <span>
                        客户级别
                        <el-tooltip content="了解更多" placement="top">
                          <el-icon
                            class="el-icon--right"
                            @click="openLink('https://rcn3q3ujmqdd.feishu.cn/docx/IR6dddgNdox9QLxDS11ck95fnvc?from=from_copylink')"
                          >
                            <QuestionFilled />
                          </el-icon>
                        </el-tooltip>
                      </span>
                    </template>
                    <dict-select v-model="form.level" dict-key="dict_customer_level" placeholder="请选择客户级别" :disabled="isView" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="客户类型" prop="type">
                    <template #label>
                      <span>客户类型</span>
                      <el-tooltip content="了解更多" placement="top">
                        <el-icon
                          class="el-icon--right"
                          @click="openLink('https://rcn3q3ujmqdd.feishu.cn/docx/IR6dddgNdox9QLxDS11ck95fnvc?from=from_copylink')"
                        >
                          <QuestionFilled />
                        </el-icon>
                      </el-tooltip>
                    </template>
                    <dict-select v-model="form.type" dict-key="dict_customer_type" placeholder="请选择客户类型" :disabled="isView" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="客户来源" prop="source">
                    <template #label>
                      <span>客户来源</span>
                      <el-tooltip content="了解更多" placement="top">
                        <el-icon
                          class="el-icon--right"
                          @click="openLink('https://rcn3q3ujmqdd.feishu.cn/docx/IR6dddgNdox9QLxDS11ck95fnvc?from=from_copylink')"
                        >
                          <QuestionFilled />
                        </el-icon>
                      </el-tooltip>
                    </template>
                    <dict-select v-model="form.source" dict-key="dict_crm_resource" placeholder="请选择客户来源" :disabled="isView" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="所在行业" prop="industry">
                    <dict-select v-model="form.industry" dict-key="dict_industry" placeholder="请选择所在行业" :disabled="isView" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="采购用途" prop="demandPurpose">
                    <dict-select
                      v-model="form.demandPurpose"
                      dict-key="dict_demand_purpose"
                      placeholder="请选择采购用途"
                      multiple
                      :disabled="isView"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="意向产品" prop="productKey">
                    <dict-select v-model="form.productKey" dict-key="dict_crm_pro" placeholder="请选择意向产品" :disabled="isView" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="客户规模" prop="companyScale">
                    <dict-select v-model="form.companyScale" dict-key="dict_customer_scale" placeholder="请选择客户规模" :disabled="isView" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="上一年成交额(元)" prop="lastYearDealAmount">
                    <el-input-number
                      v-model="form.lastYearDealAmount"
                      placeholder="支持两位小数"
                      :disabled="isView"
                      :precision="2"
                      :step="0.01"
                      :min="0"
                      :max="99999999.99"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="是否已签合同" prop="isSignedContract">
                    <dict-select
                      v-model="form.isSignedContract"
                      dict-key="sys_yes_no"
                      placeholder="请选择是否已签合同"
                      :show-footer="false"
                      :disabled="isView"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="客户描述" prop="customerDesc">
                    <el-input
                      v-model="form.customerDesc"
                      type="textarea"
                      placeholder="请输入内容"
                      :rows="5"
                      :disabled="isView"
                      maxlength="500"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 承做信息 -->
            <div id="owner" class="form-section">
              <div class="section-title">
                <span class="title-text">承做信息</span>
              </div>
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-form-item label="所属公海" prop="highSeaId">
                    <high-sea-select v-model="form.highSeaId" @change="handleHighSeaChange" :disabled="isView" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="承做部门" prop="ownerDept">
                    <el-tree-select
                      v-model="form.ownerDept"
                      :data="deptOptions"
                      :props="{ value: 'id', label: 'label', children: 'children' }"
                      value-key="id"
                      placeholder="请选择承做部门"
                      check-strictly
                      filterable
                      :disabled="isView"
                      @change="handleDeptChangeInForm"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="承做人（A角）" prop="owner" :required="!!form.ownerDept">
                    <el-select
                      v-model="form.owner"
                      placeholder="请输入承做人"
                      clearable
                      @change="handleOwnerChange"
                      filterable
                      :disabled="!form.ownerDept || isView"
                    >
                      <el-option v-for="item in userOptionsByDeptId" :key="item.userId" :label="item.nickName" :value="item.userId" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="协做人（B角）" prop="coOwner">
                    <el-select v-model="form.coOwner" placeholder="请输入协做人" clearable filterable :disabled="!form.ownerDept || isView">
                      <el-option v-for="item in userOptionsByDeptId" :key="item.userId" :label="item.nickName" :value="item.userId" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="下次跟进日期" prop="followUpDate" :required="!!form.ownerDept">
                    <el-date-picker
                      clearable
                      v-model="form.followUpDate"
                      type="date"
                      value-format="YYYY-MM-DD"
                      placeholder="请选择下次跟进日期"
                      style="width: 100%"
                      :disabled="!form.ownerDept || isView"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 工商信息 -->
            <div id="business" class="form-section">
              <div class="section-title">
                <span class="title-text">工商信息</span>
              </div>
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-form-item label="税号" prop="tin">
                    <el-input v-model="form.tin" placeholder="请输入税号" :disabled="isView" maxlength="20" show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="公司名称" prop="companyName">
                    <el-input v-model="form.companyName" placeholder="请输入公司名称" :disabled="isView" maxlength="100" show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="法人代表" prop="legalPerson">
                    <el-input v-model="form.legalPerson" placeholder="请输入法人代表" :disabled="isView" maxlength="20" show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="经营范围" prop="bizScope">
                    <el-input v-model="form.bizScope" type="textarea" placeholder="请输入内容" :disabled="isView" maxlength="100" show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="营业执照" prop="bizLicense">
                    <file-upload v-model="form.bizLicense" :disabled="isView" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 地址信息 -->
            <div id="address" class="form-section">
              <div class="section-title">
                <span class="title-text">地址信息</span>
              </div>
              <el-row :gutter="10">
                <el-col :span="8">
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form-item label="省市区县" prop="address">
                        <el-cascader
                          v-model="selectedOptions"
                          filterable
                          style="width: 100%"
                          :options="areaOptions"
                          @change="handleAreaChange"
                          :disabled="isView"
                        >
                        </el-cascader>
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <map-select v-model="mapAddress" @change="handleMapAddressChange" :disabled="isView" />
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </div>

            <!-- 联系信息 -->
            <div id="contact" class="form-section" v-if="dialogEditStatus && form.id">
              <div class="section-title">
                <span class="title-text">联系信息</span>
              </div>
              <contact-card :customer-id="form.id" :disabled="isView" />
            </div>

            <!-- 跟进记录 -->
            <div id="followUp" class="form-section" v-if="dialogEditStatus && form.id">
              <div class="section-title">
                <span class="title-text">跟进记录</span>
              </div>
              <activity-time-line :customer-id="form.id" :disabled="isView" />
            </div>

            <!-- 销售机会 -->
            <div id="opportunity" class="form-section" v-if="dialogEditStatus && form.id">
              <div class="section-title">
                <span class="title-text">销售机会</span>
              </div>
              <opportunity-card :customer-id="form.id" :disabled="isView" />
            </div>

            <!-- 开票抬头 -->
            <div id="taxInvoiceTitle" class="form-section" v-if="dialogEditStatus && form.id">
              <div class="section-title">
                <span class="title-text">开票抬头</span>
              </div>
              <tax-invoice-title-card :customer-id="form.id" :disabled="isView" />
            </div>

            <!-- 收款计划 -->
            <div id="collection" class="form-section" v-if="dialogEditStatus">
              <div class="section-title">
                <span class="title-text">收款计划, 待开发</span>
              </div>
              开发中
            </div>

            <!-- 合同信息 -->
            <div id="contract" class="form-section" v-if="dialogEditStatus">
              <div class="section-title">
                <span class="title-text">合同信息, 待开发</span>
              </div>
            </div>

            <!-- 备注信息 -->
            <div id="note" class="form-section">
              <div class="section-title">
                <span class="title-text">备注信息</span>
              </div>
              <el-row :gutter="10">
                <el-col :span="12">
                  <el-form-item label="附件" prop="files">
                    <file-upload v-model="form.files" :disabled="isView" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="备注" prop="remark">
                    <el-input
                      v-model="form.remark"
                      type="textarea"
                      placeholder="请输入内容"
                      :rows="5"
                      :disabled="isView"
                      maxlength="500"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </el-col>
      </el-row>

      <template #footer>
        <div class="dialog-footer">
          <el-checkbox v-if="!dialogEditStatus" v-model="continueCreate" style="float: left; margin-top: 8px">继续新建</el-checkbox>
          <el-button :loading="buttonLoading" type="primary" @click="submitForm" :disabled="isView">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Customer" lang="ts">
import { listCustomer, getCustomer, delCustomer, addCustomer, updateCustomer, checkCustomerNameUnique } from '@/api/crm/customer';
import { CustomerVO, CustomerQuery, CustomerForm } from '@/api/crm/customer/types';
import DictSelect from '@/components/DictSelect/index.vue';
import { regionData, codeToText } from 'element-china-area-data';
import { useRoute, useRouter } from 'vue-router';
import ActivityTimeLine from './components/activityTimeLine.vue';
import OpportunityCard from './components/opportunityCard.vue';
import TaxInvoiceTitleCard from './components/taxInvoiceTitleCard.vue';
import MapSelect from '@/components/biz/MapSelect/index.vue';
import ContactCard from './components/contactCard.vue';
import { canOperated } from '@/utils/biz/canOperated';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const {
  dict_customer_level,
  dict_industry,
  dict_crm_resource,
  dict_demand_purpose,
  dict_customer_status,
  dict_customer_type,
  dict_customer_group,
  dict_customer_scale,
  dict_crm_pro,
  dict_crm_tag,
  sys_sort_type
} = toRefs<any>(
  proxy?.useDict(
    'dict_customer_level',
    'dict_industry',
    'dict_crm_resource',
    'dict_demand_purpose',
    'dict_customer_status',
    'dict_customer_type',
    'dict_customer_group',
    'dict_customer_scale',
    'dict_crm_pro',
    'dict_crm_tag',
    'sys_sort_type'
  )
);

// 状态机选择组件的支撑
import { useNextStatusList } from '@/views/org/stateMachine/detail/nextStatusList';
const { nextStatusList, nextStatusLoading, filteredStatus, gainNextStatusList } = useNextStatusList();

// 类型断言
const areaOptions = regionData as unknown as CascaderOption[];
const address = ref({
  completeAddress: '', // 完整地址
  lng: '', // 经度
  lat: '' // 纬度
});

// 修改 mapAddress 的初始化方式
const mapAddress = ref({
  completeAddress: '', // 完整地址
  lng: '', // 经度
  lat: '' // 纬度
});
const selectedOptions = ref<(string | number)[]>([]);

// 查询时的省市区县
const queryAreaOptions = ref<(string | number)[]>([]);

const customerList = ref<CustomerVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeFollowUpDate = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑
const continueCreate = ref(true); // 是否继续创建，对应复选框的值

const queryFormRef = ref<ElFormInstance>();
const customerFormRef = ref<ElFormInstance>();

import { useDeptSelect } from '@/hooks/useBusiness/deptSelect';
const { deptOptions, loadDeptTree, findDeptLabel } = useDeptSelect();

import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
const { findUserName, loadUserList, userOptions } = useSysUserSelect();

// 表单中用户（承做人和协做人等的）选择
import { useUserSelectByDeptId } from '@/views/system/user/detail/userSelectByDeptId';
const { userOptionsByDeptId, loadUserListByDeptId, userLoadingByDeptId } = useUserSelectByDeptId();

import { useHighSeaSelect } from '@/hooks/useBusiness/highSeaSelect';
const { loadHighSeaList, findHighSeaName } = useHighSeaSelect();

import { useProvinceSelect } from '@/api/setting/chinaProvince/provinceSelect';
const { provinceOptions, provinceLoading, loadProvinceList } = useProvinceSelect();

import { useCitySelect } from '@/api/setting/chinaCity/citySelect';
const { cityOptions, cityLoading, loadCityList } = useCitySelect();

import type { CascaderOption } from 'element-plus';

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const route = useRoute();
const router = useRouter();

const currentAnchor = ref('#basic');

const initFormData: CustomerForm = {
  id: undefined,
  name: undefined,
  aliasName: undefined,
  openCode: undefined,
  status: undefined,
  customerCode: undefined,
  owner: undefined,
  ownerDept: undefined,
  coOwner: undefined,
  level: undefined,
  type: undefined,
  source: undefined,
  industry: undefined,
  productKey: undefined,
  companyScale: undefined,
  demandPurpose: undefined,
  saleScene: undefined,
  customerDesc: undefined,
  followUpDate: undefined,
  highSeaId: undefined,
  lastYearDealAmount: undefined,
  tin: undefined,
  companyName: undefined,
  legalPerson: undefined,
  bizScope: undefined,
  bizLicense: undefined,
  address: undefined,
  provinceId: undefined,
  cityId: undefined,
  districtId: undefined,
  lng: undefined,
  lat: undefined,
  remark: undefined,
  files: undefined,
  isSignedContract: undefined
};
const data = reactive<PageData<CustomerForm, CustomerQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    status: undefined,
    openCode: undefined,
    customerCode: undefined,
    customerGroup: undefined,
    aliasName: undefined,
    ownerRegion: undefined,
    owner: undefined,
    ownerDept: undefined,
    level: undefined,
    type: undefined,
    source: undefined,
    industry: undefined,
    productKey: undefined,
    companyScale: undefined,
    demandPurpose: undefined,
    highSeaId: undefined,
    address: undefined,
    provinceId: undefined,
    cityId: undefined,
    districtId: undefined,
    updateBy: undefined,
    isSignedContract: undefined,
    params: {
      followUpDate: undefined,
      updateTime: undefined
    },
    orderByColumn: undefined,
    isAsc: 'desc'
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    name: [
      { required: true, message: '客户名称不能为空', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value) {
            queryUnique.name = form.value.name;
            queryUnique.id = form.value.id;
            checkCustomerNameUnique(queryUnique)
              .then((res) => {
                if (res.data) {
                  callback(new Error('客户名称已存在'));
                } else {
                  callback();
                }
              })
              .catch(() => {
                callback(new Error('验证客户名称失败'));
              });
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ],
    aliasName: [{ min: 2, max: 20, message: '客户别名长度在2-20个字符之间', trigger: 'blur' }],
    status: [{ required: true, message: '客户状态不能为空', trigger: 'change' }],
    level: [{ required: true, message: '客户级别不能为空', trigger: 'change' }],
    type: [{ required: true, message: '客户类型不能为空', trigger: 'change' }],
    source: [{ required: true, message: '客户来源不能为空', trigger: 'change' }],
    saleScene: [{ required: true, message: '销售场景不能为空', trigger: 'change' }],
    highSeaId: [
      {
        validator: (rule, value, callback) => {
          if (!value && !data.form.ownerDept) {
            callback(new Error('所属公海和承做部门必须填写一个'));
          } else {
            callback();
          }
        },
        trigger: 'change'
      }
    ],
    ownerDept: [
      {
        validator: (rule, value, callback) => {
          if (!value && !data.form.highSeaId) {
            callback(new Error('承做部门和所属公海必须填写一个'));
          } else {
            callback();
          }
        },
        trigger: 'change'
      }
    ],
    isSignedContract: [{ required: true, message: '是否已签合同不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

const handleMapAddressChange = (data) => {
  form.value.address = data.address;
  form.value.lng = data.location.lng;
  form.value.lat = data.location.lat;
};

// 表单中，处理省市县选择变化
const handleAreaChange = (data) => {
  form.value.provinceId = data[0];
  form.value.cityId = data[1];
  form.value.districtId = data[2];
};

// 查询时，处理省市县选择变化
const handleAreaQuery = (data) => {
  queryParams.value.provinceId = data[0];
  queryParams.value.cityId = data[1];
  queryParams.value.districtId = data[2];
  handleQuery();
};

// 查询时，处理归属部门选择变化
const handleDeptChangeQuery = (data) => {
  queryParams.value.ownerDept = data;
  handleQuery();
};

/** 查询客户档案列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeFollowUpDate.value, 'FollowUpDate');
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listCustomer(queryParams.value);
  customerList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  isView.value = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  customerFormRef.value?.resetFields();
  selectedOptions.value = [];
  // 重置 mapAddress 为初始状态而不是 undefined
  mapAddress.value = {
    completeAddress: '',
    lng: '',
    lat: ''
  };
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeFollowUpDate.value = ['', ''];
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  queryParams.value.ownerDept = undefined;
  resetQueryArea();
  handleQuery();
};

/** 重置查询时的省市区县 */
const resetQueryArea = () => {
  queryAreaOptions.value = [];
  queryParams.value.provinceId = undefined;
  queryParams.value.cityId = undefined;
  queryParams.value.districtId = undefined;
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: CustomerVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加客户档案';
  dialogEditStatus.value = false;
  currentAnchor.value = '#basic';
  nextTick(() => {
    document.getElementById('basic')?.scrollIntoView({ behavior: 'smooth' });
  });
};

/** 打开修改弹窗时，组装省市区信息 */
const assembleAreaInfo = () => {
  if (form.value.provinceId && form.value.cityId && form.value.districtId) {
    selectedOptions.value = [form.value.provinceId.toString(), form.value.cityId.toString(), form.value.districtId.toString()];
  }
};

/** 修改按钮操作 */
const handleUpdate = async (row?: CustomerVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getCustomer(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改客户档案' + '(' + form.value.openCode + ')';
  loadUserListByDeptId(form.value.ownerDept);
  dialogEditStatus.value = true;
  currentAnchor.value = '#basic';

  // 客户状态的状态机相关
  const currentStatus = form.value.status;
  gainNextStatusList('dict_customer_status', dict_customer_status.value, currentStatus);

  // 组装地图回显信息
  mapAddress.value = {
    completeAddress: form.value.address || '',
    lng: form.value.lng || '',
    lat: form.value.lat || ''
  };

  // 组装省市区信息
  assembleAreaInfo();

  // 滚动到基本信息
  nextTick(() => {
    document.getElementById('basic')?.scrollIntoView({ behavior: 'smooth' });
  });
};

/** 查看按钮操作 */
const isView = ref(false); // 是否是查看模式, 查看模式下，表单组件处于禁用状态
const handleView = async (row?: CustomerVO) => {
  reset();
  const res = await getCustomer(row?.id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '查看客户档案' + '(' + form.value.openCode + ')';
  isView.value = true;
  dialogEditStatus.value = true;
  currentAnchor.value = '#basic';
  nextTick(() => {
    document.getElementById('basic')?.scrollIntoView({ behavior: 'smooth' });
  });
};

/** 提交按钮 */
const submitForm = () => {
  customerFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateCustomer(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addCustomer(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');

      if (continueCreate.value && !dialogEditStatus.value) {
        const currentForm = { ...form.value };
        reset();
        form.value.saleScene = currentForm.saleScene;
        form.value.source = currentForm.source;
        form.value.industry = currentForm.industry;
        form.value.type = currentForm.type;
        form.value.owner = currentForm.owner;
        form.value.ownerDept = currentForm.ownerDept;
        form.value.highSeaId = currentForm.highSeaId;
        form.value.coOwner = currentForm.coOwner;
        dialogEditStatus.value = false;
        dialog.title = '新增客户';
        currentAnchor.value = '#basic';
        nextTick(() => {
          document.getElementById('basic')?.scrollIntoView({ behavior: 'smooth' });
        });
      } else {
        dialog.visible = false;
        dialogEditStatus.value = false;
      }
      await getList();
      isView.value = false;
    }
  });
  selectedOptions.value = [];
  // 重置 mapAddress 为初始状态而不是 undefined
  mapAddress.value = {
    completeAddress: '',
    lng: '',
    lat: ''
  };
  isView.value = false;
};

/** 删除按钮操作 */
const handleDelete = async (row?: CustomerVO) => {
  await proxy?.$modal.confirm('是否确认删除客户名称为"' + row?.name + '"的数据项？').finally(() => (loading.value = false));
  await delCustomer(row?.id);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'crm/customer/export',
    {
      ...queryParams.value
    },
    `customer_${new Date().getTime()}.xlsx`
  );
};

// 部门选项改变时的触发
const handleDeptChangeInForm = async () => {
  // 当前公海ID，以便取消的时候恢复
  const currentHighSeaId = form.value.highSeaId;

  // 如果当前有公海ID,则需要确认
  if (form.value.highSeaId) {
    try {
      await proxy?.$modal.confirm('指派承做人后，客户将从公海中移出，是否确认？');
      // 确认后清空公海ID、承做人、协做人
      form.value.highSeaId = undefined;
      form.value.owner = undefined;
      form.value.coOwner = undefined;
    } catch {
      // highSeaId恢复改变之前的值
      ElMessage.info('取消指承做人的操作');
      form.value.highSeaId = currentHighSeaId;
      form.value.ownerDept = undefined;
    }
  }

  form.value.owner = undefined;
  form.value.coOwner = undefined;
  loadUserListByDeptId(form.value.ownerDept);
};

// 根据跟进负责人选项改变时，同步改变跟进负责人所在部门
const handleOwnerChange = async () => {
  // 当前公海ID，以便取消的时候恢复
  const currentDeptId = form.value.ownerDept;
  const ownerDeptId = userOptions.value.find((item) => item.userId === form.value.owner)?.deptId;

  // 如果当前承办人的直接部门（ownerDeptId）与currentDeptId不一样，则要将告知操作者，系统要将承办部门设置为ownerDeptId
  if (currentDeptId !== ownerDeptId) {
    try {
      await proxy?.$modal.confirm('指派跟进人后，承做部门必须为该用户所在的直接部门,请确认？');
      form.value.ownerDept = ownerDeptId;
      loadUserListByDeptId(form.value.ownerDept);
    } catch {
      ElMessage.info('取消指派跟进人');
      form.value.ownerDept = currentDeptId;
    }
  }
};

// 打开链接
const openLink = (url: string) => {
  window.open(url, '_blank');
};

// 处理公海选择变化的方法
const handleHighSeaChange = async () => {
  if (form.value.ownerDept) {
    try {
      await proxy?.$modal.confirm('将入公海后，客户将取消之前指派的业务员，是否确认？');
      // 确认后清空公海ID
      form.value.owner = undefined;
      form.value.ownerDept = undefined;
      form.value.coOwner = undefined;
      form.value.followUpDate = undefined;
    } catch {
      form.value.highSeaId = undefined;
      ElMessage.info('取消纳入公海');
    }
  }
};

// 选择省份时，加载城市列表
const handleSelectProvinceInQuery = () => {
  loadCityList(queryParams.value.provinceId);
  queryParams.value.cityId = undefined;
  handleQuery();
};

// 检查唯一性查询体
const queryUnique: CustomerQuery = {
  pageNum: 1,
  pageSize: 10,
  id: undefined,
  name: undefined
};

// 监听要打开哪个弹窗
const handleOpenDialog = () => {
  // 1. 打开新增弹窗
  const openAdd = route.query.openAdd;
  if (openAdd === 'true') {
    nextTick(() => {
      handleAdd();
      router.replace({ query: {} });
    });
  }
  // 2. 打开修改弹窗
  const id = route.query.id;
  const openEdit = route.query.openEdit;
  if (id && openEdit === 'true') {
    nextTick(() => {
      handleUpdate({ id: id } as CustomerVO);
      router.replace({ query: {} });
    });
  }

  // 3. 打开查看弹窗
  const openView = route.query.openView;
  if (id && openView === 'true') {
    nextTick(() => {
      handleView({ id: id } as CustomerVO);
      router.replace({ query: {} });
    });
  }
};

// 添加行样式方法
const tableRowClassName = ({ row }) => {
  if (row.contactNum === 0) {
    return 'warning-row';
  }
  return '';
};

onMounted(() => {
  getList();
  loadUserList();
  loadDeptTree();
  loadHighSeaList();
  handleOpenDialog();
});
</script>

<style scoped lang="scss">
@import '@/assets/styles/anchorform.scss';

:deep(.warning-row) {
  background-color: #fef0f0 !important;
}
</style>
