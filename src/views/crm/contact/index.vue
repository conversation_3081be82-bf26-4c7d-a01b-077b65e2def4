<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px" class="search-form-container">
            <el-form-item label="客户名称" prop="customerId">
              <customer-select v-model="queryParams.customerId" placeholder="请选择客户档案" />
            </el-form-item>
            <el-form-item label="联系人姓名" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="联系人角色" prop="role" v-if="showMoreCondition">
              <dict-select
                v-model="queryParams.role"
                dict-key="dict_contact_role"
                placeholder="请选择联系人角色"
                :show-footer="false"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="queryParams.phone" placeholder="请输入联系电话" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="微信号" prop="wxId" v-if="showMoreCondition">
              <el-input v-model="queryParams.wxId" placeholder="请输入微信号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否首要" prop="isPrimary" v-if="showMoreCondition">
              <dict-select
                v-model="queryParams.isPrimary"
                dict-key="dict_is_primary"
                placeholder="请选择是否首要联系人"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="行为取向" prop="behavioral" v-if="showMoreCondition">
              <dict-select
                v-model="queryParams.behavioral"
                dict-key="dict_contact_behavioral"
                placeholder="请选择行为取向"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="性格特征" prop="personality" v-if="showMoreCondition">
              <dict-select
                v-model="queryParams.personality"
                dict-key="dict_contact_personality"
                placeholder="请选择性格特征"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="对我方立场" prop="positionOnMe" v-if="showMoreCondition">
              <dict-select
                v-model="queryParams.positionOnMe"
                dict-key="dict_position_on_me"
                placeholder="请选择对我方立场"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="交往程度" prop="communicate" v-if="showMoreCondition">
              <dict-select
                v-model="queryParams.communicate"
                dict-key="dict_contact_communicate"
                placeholder="请选择交往程度"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="个人爱好" prop="hobby" v-if="showMoreCondition">
              <dict-select
                v-model="queryParams.hobby"
                dict-key="dict_contact_hobby"
                placeholder="请选择个人爱好"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="更新人" prop="updateBy" v-if="showMoreCondition">
              <el-select v-model="queryParams.updateBy" placeholder="请输入更新人" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新时间" prop="updateTime" v-if="showMoreCondition">
              <el-date-picker
                clearable
                v-model="queryParams.updateTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择更新时间"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['crm:contact:add']">新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['crm:contact:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['crm:contact:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['crm:contact:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="contactList" @selection-change="handleSelectionChange" border>
        <!-- <el-table-column type="selection" width="55" /> -->
        <el-table-column label="#" type="index" align="center" fixed="left" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="客户名称" prop="customerId" width="200" show-overflow-tooltip>
          <template #default="scope">
            <el-tooltip content="查看客户档案" placement="top">
              <el-link type="primary" @click="openCustomerDetailInView(scope.row.customerId)">{{ scope.row.customer?.name }}</el-link>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="联系人姓名" prop="name" width="100" />
        <el-table-column label="联系人角色" prop="role" width="100">
          <template #default="scope">
            <dict-tag :options="dict_contact_role" :value="scope.row.role" />
          </template>
        </el-table-column>
        <el-table-column label="联系电话" prop="phone" width="150" />
        <el-table-column label="是否主要" prop="isPrimary" width="100">
          <template #default="scope">
            <dict-tag :options="dict_is_primary" :value="scope.row.isPrimary" />
          </template>
        </el-table-column>
        <el-table-column label="在职状态" prop="status" width="100">
          <template #default="scope">
            <dict-tag :options="dict_contact_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="行为取向" prop="behavioral" width="100">
          <template #default="scope">
            <dict-tag :options="dict_contact_behavioral" :value="scope.row.behavioral" />
          </template>
        </el-table-column>
        <el-table-column label="性格特征" prop="personality" width="100">
          <template #default="scope">
            <dict-tag :options="dict_contact_personality" :value="scope.row.personality" />
          </template>
        </el-table-column>
        <el-table-column label="对我方立场" prop="positionOnMe" width="100">
          <template #default="scope">
            <dict-tag :options="dict_position_on_me" :value="scope.row.positionOnMe" />
          </template>
        </el-table-column>
        <el-table-column label="交往程度" prop="communicate" width="100">
          <template #default="scope">
            <dict-tag :options="dict_contact_communicate" :value="scope.row.communicate" />
          </template>
        </el-table-column>
        <el-table-column label="个人爱好" prop="hobby" width="200">
          <template #default="scope">
            <dict-tag :options="dict_contact_hobby" :value="scope.row.hobby" />
          </template>
        </el-table-column>
        <!-- <el-table-column label="备注" prop="remark" min-width="200" /> -->
        <el-table-column label="更新人" prop="updateBy" width="150">
          <template #default="scope">
            <span>{{ findUserName(scope.row.updateBy) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="150">
          <template #default="scope">
            <el-tooltip content="查看" placement="top">
              <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['crm:contact:view']"></el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['crm:contact:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top" v-if="canOperated(scope.row.createTime)">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['crm:contact:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改联系人对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="60%" append-to-body @close="cancel">
      <el-form ref="contactFormRef" :model="form" :rules="rules" label-width="80px" label-position="top">
        <el-row :gutter="10">
          <el-col :span="6">
            <el-form-item label="客户名称" prop="customerId">
              <template #label>
                <span>
                  客户名称
                  <el-tooltip content="查看客户档案" placement="top" v-if="form.customerId">
                    <el-icon class="el-icon--right" @click="openCustomerDetailInView(form.customerId)"><View /></el-icon>
                  </el-tooltip>
                </span>
              </template>
              <customer-select v-model="form.customerId" placeholder="请选择客户名称" :disabled="dialogEditStatus" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="联系人姓名" prop="name">
              <el-input v-model="form.name" placeholder="请输入姓名" :disabled="isView" maxlength="10" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="联系人角色" prop="role">
              <dict-select v-model="form.role" dict-key="dict_contact_role" placeholder="请选择联系人角色" :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="联系人手机" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系手机" :disabled="isView" maxlength="11" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="联系人微信" prop="wxId">
              <el-input v-model="form.wxId" placeholder="请输入联系人微信" :disabled="isView" maxlength="20" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="联系人邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入联系人邮箱" :disabled="isView" maxlength="50" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="联系人性别" prop="gender">
              <el-radio-group v-model="form.gender" :disabled="isView">
                <el-radio v-for="dict in sys_user_sex" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="联系人职务" prop="position">
              <el-input v-model="form.position" placeholder="请输入联系人职务" :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否主要联系人" prop="isPrimary">
              <el-radio-group v-model="form.isPrimary" :disabled="isView">
                <el-radio v-for="dict in dict_is_primary" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="在职状态" prop="status">
              <dict-select v-model="form.status" dict-key="dict_contact_status" placeholder="请选择在职状态" :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="行为取向" prop="behavioral">
              <dict-select v-model="form.behavioral" dict-key="dict_contact_behavioral" placeholder="请选择行为取向" :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="性格特征" prop="personality">
              <dict-select v-model="form.personality" dict-key="dict_contact_personality" placeholder="请选择性格特征" :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="对我方立场" prop="positionOnMe">
              <dict-select v-model="form.positionOnMe" dict-key="dict_position_on_me" placeholder="请选择对我方立场" :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="交往程度" prop="communicate">
              <dict-select v-model="form.communicate" dict-key="dict_contact_communicate" placeholder="请选择交往程度" :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="个人爱好" prop="hobby">
              <dict-select v-model="form.hobby" dict-key="dict_contact_hobby" placeholder="请选择个人爱好" multiple :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <template #label>
                <span>
                  备注
                  <el-tooltip content="可记录联系人的籍贯、常在城市、过往职业等有趣的业务信息" placement="top">
                    <el-icon class="el-icon--right">
                      <InfoFilled />
                    </el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" :rows="3" :disabled="isView" maxlength="200" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人附件" prop="file">
              <file-upload v-model="form.file" :disabled="isView" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm" :disabled="isView">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Contact" lang="ts">
import { listContact, getContact, delContact, addContact, updateContact } from '@/api/crm/contact';
import { ContactVO, ContactQuery, ContactForm } from '@/api/crm/contact/types';
import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
import { useRoute, useRouter } from 'vue-router';
import { canOperated } from '@/utils/biz/canOperated';
import { View } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const {
  dict_is_primary,
  dict_contact_hobby,
  dict_contact_communicate,
  dict_contact_role,
  sys_user_sex,
  dict_contact_personality,
  dict_contact_behavioral,
  dict_position_on_me,
  dict_contact_status
} = toRefs<any>(
  proxy?.useDict(
    'dict_is_primary',
    'dict_contact_hobby',
    'dict_contact_communicate',
    'dict_contact_role',
    'sys_user_sex',
    'dict_contact_personality',
    'dict_contact_behavioral',
    'dict_position_on_me',
    'dict_contact_status'
  )
);

const contactList = ref<ContactVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const dialogEditStatus = ref(false); // 是否编辑状态,true为编辑，false为新增

const queryFormRef = ref<ElFormInstance>();
const contactFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ContactForm = {
  id: undefined,
  customerId: undefined,
  name: undefined,
  role: undefined,
  phone: undefined,
  wxId: undefined,
  email: undefined,
  status: '1',
  gender: '0',
  position: undefined,
  isPrimary: '0',
  behavioral: undefined,
  personality: undefined,
  positionOnMe: undefined,
  communicate: undefined,
  hobby: undefined,
  file: undefined,
  remark: undefined
};
const data = reactive<PageData<ContactForm, ContactQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    customerId: undefined,
    name: undefined,
    role: undefined,
    phone: undefined,
    wxId: undefined,
    email: undefined,
    status: undefined,
    isPrimary: undefined,
    behavioral: undefined,
    personality: undefined,
    positionOnMe: undefined,
    communicate: undefined,
    hobby: undefined,
    updateBy: undefined,
    updateTime: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    customerId: [{ required: true, message: '客户名称不能为空', trigger: 'blur' }],
    name: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
    role: [{ required: true, message: '联系人角色不能为空', trigger: 'change' }],
    gender: [{ required: true, message: '性别不能为空', trigger: 'change' }],
    email: [{ type: 'email', message: '邮箱格式不正确', trigger: 'blur' }],
    isPrimary: [{ required: true, message: '是否主要联系人不能为空', trigger: 'change' }],
    phone: [
      { required: false },
      {
        validator: (rule: any, value: string, callback: (error?: Error) => void) => {
          if (!value) {
            callback();
            return;
          }
          if (!/^1[3-9]\d{9}$/.test(value)) {
            callback(new Error('请输入正确的11位手机号码'));
            return;
          }
          callback();
        },
        trigger: 'blur'
      }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

const { findUserName, loadUserList, userOptions } = useSysUserSelect();

// 添加展开/收起状态变量
const showMoreCondition = ref(false);

// 获取路由实例
const route = useRoute();
const router = useRouter();

/** 查询联系人列表 */
const getList = async () => {
  loading.value = true;
  const res = await listContact(queryParams.value);
  contactList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  isView.value = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  contactFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ContactVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加联系人';
  dialogEditStatus.value = false;
  // 如果URL中有customerId参数，在这里可以预填表单
  const customerId = route.query.customerId;
  if (customerId) {
    form.value.customerId = customerId as string;
  }
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ContactVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getContact(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改联系人';
  dialogEditStatus.value = true;
};

/** 查看按钮操作 */
const isView = ref(false); // 是否是查看模式, 查看模式下，表单组件处于禁用状态
const handleView = async (row?: ContactVO) => {
  reset();
  const res = await getContact(row?.id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '查看联系人';
  isView.value = true;
  dialogEditStatus.value = true;
};

/** 提交按钮 */
const submitForm = () => {
  contactFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateContact(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addContact(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ContactVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除联系人编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delContact(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'crm/contact/export',
    {
      ...queryParams.value
    },
    `contact_${new Date().getTime()}.xlsx`
  );
};

// 打开客户详情(编辑页面)
const openCustomerDetailInEdit = (id: string | number) => {
  const routeUrl = `/crm/bizMg/customer/?id=${id}&openEdit=true`;
  window.open(routeUrl, '_blank');
};

// 打开客户详情(查看页面)
const openCustomerDetailInView = (id: string | number) => {
  const routeUrl = `/crm/bizMg/customer/?id=${id}&openView=true`;
  window.open(routeUrl, '_blank');
};

// 外部跳转逻辑维护
const handleOpenDialog = () => {
  // 1. 打开联系人新增
  const customerId = route.query.customerId;
  const openAdd = route.query.openAdd;
  if (customerId && openAdd === 'true') {
    nextTick(() => {
      // 打开新增弹窗，并预填客户ID
      handleAdd();
      // 设置表单中的客户ID
      form.value.customerId = customerId as string;
      // 清除URL参数
      router.replace({ query: {} });
    });
  }

  // 2. 打开联系人详情(编辑页面)
  const id = route.query.id;
  const openEdit = route.query.openEdit;
  if (id && openEdit === 'true') {
    nextTick(() => {
      handleUpdate({ id: id } as ContactVO);
      router.replace({ query: {} });
    });
  }

  // 3. 打开联系人详情(查看页面)
  const openView = route.query.openView;
  if (id && openView === 'true') {
    nextTick(() => {
      handleView({ id: id } as ContactVO);
      router.replace({ query: {} });
    });
  }
};

onMounted(() => {
  getList();
  handleOpenDialog();

  loadUserList();
});
</script>

<style scoped>
@import '@/assets/styles/anchorform.scss';
</style>
