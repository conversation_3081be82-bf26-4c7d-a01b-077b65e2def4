import { ref } from 'vue';
import { listContact } from '@/api/crm/contact';
import type { ContactVO } from '@/api/crm/contact/types';

export const useContactSelect = () => {
  const contactOptions = ref<ContactVO[]>([]);
  const contactLoading = ref(false);

  const loadContactList = async (customerId: string | number) => {
    try {
      contactLoading.value = true;
      const res = await listContact({
        pageNum: 1,
        pageSize: 30,
        customerId: customerId
      });
      contactOptions.value = res.rows;
      // console.log('contactOptions', contactOptions.value);
    } finally {
      contactLoading.value = false;
    }
  };

  return {
    contactOptions,
    contactLoading,
    loadContactList
  };
};
