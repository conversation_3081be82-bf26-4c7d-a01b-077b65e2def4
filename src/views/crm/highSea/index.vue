<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="公海名称" prop="seaName">
              <el-input v-model="queryParams.seaName" placeholder="请输入公海名称" clearable @keyup.enter="handleQuery" style="width: '100%'" />
            </el-form-item>
            <el-form-item label="公海状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择公海状态" clearable style="width: '100%'" @change="handleQuery">
                <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新人" prop="updateBy">
              <el-select v-model="queryParams.updateBy" placeholder="请输入更新人" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['crm:highSea:add']">新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['crm:highSea:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['crm:highSea:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['crm:highSea:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="highSeaList" @selection-change="handleSelectionChange" border>
        <el-table-column type="index" width="40" align="center" label="#" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="公海名称" prop="seaName" min-width="250px" />
        <el-table-column label="公海状态" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="所属部门" prop="deptId">
          <template #default="scope">
            <span>{{ findDeptLabel(scope.row.deptId) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="150" max-width="250" show-overflow-tooltip />
        <el-table-column label="更新人" prop="updateBy" width="150">
          <template #default="scope">
            <span>{{ findUserName(scope.row.updateBy) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['crm:highSea:edit']"></el-button>
            </el-tooltip>
            <!-- 限制删除的注释  v-if="canOperated(scope.row.updateTime)" -->
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['crm:highSea:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改公海对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="highSeaFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公海名称" prop="seaName">
          <el-input v-model="form.seaName" placeholder="请输入公海名称" maxlength="20" show-word-limit />
        </el-form-item>
        <el-form-item label="所属部门" prop="deptId">
          <el-tree-select
            v-model="form.deptId"
            :data="deptOptions"
            :props="{ value: 'id', label: 'label', children: 'children' }"
            value-key="id"
            placeholder="请选择归属部门"
            check-strictly
          />
        </el-form-item>
        <el-form-item label="公海状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" maxlength="500" show-word-limit :rows="5" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="HighSea" lang="ts">
import { listHighSea, getHighSea, delHighSea, addHighSea, updateHighSea } from '@/api/crm/highSea';
import { HighSeaVO, HighSeaQuery, HighSeaForm } from '@/api/crm/highSea/types';
import { fa } from 'element-plus/es/locale/index.mjs';
import { useDeptSelect } from '@/hooks/useBusiness/deptSelect';
import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
import { canOperated } from '@/utils/biz/canOperated';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_normal_disable } = toRefs<any>(proxy?.useDict('sys_normal_disable'));

const highSeaList = ref<HighSeaVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const highSeaFormRef = ref<ElFormInstance>();

const { deptOptions, loadDeptTree, findDeptLabel } = useDeptSelect();
const { findUserName, loadUserList, userOptions } = useSysUserSelect();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: HighSeaForm = {
  id: undefined,
  seaName: undefined,
  status: '0',
  deptId: undefined,
  remark: undefined
};
const data = reactive<PageData<HighSeaForm, HighSeaQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    seaName: undefined,
    status: undefined,
    deptId: undefined,
    updateBy: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    seaName: [{ required: true, message: '公海名称不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '公海状态不能为空', trigger: 'change' }],
    deptId: [{ required: true, message: '所属部门不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公海列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listHighSea(queryParams.value);
  highSeaList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  highSeaFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: HighSeaVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加公海';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: HighSeaVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getHighSea(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改公海';
};

/** 提交按钮 */
const submitForm = () => {
  highSeaFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateHighSea(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addHighSea(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: HighSeaVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除公海编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delHighSea(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'biz/crm/highSea/export',
    {
      ...queryParams.value
    },
    `highSea_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
  loadDeptTree();
  loadUserList();
});
</script>
