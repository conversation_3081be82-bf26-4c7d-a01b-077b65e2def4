<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px" class="search-form-container">
            <el-form-item label="客户名称" prop="customer">
              <el-input v-model="queryParams.customer" placeholder="请输入客户名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="行业" prop="industryKey">
              <dict-select
                v-model="queryParams.industryKey"
                dict-key="dict_industry"
                placeholder="请选择行业"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="联系人姓名" prop="contactName">
              <el-input v-model="queryParams.contactName" placeholder="请输入联系人姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="联系人手机" prop="contactPhone">
              <el-input v-model="queryParams.contactPhone" placeholder="请输入联系人手机" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="联系人角色" prop="contactRole" v-if="showMoreCondition">
              <dict-select
                v-model="queryParams.contactRole"
                dict-key="dict_contact_role"
                placeholder="请选择联系人角色"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>

            <el-form-item label="意向产品" prop="productKey">
              <dict-select
                v-model="queryParams.productKey"
                dict-key="dict_crm_pro"
                placeholder="请选择意向产品"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>

            <el-form-item label="采购用途" prop="demandPurpose">
              <dict-select
                v-model="queryParams.demandPurpose"
                dict-key="dict_demand_purpose"
                placeholder="请选择采购用途"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>

            <el-form-item label="线索来源" prop="sourceKey" v-if="showMoreCondition">
              <dict-select
                v-model="queryParams.sourceKey"
                dict-key="dict_crm_resource"
                placeholder="请选择线索来源"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>

            <el-form-item label="线索状态" prop="status">
              <template #label>
                <span>
                  线索状态
                  <el-tooltip content="了解线索逻辑" placement="top">
                    <el-icon @click="openLink('https://rcn3q3ujmqdd.feishu.cn/docx/LFOrdrAEjoGNbNx6gcZcopu4nPe?from=from_copylink')"
                      ><QuestionFilled
                    /></el-icon>
                  </el-tooltip>
                </span>
              </template>
              <dict-select
                v-model="queryParams.status"
                dict-key="dict_clue_status"
                placeholder="请选择线索状态"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>

            <el-form-item label="市场活动" prop="marktingId" v-if="showMoreCondition">
              <marketing-select v-model="queryParams.marktingId" placeholder="请选择市场活动" @change="handleQuery" />
            </el-form-item>

            <el-form-item label="业务部门" prop="ownerDept" v-if="showMoreCondition">
              <el-tree-select
                v-model="queryParams.ownerDept"
                :data="deptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                placeholder="请选择承做部门"
                check-strictly
                filterable
                clearable
                @change="handleDeptChangeQuery"
              />
            </el-form-item>

            <el-form-item label="业务员" prop="owner" v-if="showMoreCondition">
              <el-select v-model="queryParams.owner" placeholder="请选择业务员" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>

            <el-form-item label="分配时间" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeAssignTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>

            <el-form-item label="关闭原因" prop="closeReason" v-if="showMoreCondition">
              <el-select v-model="queryParams.closeReason" placeholder="请选择关闭原因" clearable @change="handleQuery" filterable>
                <el-option v-for="item in dict_clue_close" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>

            <el-form-item label="省份" prop="provinceId" v-if="showMoreCondition">
              <el-select
                v-model="queryParams.provinceId"
                placeholder="请选择省份"
                clearable
                @change="handleSelectProvinceInQuery"
                @visible-change="loadProvinceList"
                filterable
              >
                <el-option v-for="item in provinceOptions" :loading="provinceLoading" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>

            <el-form-item label="城市" prop="cityId" v-if="showMoreCondition">
              <el-select
                v-model="queryParams.cityId"
                placeholder="请选择城市"
                clearable
                @change="handleQuery"
                filterable
                :loading="cityLoading"
                :disabled="!queryParams.provinceId"
              >
                <el-option v-for="item in cityOptions" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>

            <el-form-item label="客户档案" prop="customerId" v-if="showMoreCondition">
              <customer-select v-model="queryParams.customerId" placeholder="请选择客户档案" :show-footer="false" @change="handleQuery" />
            </el-form-item>

            <el-form-item label="销售机会" prop="opportunityId" v-if="showMoreCondition">
              <opportunity-select v-model="queryParams.opportunityId" placeholder="请选择销售机会" :show-footer="false" @change="handleQuery" />
            </el-form-item>

            <el-form-item label="更新人" prop="updateBy" v-if="showMoreCondition">
              <el-select v-model="queryParams.updateBy" placeholder="请输入更新人" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>

            <el-form-item label="更新时间" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['crm:clue:add']">新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['crm:clue:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['crm:clue:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['crm:clue:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="clueList" @selection-change="handleSelectionChange" border>
        <!-- <el-table-column type="selection" width="55" /> -->
        <el-table-column label="#" type="index" align="center" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="客户名称" prop="customer" width="200" show-overflow-tooltip>
          <template #header>
            <span>
              客户名称
              <el-tooltip content="线索中的客户名称与客户档案中的最终的客户名称可能会一致，因为线索具有一定的模糊性。" placement="top">
                <el-icon class="el-icon--right">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </span>
          </template>
          <template #default="scope">
            <el-tooltip content="查看客户档案" placement="top" v-if="scope.row.customerId">
              <el-link type="primary" @click="openCustomerDetailInView(scope.row.customerId)">{{ scope.row.customer }}</el-link>
            </el-tooltip>
            <span v-else>{{ scope.row.customer }}</span>
          </template>
        </el-table-column>
        <el-table-column label="行业" prop="industryKey" width="100">
          <template #default="scope">
            <dict-tag :options="dict_industry" :value="scope.row.industryKey" />
          </template>
        </el-table-column>
        <el-table-column label="联系人姓名" prop="contactName" width="100" show-overflow-tooltip />
        <el-table-column label="联系人手机" prop="contactPhone" width="150" />
        <el-table-column label="联系人角色" prop="contactRole" width="100">
          <template #default="scope">
            <dict-tag :options="dict_contact_role" :value="scope.row.contactRole" />
          </template>
        </el-table-column>
        <el-table-column label="意向产品" prop="productKey" width="100">
          <template #default="scope">
            <dict-tag :options="dict_crm_pro" :value="scope.row.productKey" />
          </template>
        </el-table-column>
        <el-table-column label="采购用途" prop="demandPurpose" width="100">
          <template #default="scope">
            <dict-tag :options="dict_demand_purpose" :value="scope.row.demandPurpose" />
          </template>
        </el-table-column>
        <el-table-column label="需求描述" prop="demandDesc" min-width="250" :show-overflow-tooltip="true">
          <template #default="scope">
            <el-tooltip v-if="scope.row.demandUrl" content="需求详情" placement="top">
              <el-icon @click="openLink(scope.row.demandUrl)"><Link /></el-icon>
            </el-tooltip>
            <span style="padding-left: 10px">{{ scope.row.demandDesc }}</span>
          </template>
        </el-table-column>
        <el-table-column label="线索来源" prop="sourceKey" width="100">
          <template #default="scope">
            <dict-tag :options="dict_crm_resource" :value="scope.row.sourceKey" />
          </template>
        </el-table-column>
        <el-table-column label="线索状态" prop="status" width="100">
          <template #header>
            <span>
              线索状态
              <el-tooltip content="了解线索逻辑" placement="top">
                <el-icon @click="openLink('https://rcn3q3ujmqdd.feishu.cn/docx/LFOrdrAEjoGNbNx6gcZcopu4nPe?from=from_copylink')"
                  ><QuestionFilled
                /></el-icon>
              </el-tooltip>
            </span>
          </template>
          <template #default="scope">
            <dict-tag :options="dict_clue_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="业务员" prop="ownerNickName" width="100" />
        <el-table-column label="业务部门" prop="ownerDeptName" width="100" />
        <el-table-column label="分配时间" prop="assignTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.assignTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="关闭原因" prop="closeReason" width="120">
          <template #default="scope">
            <dict-tag :options="dict_clue_close" :value="scope.row.closeReason" />
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="200" show-overflow-tooltip />
        <el-table-column label="省份" prop="provinceName" width="120" />
        <el-table-column label="城市" prop="cityName" width="150" />
        <!-- <el-table-column label="销售机会" prop="opportunityId" width="100" /> -->
        <el-table-column label="更新人" prop="updateBy" width="150">
          <template #default="scope">
            <span>{{ findUserName(scope.row.updateBy) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="150">
          <template #default="scope">
            <el-tooltip content="查看" placement="top">
              <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['crm:clue:view']"></el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['crm:clue:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top" v-if="canOperated(scope.row.createTime)">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['crm:clue:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改客户线索对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="80%" append-to-body @close="cancel">
      <el-row :gutter="10">
        <!-- 左侧锚点导航 -->
        <el-col :span="4">
          <el-anchor>
            <el-anchor-link href="#basic" title="基础信息" />
            <el-anchor-link href="#lose" title="转化失败" v-if="form.status === '10'" />
            <el-anchor-link href="#more" title="转化成功" v-if="form.status === '20' || form.status === '21'" />
            <el-anchor-link href="#activity" title="执行跟进" />
          </el-anchor>
        </el-col>
        <el-col :span="20">
          <el-form ref="clueFormRef" :model="form" :rules="rules" label-width="80px" label-position="top">
            <!-- 基础信息 -->
            <div id="basic" class="form-section">
              <div class="section-title">
                <span class="title-text">基础信息</span>
              </div>
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-form-item label="客户名称" prop="customer">
                    <template #label>
                      <span>客户名称</span>
                      <el-tooltip content="线索中的客户名称与客户档案中的最终的客户名称可能会一致，因为线索具有一定的模糊性。" placement="top">
                        <el-icon class="el-icon--right">
                          <QuestionFilled />
                        </el-icon>
                      </el-tooltip>
                    </template>
                    <el-input v-model="form.customer" placeholder="请输入客户名称" :disabled="isView" minlength="2" maxlength="50" show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="行业" prop="industryKey">
                    <dict-select v-model="form.industryKey" dict-key="dict_industry" placeholder="请选择行业" :disabled="isView" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="联系人姓名" prop="contactName">
                    <el-input
                      v-model="form.contactName"
                      placeholder="请输入联系人姓名"
                      :disabled="isView"
                      minlength="2"
                      maxlength="20"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="联系人手机" prop="contactPhone">
                    <el-input v-model="form.contactPhone" placeholder="请输入联系人手机" :disabled="isView" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="客户所在城市">
                    <el-cascader
                      v-model="areaFormOption"
                      filterable
                      style="width: 100%"
                      :options="areaOptions"
                      @change="handleAreaChangeInForm"
                      :disabled="isView"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="客户地址" prop="address">
                    <el-input v-model="form.address" placeholder="请输入地址" :disabled="isView" maxlength="100" show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="联系人角色" prop="contactRole">
                    <dict-select v-model="form.contactRole" dict-key="dict_contact_role" placeholder="请选择联系人角色" :disabled="isView" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="意向产品" prop="productKey">
                    <dict-select v-model="form.productKey" dict-key="dict_crm_pro" placeholder="请选择意向产品" multiple :disabled="isView" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="采购用途" prop="demandPurpose">
                    <dict-select
                      v-model="form.demandPurpose"
                      dict-key="dict_demand_purpose"
                      placeholder="请选择采购用途"
                      multiple
                      :disabled="isView"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="线索来源" prop="sourceKey">
                    <dict-select v-model="form.sourceKey" dict-key="dict_crm_resource" placeholder="请选择线索来源" :disabled="isView" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="市场活动" prop="marktingId">
                    <template #label>
                      <span>
                        市场活动
                        <el-tooltip content="查看市场活动" placement="top" v-if="form.marktingId">
                          <el-icon @click="openMarketDetail(form.marktingId)"><Link /></el-icon>
                        </el-tooltip>
                      </span>
                    </template>
                    <marketing-select v-model="form.marktingId" placeholder="请选择线索来源有关的市场活动" :disabled="isView" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="需求描述文档链接" prop="demandUrl">
                    <template #label>
                      <span>
                        需求描述文档链接
                        <el-tooltip content="请在飞书中维护需求描述文档，然后将链接粘贴到此处" placement="top">
                          <el-icon class="el-icon--right">
                            <InfoFilled />
                          </el-icon>
                        </el-tooltip>
                        <el-tooltip v-if="form.demandUrl" content="单击打开链接" placement="top">
                          <el-icon @click="openLink(form.demandUrl)"><Link /></el-icon>
                        </el-tooltip>
                      </span>
                    </template>
                    <el-input v-model="form.demandUrl" placeholder="请输入需求描述文档链接" :disabled="isView" maxlength="200" show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="需求描述" prop="demandDesc">
                    <el-input
                      v-model="form.demandDesc"
                      type="textarea"
                      placeholder="请输入内容"
                      :rows="5"
                      :maxlength="500"
                      show-word-limit
                      :disabled="isView"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 转化失败 -->
            <div id="lose" class="form-section" style="border: 1px solid #f56c6c" v-if="form.status === '10'">
              <div class="section-title">
                <span class="title-text">转化失败</span>
              </div>
              <el-row :gutter="10">
                <el-col :span="8">
                  <el-form-item label="关闭原因" prop="closeReason">
                    <dict-select v-model="form.closeReason" dict-key="dict_clue_close" placeholder="请选择关闭原因" :disabled="isView" />
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="关闭原因描述" prop="closeDesc">
                    <el-input
                      v-model="form.closeDesc"
                      type="textarea"
                      placeholder="请输入关闭原因描述"
                      :rows="5"
                      :maxlength="200"
                      show-word-limit
                      :disabled="isView"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 转化成功 -->
            <div id="more" class="form-section" style="border: 1px solid #67c23a" v-if="form.status === '20' || form.status === '21'">
              <div class="section-title">
                <span class="title-text">转化成功</span>
              </div>
              <el-row :gutter="10">
                <el-col :span="8">
                  <el-row :gutter="10">
                    <el-col :span="24">
                      <el-form-item label="客户名称" prop="customerId">
                        <template #label>
                          <span>
                            客户名称
                            <el-tooltip v-if="form.customerId" content="单击打开客户档案" placement="top">
                              <el-icon @click="openCustomerDetailInView(form.customerId)"><View /></el-icon>
                            </el-tooltip>
                          </span>
                        </template>
                        <customer-select
                          v-model="form.customerId"
                          placeholder="请选择客户档案"
                          @change="handleCustomerChangeInForm"
                          :disabled="isView"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="form.status === '21'">
                      <el-form-item label="销售机会" prop="opportunityId">
                        <template #label>
                          <span>
                            销售机会
                            <el-tooltip v-if="form.opportunityId" content="单击打开销售机会" placement="top">
                              <el-icon @click="openOpportunityDetailInView(form.opportunityId)"><View /></el-icon>
                            </el-tooltip>
                          </span>
                        </template>
                        <el-select v-model="form.opportunityId" placeholder="请选择销售机会" :disabled="!form.customerId || isView">
                          <el-option v-for="item in opportunityOptions" :key="item.id" :label="item.name" :value="item.id" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="备注" prop="remark">
                    <el-input
                      v-model="form.remark"
                      type="textarea"
                      placeholder="请输入内容"
                      :rows="5"
                      :maxlength="500"
                      show-word-limit
                      :disabled="isView"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 执行跟进 -->
            <div id="activity" class="form-section">
              <div class="section-title">
                <span class="title-text">执行跟进</span>
              </div>
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-form-item label="线索状态" prop="status">
                    <template #label>
                      <span>
                        线索状态
                        <el-tooltip content="了解线索逻辑" placement="top">
                          <el-icon @click="openLink('https://rcn3q3ujmqdd.feishu.cn/docx/LFOrdrAEjoGNbNx6gcZcopu4nPe?from=from_copylink')"
                            ><QuestionFilled
                          /></el-icon>
                        </el-tooltip>
                      </span>
                    </template>
                    <!-- 新增模式 -->
                    <el-select
                      v-model="form.status"
                      placeholder="请选择线索状态"
                      :disabled="isView"
                      @change="handleStatusChangeInForm"
                      v-if="!dialogEditStatus"
                    >
                      <el-option v-for="item in dict_clue_status" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                    <!-- 编辑模式 -->
                    <el-select v-model="form.status" placeholder="请选择线索状态" :disabled="isView" @change="handleStatusChangeInForm" v-else>
                      <el-option v-for="item in filteredStatus" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="业务部门" prop="ownerDept">
                    <el-tree-select
                      v-model="form.ownerDept"
                      :data="deptOptions"
                      :props="{ value: 'id', label: 'label', children: 'children' }"
                      value-key="id"
                      placeholder="请选择归属部门"
                      check-strictly
                      :disabled="form.status != '02' || isView"
                      @change="handleDeptChangeInForm"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="业务员" prop="owner">
                    <el-select
                      v-model="form.owner"
                      placeholder="请选择业务员"
                      clearable
                      filterable
                      :disabled="form.status != '02' || isView"
                      @change="handleOwnerChangeInForm"
                    >
                      <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="分配时间" prop="assignTime">
                    <el-date-picker
                      clearable
                      v-model="form.assignTime"
                      type="datetime"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      placeholder="请选择分配时间"
                      style="width: 100%"
                      :disabled="form.status != '02' || isView"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </el-col>
      </el-row>

      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm" v-if="!isView">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Clue" lang="ts">
import { listClue, getClue, delClue, addClue, updateClue } from '@/api/crm/clue';
import { ClueVO, ClueQuery, ClueForm } from '@/api/crm/clue/types';
import { canOperated } from '@/utils/biz/canOperated';

// 部门选择组件的支撑
import { useDeptSelect } from '@/hooks/useBusiness/deptSelect';
const { deptOptions, loadDeptTree, findDeptLabel } = useDeptSelect();

// 省份选择组件的支撑
import { useProvinceSelect } from '@/api/setting/chinaProvince/provinceSelect';
const { provinceOptions, provinceLoading, loadProvinceList } = useProvinceSelect();

// 城市选择组件的支撑
import { useCitySelect } from '@/api/setting/chinaCity/citySelect';
const { cityOptions, cityLoading, loadCityList } = useCitySelect();

// 状态机选择组件的支撑
import { useNextStatusList } from '@/views/org/stateMachine/detail/nextStatusList';
const { filteredStatus, gainNextStatusList } = useNextStatusList();

// 字典选择组件的支撑
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { dict_industry, dict_demand_purpose, dict_crm_resource, dict_contact_role, dict_clue_status, dict_crm_pro, dict_clue_close } = toRefs<any>(
  proxy?.useDict(
    'dict_industry',
    'dict_demand_purpose',
    'dict_crm_resource',
    'dict_contact_role',
    'dict_clue_status',
    'dict_crm_pro',
    'dict_clue_close'
  )
);

// 销售机会选择组件的支撑
import { useOpportunitySelect } from '@/views/crm/opportunity/detail/opportunitySelect';
const { opportunityOptions, opportunityLoading, loadOpportunityList } = useOpportunitySelect();

// 客户档案选择组件发生变化时触发获取销售机会列表
const handleCustomerChangeInForm = (customerId: string | number) => {
  loadOpportunityList(customerId);
};

const clueList = ref<ClueVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<(string | number)[]>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeAssignTime = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const clueFormRef = ref<ElFormInstance>();

const isView = ref(false); // 是否是查看模式, 查看模式下，表单组件处于禁用状态
const dialogEditStatus = ref(false); // true: 编辑模式, false: 新增模式

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ClueForm = {
  id: undefined,
  customer: undefined,
  industryKey: undefined,
  contactName: undefined,
  contactPhone: undefined,
  contactRole: undefined,
  productKey: undefined,
  demandPurpose: undefined,
  demandDesc: undefined,
  demandUrl: undefined,
  sourceKey: undefined,
  status: '01',
  marktingId: undefined,
  owner: undefined,
  ownerDept: undefined,
  assignTime: undefined,
  closeReason: undefined,
  closeDesc: undefined,
  remark: undefined,
  address: undefined,
  provinceId: undefined,
  cityId: undefined,
  districtId: undefined,
  lng: undefined,
  lat: undefined,
  customerId: undefined,
  opportunityId: undefined
};
const data = reactive<PageData<ClueForm, ClueQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    customer: undefined,
    industryKey: undefined,
    contactName: undefined,
    contactPhone: undefined,
    contactRole: undefined,
    productKey: undefined,
    demandPurpose: undefined,
    demandDesc: undefined,
    sourceKey: undefined,
    status: undefined,
    marktingId: undefined,
    owner: undefined,
    ownerDept: undefined,
    closeReason: undefined,
    provinceId: undefined,
    cityId: undefined,
    districtId: undefined,
    customerId: undefined,
    opportunityId: undefined,
    updateBy: undefined,
    params: {
      assignTime: undefined,
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    customer: [{ required: true, message: '客户名称不能为空', trigger: 'blur' }],
    industryKey: [{ required: true, message: '行业不能为空', trigger: 'change' }],
    contactName: [{ required: true, message: '联系人姓名不能为空', trigger: 'blur' }],
    contactRole: [{ required: true, message: '联系人角色不能为空', trigger: 'change' }],
    productKey: [{ required: true, message: '意向产品不能为空', trigger: 'change' }],
    demandPurpose: [{ required: true, message: '需求目用途不能为空', trigger: 'change' }],
    demandDesc: [{ required: true, message: '需求描述不能为空', trigger: 'blur' }],
    sourceKey: [{ required: true, message: '线索来源不能为空', trigger: 'change' }],
    status: [{ required: true, message: '线索状态不能为空', trigger: 'change' }],
    contactPhone: [
      { required: false },
      {
        validator: (rule: any, value: string, callback: (error?: Error) => void) => {
          if (!value) {
            callback();
            return;
          }
          if (!/^1[3-9]\d{9}$/.test(value)) {
            callback(new Error('请输入正确的11位手机号码'));
            return;
          }
          callback();
        },
        trigger: 'blur'
      }
    ],
    demandUrl: [
      {
        validator: (rule: any, value: string, callback: (error?: Error) => void) => {
          if (!value) {
            callback();
            return;
          }
          if (!value.startsWith('https://')) {
            callback(new Error('销售策略，填写在线文档链接必须以 https:// 开头'));
            return;
          }
          callback();
        },
        trigger: 'blur'
      }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
const { findUserName, loadUserList, userOptions } = useSysUserSelect();

// 类型断言
import { regionData, codeToText } from 'element-china-area-data';
import type { CascaderOption } from 'element-plus';
const areaOptions = regionData as unknown as CascaderOption[];
const areaFormOption = ref<(string | number)[]>([]);

// 处理表单中区域选择变化
const handleAreaChangeInForm = (data: (string | number)[]) => {
  form.value.provinceId = data[0];
  form.value.cityId = data[1];
  form.value.districtId = data[2];
};

// 表单中，处理归属部门选择变化
const handleDeptChangeInForm = (data) => {
  // 如果已分配业务员，要清除既有的分配业务员和分配时间的信息
  if (form.value.owner) {
    ElMessageBox.confirm('你确定要调整支配业务员所在的部门吗？点击确认，则需要重新分配业务员！', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      form.value.owner = undefined;
      form.value.assignTime = undefined;
    });
  }
};

// 表单中，处理业务员选择变化
const handleOwnerChangeInForm = (data) => {
  // 修改日期格式为 YYYY-MM-DD HH:mm:ss
  form.value.assignTime = new Date()
    .toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    })
    .replace(/\//g, '-');
};

// 表单中，处理线索状态选择变化
const handleStatusChangeInForm = (data) => {
  if (data === '10') {
    // 清空销售机会和客户线索
    form.value.opportunityId = undefined;
    form.value.customerId = undefined;
    // 滚动到转化失败区域
    const loseSection = document.getElementById('lose');
    if (loseSection) {
      loseSection.scrollIntoView({ behavior: 'smooth' });
    }
  }
  if (data === '20' || data === '21') {
    // 清空失败原因和失败原因描述
    form.value.closeReason = undefined;
    form.value.closeDesc = undefined;
    // 滚动到转化成功区域
    const moreSection = document.getElementById('more');
    if (moreSection) {
      moreSection.scrollIntoView({ behavior: 'smooth' });
    }
    if (data === '20') {
      // 清空销售机会
      form.value.opportunityId = undefined;
    }
  }
};

// 添加展开/收起状态变量
const showMoreCondition = ref(false);

/** 查询客户线索列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeAssignTime.value, 'AssignTime');
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listClue(queryParams.value);
  clueList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  isView.value = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  clueFormRef.value?.resetFields();
  areaFormOption.value = [];
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeAssignTime.value = ['', ''];
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

// 查询时，处理归属部门选择变化
const handleDeptChangeQuery = (data) => {
  queryParams.value.ownerDept = data;
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ClueVO[]) => {
  ids.value = selection.map((item) => item.id || '');
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加客户线索';
  dialogEditStatus.value = false;
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ClueVO) => {
  const currentStatus = row?.status;
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getClue(_id);
  Object.assign(form.value, res.data);
  areaFormOption.value = [form.value.provinceId, form.value.cityId, form.value.districtId];
  dialog.visible = true;
  dialog.title = '修改客户线索';
  dialogEditStatus.value = true;
  // 修改这里的调用,传入字典选项和当前状态
  gainNextStatusList('dict_clue_status', dict_clue_status.value, currentStatus);
};

/** 查看按钮操作 */
const handleView = async (row?: ClueVO) => {
  reset();
  const res = await getClue(row.id);
  Object.assign(form.value, res.data);
  isView.value = true;
  dialog.visible = true;
  dialog.title = '查看客户线索';
};

/** 提交按钮 */
const submitForm = () => {
  clueFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      try {
        if (form.value.id) {
          await updateClue(form.value);
        } else {
          await addClue(form.value);
        }
        proxy?.$modal.msgSuccess('操作成功');
        dialog.visible = false;
        await getList();
      } finally {
        buttonLoading.value = false;
      }
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ClueVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm(`是否确认删除客户线索编号为"${_ids}"的数据项？`).finally(() => (loading.value = false));
  await delClue(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'crm/clue/export',
    {
      ...queryParams.value
    },
    `clue_${new Date().getTime()}.xlsx`
  );
};

// 新窗口打开外部链接
const openLink = (url: string) => {
  window.open(url, '_blank');
};

// 打开客户详情（编辑页面）
const openCustomerDetailInEdit = (id: string | number) => {
  const routeUrl = `/crm/bizMg/customer/?id=${id}&openEdit=true`;
  window.open(routeUrl, '_blank');
};

// 打开客户详情（查看页面）
const openCustomerDetailInView = (id: string | number) => {
  const routeUrl = `/crm/bizMg/customer/?id=${id}&openView=true`;
  window.open(routeUrl, '_blank');
};

// 打开销售机会详情（编辑页面）
const openOpportunityDetailInEdit = (id: string | number) => {
  const routeUrl = `/crm/bizMg/opportunity/?id=${id}&openEdit=true`;
  window.open(routeUrl, '_blank');
};

// 打开销售机会详情（查看页面）
const openOpportunityDetailInView = (id: string | number) => {
  const routeUrl = `/crm/bizMg/opportunity/?id=${id}&openView=true`;
  window.open(routeUrl, '_blank');
};

// 打开市场活动详情
const openMarketDetail = (id: string | number) => {
  const routeUrl = `/crm/set/marketing/?id=${id}&openEdit=true`;
  window.open(routeUrl, '_blank');
};

// 选择省份时，加载城市列表
const handleSelectProvinceInQuery = () => {
  loadCityList(queryParams.value.provinceId);
  queryParams.value.cityId = undefined;
  handleQuery();
};

// 获取路由实例
const route = useRoute();
const router = useRouter();

// 外部跳转逻辑维护
const handleOpenDialog = () => {
  // 打开新增弹窗
  const openAdd = route.query.openAdd;
  if (openAdd === 'true') {
    handleAdd();

    // 清除URL参数
    router.replace({ query: {} });
  }
};

onMounted(() => {
  getList();
  loadUserList();
  loadDeptTree();
  handleOpenDialog();
});
</script>

<style scoped>
@import '@/assets/styles/anchorform.scss';
</style>
