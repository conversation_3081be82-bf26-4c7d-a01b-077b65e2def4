<template>
  <div>
    <el-card shadow="hover" style="height: calc(50vh - 60px)">
      <template #header>
        <div class="flex justify-between items-center">
          <div>
            <span>公海客户</span>
            <span style="font-size: 12px; color: #999">
              (最多20条)
              <el-tooltip content="展示过去30天有更新的客户，更多请在客户档案中查看" placement="top">
                <el-icon>
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </span>
          </div>
          <div>
            <el-tooltip content="刷新数据" placement="top">
              <el-button link @click="refresh" icon="Refresh" />
            </el-tooltip>
          </div>
        </div>
      </template>
      <div>
        <el-table :data="highSeaCustomerList" :loading="loading" style="width: 100%" max-height="calc(50vh - 130px)" border>
          <el-table-column prop="name" label="客户名称" min-width="180" show-overflow-tooltip>
            <template #default="scope">
              <el-link type="primary" @click="openCustomerDetailInEdit(scope.row.id)">
                <el-tooltip content="维护客户档案" placement="top">
                  <span>{{ scope.row.name }}</span>
                </el-tooltip>
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" min-width="100">
            <template #default="scope">
              <dict-tag :options="dict_customer_status" :value="scope.row.status || ''" />
            </template>
          </el-table-column>
          <el-table-column label="公海" prop="highSeaName" min-width="140" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.highSeaName }}
            </template>
          </el-table-column>
          <el-table-column width="50" fixed="right">
            <template #default="scope">
              <el-tooltip content="指派承办人" placement="top">
                <el-button link @click="handleReceive(scope.row)" icon="Upload" />
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    <!-- 领取弹窗 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="40%" append-to-body @close="cancel" draggable>
      <div style="margin-bottom: 20px">
        <span style="font-size: 12px; color: #999"
          >当前公海名称为：<span style="color: red">{{ currentHighSeaName }}</span>
          指派了承做人后，系统将自动将客户从公海中移出。
        </span>
      </div>
      <el-form :model="form" :rules="rules" ref="customerFormRef" label-width="100px" label-position="top">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="客户名称" prop="name">
              <el-input v-model="form.name" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status"
              ><template #label>
                <span>
                  客户状态
                  <el-tooltip content="了解更多" placement="top">
                    <el-icon @click="openLink('https://rcn3q3ujmqdd.feishu.cn/docx/KvJxdPIQdoPSchxGEj2c95t8nfh?from=from_copylink')">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-select v-model="form.status" placeholder="请选择客户状态" disabled>
                <el-option v-for="item in filteredStatus" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 指定承办 -->
          <el-col :span="12">
            <el-form-item label="承做部门" prop="ownerDept">
              <el-tree-select
                v-model="form.ownerDept"
                :data="deptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                placeholder="请选择承做部门"
                check-strictly
                filterable
                @change="handleDeptChangeInForm"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="承做人（A角）" prop="owner">
              <el-select v-model="form.owner" placeholder="请输入承做人" clearable @change="handleOwnerChange" filterable :disabled="!form.ownerDept">
                <el-option v-for="item in userOptionsByDeptId" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="协做人（B角）" prop="coOwner">
              <el-select v-model="form.coOwner" placeholder="请输入协做人" clearable filterable :disabled="!form.owner">
                <el-option v-for="item in userOptionsByDeptId" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下次跟进日期" prop="followUpDate">
              <el-date-picker
                clearable
                v-model="form.followUpDate"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择下次跟进日期"
                style="width: 100%"
                :disabled="!form.owner"
                :disabledDate="disabledDate"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" :rows="5" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { listHighSeaCustomer, getCustomer, updateCustomer } from '@/api/crm/customer';
import { CustomerVO, CustomerForm, CustomerQuery } from '@/api/crm/customer/types';
import dayjs from 'dayjs';

// 状态机选择组件的支撑
import { useNextStatusList } from '@/views/org/stateMachine/detail/nextStatusList';
const { nextStatusList, nextStatusLoading, filteredStatus, gainNextStatusList } = useNextStatusList();

const buttonLoading = ref(false);
const loading = ref(true);
const highSeaCustomerList = ref<CustomerVO[]>([]);
const currentHighSeaName = ref<string>('');

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { dict_customer_status } = toRefs<any>(proxy?.useDict('dict_customer_status'));
// 帅选最新30天更新的客户
const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>([
  dayjs().subtract(30, 'day').format('YYYY-MM-DD HH:mm:ss'),
  dayjs().add(1, 'day').format('YYYY-MM-DD HH:mm:ss')
]);

const queryFormRef = ref<ElFormInstance>();
const customerFormRef = ref<ElFormInstance>();

// 部门选择组件相关的支持
import { useDeptSelect } from '@/hooks/useBusiness/deptSelect';
const { deptOptions, loadDeptTree } = useDeptSelect();

// 表单中用户（承做人和协做人等的）选择
import { useUserSelectByDeptId } from '@/views/system/user/detail/userSelectByDeptId';
const { userOptionsByDeptId, loadUserListByDeptId, userLoadingByDeptId } = useUserSelectByDeptId();
import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
const { findUserName, loadUserList, userOptions } = useSysUserSelect();

// 弹窗
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

// 初始化表单
const initFormData: CustomerForm = {
  id: undefined,
  name: undefined,
  customerCode: undefined,
  openCode: undefined,
  highSeaId: undefined,
  highSeaName: undefined,
  owner: undefined,
  ownerDept: undefined,
  coOwner: undefined,
  followUpDate: undefined,
  remark: undefined
};
const data = reactive<PageData<CustomerForm, CustomerQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    name: undefined,
    status: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
    ownerDept: [{ required: true, message: '承做部门不能为空', trigger: 'blur' }],
    owner: [{ required: true, message: '承做人不能为空', trigger: 'blur' }],
    followUpDate: [{ required: true, message: '跟进日期不能为空', trigger: 'blur' }]
  }
});
const { queryParams, form, rules } = toRefs(data);

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

// 表单重置
const reset = () => {
  form.value = { ...initFormData };
  customerFormRef.value?.resetFields();
};
// 查询公海客户
const getHighSeaCustomerList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listHighSeaCustomer(queryParams.value);
  highSeaCustomerList.value = res.rows;
  loading.value = false;
};

/** 领取 */
const handleReceive = async (row: CustomerVO) => {
  reset();
  const res = await getCustomer(row.id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '指派客户' + '(' + row.openCode + ')';
  // 当前公海名称,为弹窗中展示的内容做准备。
  currentHighSeaName.value = form.value.highSeaName;
  // 承做人下拉选项数据
  loadUserListByDeptId(form.value.ownerDept);

  // 客户状态的状态机相关
  const currentStatus = form.value.status;
  gainNextStatusList('dict_customer_status', dict_customer_status.value, currentStatus);
};

/** 提交按钮 */
const submitForm = () => {
  customerFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateCustomer(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getHighSeaCustomerList();
    }
  });
};

/** 刷新 */
const refresh = () => {
  handleQuery();
  ElMessage.success('刷新成功');
};

/** 搜索操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getHighSeaCustomerList();
};

// 部门选项改变时的触发
const handleDeptChangeInForm = async () => {
  // 当前公海ID，以便取消的时候恢复
  const currentHighSeaId = form.value.highSeaId;
  console.log('currentHighSeaId', currentHighSeaId);
  // 如果当前有公海ID,则需要确认
  if (form.value.highSeaId) {
    try {
      await proxy?.$modal.confirm('指派承做人后，客户将从公海【' + currentHighSeaName.value + '】中移出，是否确定？');
      // 确认后清空公海ID、承做人、协做人
      form.value.highSeaId = undefined;
      form.value.owner = undefined;
      form.value.coOwner = undefined;
    } catch {
      // hignSeaId恢复改变之前的值
      ElMessage.info('取消指承做人的操作');
      form.value.highSeaId = currentHighSeaId;
      form.value.ownerDept = undefined;
    }
  }

  form.value.owner = undefined;
  form.value.coOwner = undefined;
  loadUserListByDeptId(form.value.ownerDept);
};

// 根据跟进负责人选项改变时，同步改变跟进负责人所在部门
const handleOwnerChange = async () => {
  // 当前公海ID，以便取消的时候恢复
  const currentDeptId = form.value.ownerDept;
  console.log('currentDeptId', currentDeptId);
  const ownerDeptId = userOptions.value.find((item) => item.userId === form.value.owner)?.deptId;
  console.log('ownerDeptId', ownerDeptId);

  // 如果当前承办人的直接部门（ownerDeptId）与currentDeptId不一样，则要将告知操作者，系统要将承办部门设置为ownerDeptId
  if (currentDeptId !== ownerDeptId) {
    try {
      await proxy?.$modal.confirm('指派跟进人后，承做部门必须为该用户所在的直接部门,请确认？');
      form.value.ownerDept = ownerDeptId;
      loadUserListByDeptId(form.value.ownerDept);
    } catch {
      ElMessage.info('取消指派跟进人');
      form.value.ownerDept = currentDeptId;
      form.value.owner = undefined;
      form.value.coOwner = undefined;
    }
  }
};
// 打开客户详情(编辑页面)
const openCustomerDetailInEdit = (id: string | number) => {
  const routeUrl = `/crm/bizMg/customer/?id=${id}&openEdit=true`;
  window.open(routeUrl, '_blank');
};

// 打开链接
const openLink = (url: string) => {
  window.open(url, '_blank');
};

const disabledDate = (time: Date) => {
  const today = new Date();
  const maxDate = new Date();
  maxDate.setDate(today.getDate() + 180);
  return time.getTime() < today.getTime() - 8.64e7 || time.getTime() > maxDate.getTime();
};

onMounted(() => {
  getHighSeaCustomerList();
  loadUserList();
  loadDeptTree();
});
</script>
