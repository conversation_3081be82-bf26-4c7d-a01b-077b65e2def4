<template>
  <div>
    <el-table :data="customerList" :loading="loading" style="width: 100%" max-height="35vh" border>
      <el-table-column prop="name" label="客户名称" min-width="180" show-overflow-tooltip>
        <template #default="scope">
          <el-link type="primary" @click="openCustomerDetailInEdit(scope.row.id)">
            <el-tooltip content="维护客户档案" placement="top">
              <span>{{ scope.row.name }}</span>
            </el-tooltip>
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" min-width="100">
        <template #default="scope">
          <dict-tag :options="dict_customer_status" :value="scope.row.status || ''" />
        </template>
      </el-table-column>
      <el-table-column prop="followUpDate" label="下次跟进时间" width="140">
        <template #default="scope">
          <span style="display: flex; align-items: center; gap: 5px">
            <el-tooltip :content="`承做人：${scope.row.ownerNickName}`" placement="top">
              <el-link type="primary" @click="updateFollowUpDate(scope.row)">
                <span>{{ parseTime(scope.row.followUpDate, '{y}-{m}-{d}') }}</span>
              </el-link>
            </el-tooltip>
            <!-- <el-button link @click="updateFollowUpDate(scope.row)" icon="EditPen" /> -->
          </span>
        </template>
      </el-table-column>
      <el-table-column label="距今天" min-width="60">
        <template #default="scope">
          <span>{{ dayjs(scope.row.followUpDate).diff(dayjs(), 'day') }}天</span>
        </template>
      </el-table-column>
      <el-table-column width="50" fixed="right">
        <template #default="scope">
          <el-tooltip content="新增跟进记录" placement="top">
            <el-button link @click="handleAddActivity(scope.row)" icon="EditPen" />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <!-- 跟进时间调整弹窗 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body @close="cancel" draggable>
      <el-form :model="form" :rules="rules" ref="customerFormRef" label-width="100px" label-position="top">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="客户名称" prop="name">
              <el-input v-model="form.name" disabled />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="客户状态" prop="status">
              <template #label>
                <span>
                  客户状态
                  <el-tooltip content="了解更多" placement="top">
                    <el-icon @click="openLink('https://rcn3q3ujmqdd.feishu.cn/docx/KvJxdPIQdoPSchxGEj2c95t8nfh?from=from_copylink')">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-select v-model="form.status" placeholder="请选择客户状态">
                <el-option v-for="item in filteredStatus" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划跟进" prop="followUpDate">
              <el-date-picker
                clearable
                v-model="form.followUpDate"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择下次跟进日期"
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户编码" prop="customerCode" :required="form.status === '11'">
              <el-input v-model="form.customerCode" placeholder="ERP中对应的编码" :disabled="form.status !== '11'" />
            </el-form-item>
          </el-col>
          <el-col :span="12"></el-col>
          <el-col :span="12"></el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { listCustomer, getCustomer, updateCustomer } from '@/api/crm/customer';
import type { CustomerVO, CustomerQuery, CustomerForm } from '@/api/crm/customer/types';
import dayjs from 'dayjs';
import { parseTime } from '@/utils/ruoyi';
import type { ComponentInternalInstance } from 'vue';
import { ComponentCustomProperties } from 'vue';

// 状态机选择组件的支撑
import { useNextStatusList } from '@/views/org/stateMachine/detail/nextStatusList';
const { nextStatusList, nextStatusLoading, filteredStatus, gainNextStatusList } = useNextStatusList();

const buttonLoading = ref(false);
const loading = ref(true);
const customerList = ref<CustomerVO[]>([]);
// 筛选出过去2天内和未来30天的跟进客户
const dateRangeFollowUpDate = ref<[DateModelType, DateModelType]>([
  dayjs().subtract(2, 'day').format('YYYY-MM-DD HH:mm:ss'),
  dayjs().add(30, 'day').format('YYYY-MM-DD HH:mm:ss')
]);
const total = ref(0);

// 字典
const { proxy } = getCurrentInstance() as ComponentInternalInstance & { proxy: ComponentCustomProperties };
const { dict_customer_status } = toRefs<any>(proxy?.useDict('dict_customer_status'));

const queryFormRef = ref<ElFormInstance>();
const customerFormRef = ref<ElFormInstance>();

// 弹窗
const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

// 初始化表单
const initFormData: CustomerForm = {
  id: undefined,
  name: undefined,
  customerCode: undefined,
  openCode: undefined,
  aliasName: undefined,
  status: undefined,
  followUpDate: undefined,
  remark: undefined
};
const data = reactive<PageData<CustomerForm, CustomerQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    status: undefined,
    params: {
      followUpDate: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    followUpDate: [{ required: true, message: '跟进日期不能为空', trigger: 'blur' }]
  }
});
const { queryParams, form, rules } = toRefs(data);

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 查询客户档案列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeFollowUpDate.value, 'FollowUpDate');
  const res = await listCustomer(queryParams.value);
  customerList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 搜索操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

// 表单重置
const reset = () => {
  form.value = { ...initFormData };
  customerFormRef.value?.resetFields();
};

// 更新跟进日期
const updateFollowUpDate = async (row?: CustomerVO) => {
  reset();
  const res = await getCustomer(row?.id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '更新跟进日期' + '(' + form.value.openCode + ')';

  // 客户状态的状态机相关
  const currentStatus = form.value.status;
  gainNextStatusList('dict_customer_status', dict_customer_status.value, currentStatus);
};

/** 提交按钮 */
const submitForm = () => {
  customerFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateCustomer(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

// 暴露方法给父组件调用
defineExpose({
  handleQuery
});

// 打开客户详情(编辑页面)
const openCustomerDetailInEdit = (id: string | number) => {
  const routeUrl = `/crm/bizMg/customer/?id=${id}&openEdit=true`;
  window.open(routeUrl, '_blank');
};

// 打开跟进记录(新增页面)
const handleAddActivity = (row: CustomerVO) => {
  const routeUrl = `/crm/bizMg/activity?customerId=${row.id}&openAdd=true`;
  window.open(routeUrl, '_blank');
};

// 打开链接
const openLink = (url: string) => {
  window.open(url, '_blank');
};

onMounted(() => {
  getList();
});
</script>
