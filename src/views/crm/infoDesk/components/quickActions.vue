<template>
  <div class="card-container">
    <!-- 新建客户档案 -->
    <div class="card-item" @click="handleAddCustomer">
      <div class="text">
        <el-icon>
          <Avatar />
        </el-icon>
        <span>新建客户档案</span>
        <el-tooltip content="你应该按步将客户推进到「成交客户」，你的努力值得记载。" placement="top">
          <el-icon style="font-size: 14px; color: #999">
            <InfoFilled />
          </el-icon>
        </el-tooltip>
      </div>
      <div class="description">
        <span style="font-size: 12px; color: #999">你是业务员，你可以从这里开始每一个新客户。</span>
      </div>
    </div>
    <!-- 维护客户档案 -->
    <div class="card-item" @click="handleMainstanceCustomer">
      <div class="text">
        <el-icon>
          <Setting />
        </el-icon>
        <span>维护客户</span>
        <el-tooltip content="客户的维护没有一劳永逸，你应该持续跟进客户，并将其推进到「成交客户」。" placement="top">
          <el-icon style="font-size: 14px; color: #999">
            <InfoFilled />
          </el-icon>
        </el-tooltip>
      </div>
      <div class="description">
        <span style="font-size: 12px; color: #999">你的客户如果有任何变动，你应该及时更新客户档案。</span>
      </div>
    </div>
    <!-- 添加线索 -->
    <div class="card-item" @click="handleAddClue">
      <div class="text">
        <el-icon>
          <EditPen />
        </el-icon>
        <span>添加线索</span>
        <el-tooltip content="销售机会始于线索，添加线索后，您可以跟进线索，并将其转换为客户档案或销售机会。" placement="top">
          <el-icon style="font-size: 14px; color: #999">
            <InfoFilled />
          </el-icon>
        </el-tooltip>
      </div>
      <div class="description">
        <span style="font-size: 12px; color: #999">销售机会始于线索。好记忆不如烂笔头，你应该即时添加线索。</span>
      </div>
    </div>
    <!-- 帮助手册 -->
    <div class="card-item" @click="handleHelpManual">
      <div class="text">
        <el-icon>
          <Help />
        </el-icon>
        <span>帮助手册</span>
      </div>
      <div class="description">
        <span style="font-size: 12px; color: #999">如果你对CRM模块不熟悉，请参考帮助手册，系统会持续更新文档。</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Plus, Setting, Help, Avatar } from '@element-plus/icons-vue';

// 打开新增页面
const handleAddClue = () => {
  const routeUrl = `/crm/bizMg/clue?openAdd=true`;
  window.open(routeUrl, '_blank');
};

const handleAddCustomer = () => {
  const routeUrl = `/crm/bizMg/customer?openAdd=true`;
  window.open(routeUrl, '_blank');
};

const handleMainstanceCustomer = () => {
  const routeUrl = `/crm/bizMg/customer`;
  window.open(routeUrl, '_blank');
};

const handleHelpManual = () => {
  const routeUrl = `https://rcn3q3ujmqdd.feishu.cn/docx/PrVjdl3tzorCj9xLZt2ceJtpn6g?from=from_copylink`;
  window.open(routeUrl, '_blank');
};
</script>

<style lang="scss" scoped>
.card-container {
  display: flex;
  flex-direction: row;
  gap: 10px;
  overflow-x: auto;
  padding-bottom: 10px;

  &::-webkit-scrollbar {
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }
}

.card-item {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: start;
  gap: 5px;
  padding: 10px;
  border-radius: 5px;
  width: 165px;
  height: 110px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #f5f7fa;
  &:hover {
    background-color: #eef5fe;
    border: 1px solid #cbe1fc;
  }

  .text {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: start;
    gap: 5px;
  }
}
</style>
