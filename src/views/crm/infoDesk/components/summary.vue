<template>
  <!-- 开发中.....
  <div>展示：处于跟进中的线索的数量；处于公海的的客户数量；客户总数和按客户状态统计的数量；销售机会的总数和重要状态的销售机数量和金额</div> -->
  <div class="echarts-container">
    <div class="echarts-item">
      <Pie :data="OpportunityByPhaseData" title="各阶段的销售机会数量(个)" />
    </div>
    <div class="echarts-item">
      <Pie :data="OpportunityAmountByPhaseDataToPie" title="各阶段销售机会预期金额（万元）" />
    </div>
    <div class="echarts-item">
      <Pie :data="CustomerByStatusData" title="各状态的客户数量(个)" />
    </div>
    <div class="echarts-item">
      <Pie :data="ClueByStatusData" title="各状态的线索数量(个)" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { getCustomerByStatus, getOpportunityByPhase, getClueByStatus } from '@/api/crm/infoDesk';
import Pie from '@/components/echarts/Pie.vue';
import Bar from '@/components/echarts/Bar.vue';
import { ref } from 'vue';

interface AxisData {
  xData: string[];
  yData: number[];
}

const OpportunityByPhaseData = ref([]);
const OpportunityAmountByPhaseDataToPie = ref([]);
const OpportunityAmountByPhaseDataToBar = ref<AxisData>({ xData: [], yData: [] });
const CustomerByStatusData = ref([]);
const ClueByStatusData = ref([]);
// getOpportunityByPhase

const transformData = (data: any, label: string, value: string) => {
  return data.map((item) => ({
    name: item[label],
    value: value === 'total_expect_amount' ? Number((item[value] / 10000).toFixed(2)) : item[value]
  }));
};

const transformToAxisData = (data: any, labelKey: string, valueKey: string) => {
  const xData = data.map((item) => item[labelKey]);
  const yData = data.map((item) => (valueKey === 'total_expect_amount' ? Number((item[valueKey] / 10000).toFixed(2)) : item[valueKey]));
  return {
    xData,
    yData
  };
};

const getOpportunityByPhaseAPI = () => {
  getOpportunityByPhase().then((res) => {
    if (res.code === 200) {
      OpportunityByPhaseData.value = transformData(res.data, 'phaseLabel', 'count');
      OpportunityAmountByPhaseDataToPie.value = transformData(res.data, 'phaseLabel', 'total_expect_amount');
      const axisData = transformToAxisData(res.data, 'phaseLabel', 'total_expect_amount');
      OpportunityAmountByPhaseDataToBar.value = axisData;
    }
  });
};

const getCustomerByStatusAPI = () => {
  getCustomerByStatus().then((res) => {
    if (res.code === 200) {
      CustomerByStatusData.value = transformData(res.data, 'statusLabel', 'count');
    }
  });
};

const getClueByStatusAPI = () => {
  getClueByStatus().then((res) => {
    if (res.code === 200) {
      ClueByStatusData.value = transformData(res.data, 'statusLabel', 'count');
    }
  });
};
getOpportunityByPhaseAPI();
getCustomerByStatusAPI();
getClueByStatusAPI();
</script>

<style scoped lang="scss">
.echarts-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 10px 0px;
  max-height: calc(100vh - 400px);
  overflow-y: auto;
  padding-right: 10px;

  .echarts-item {
    width: 100%;
    min-height: 380px;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }
}
</style>
