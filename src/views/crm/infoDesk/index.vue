<template>
  <div class="info-desk">
    <div class="p-2">
      <el-row :gutter="10">
        <el-col :span="12">
          <el-row :gutter="10">
            <el-col :span="24">
              <el-card shadow="hover" class="mb-2" style="height: 180px">
                <template #header>
                  <div class="flex justify-between items-center">
                    <span>快捷操作</span>
                  </div>
                </template>
                <QuickActions />
              </el-card>
            </el-col>
            <el-col :span="24">
              <el-card shadow="hover" style="height: calc(68vh)">
                <template #header>
                  <div class="flex justify-between items-center">
                    <span class="flex flex-row items-center" style="gap: 10px">
                      <span>客情摘要</span>
                      <span style="font-size: 12px; color: #999">
                        <el-tooltip content="按更新时间统计近365天的数据" placement="top">
                          <el-icon>
                            <QuestionFilled />
                          </el-icon>
                        </el-tooltip>
                      </span>
                    </span>
                  </div>
                </template>
                <Summary />
              </el-card>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="12">
          <el-row :gutter="10">
            <el-col :span="24">
              <el-card shadow="hover" class="mb-2" style="height: calc(50vh - 50px)">
                <template #header>
                  <div class="flex justify-between items-center">
                    <span class="flex flex-row items-center" style="gap: 10px">
                      <span>跟进计划</span>
                      <span style="font-size: 12px; color: #999">
                        (最多20条)
                        <el-tooltip content="展示过去2天到未来30天的跟进计划，更多请在客户档案中查看" placement="top">
                          <el-icon>
                            <QuestionFilled />
                          </el-icon>
                        </el-tooltip>
                      </span>
                    </span>
                    <div>
                      <el-tooltip content="如果你更新可客户的跟进时间计划，请刷新" placement="top">
                        <el-button link @click="handleRefresh" icon="Refresh" />
                      </el-tooltip>
                      <el-button type="primary" @click="dialogVisible = true">添加跟进</el-button>
                    </div>
                  </div>
                </template>
                <Follow ref="followRef" />
              </el-card>
            </el-col>
            <el-col :span="24">
              <!-- 公海客户 -->
              <HighSeaCustomer />
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>

    <el-dialog v-model="dialogVisible" title="添加跟进的操作步骤" width="500px" append-to-body>
      <ol>
        <li>
          <span> 点击「立即前往」按钮，跳转至客户档案页面</span>
        </li>
        <li>
          <span>在客户档案页面，筛选到需要添加跟进计划的客户，点击「编辑」按钮，进行编辑。</span>
        </li>
        <li>
          <span>在客户档案页面，维护<span class="text-red-500">「下次跟进时间」</span>，点击「保存」按钮。</span>
        </li>
        <li>
          <span class="text-red-500">如果客户尚未建档，则需要先建档。</span>
        </li>
      </ol>
      <template #footer>
        <!-- <el-button @click="dialogVisible = false">取 消</el-button> -->
        <el-button type="primary" @click="goToCustomer">立即前往</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import QuickActions from './components/quickActions.vue';
import Summary from './components/summary.vue';
import Follow from './components/follow.vue';
import HighSeaCustomer from './components/highSeaCustomer.vue';
const dialogVisible = ref(false);
const followRef = ref();

const goToCustomer = () => {
  const routeUrl = `/crm/bizMg/customer`;
  window.open(routeUrl, '_blank');
  dialogVisible.value = false;
};

const handleRefresh = () => {
  followRef.value?.handleQuery();
  ElMessage.success('刷新成功');
};
</script>

<style lang="scss" scoped></style>
