<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="活动名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入活动名称" clearable @keyup.enter="handleQuery" style="width: '100%'" />
            </el-form-item>

            <el-form-item label="活动类型" prop="type">
              <dict-select
                v-model="queryParams.type"
                dict-key="dict_markting_type"
                placeholder="请选择活动类型"
                clearable
                :show-footer="false"
                style="width: '100%'"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="活动状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择活动状态" clearable style="width: '100%'" @change="handleQuery">
                <el-option v-for="dict in dict_markting_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="开始日期" style="width: 308px">
              <el-date-picker
                v-model="dateRangeStartDate"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                @change="handleQuery"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="结束日期" style="width: 308px">
              <el-date-picker
                v-model="dateRangeEndDate"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                @change="handleQuery"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="更新人" prop="updateBy">
              <el-select v-model="queryParams.updateBy" placeholder="请选择更新人" clearable @change="handleQuery" filterable style="width: '100%'">
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['biz/crm:marketing:add']">新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['biz/crm:marketing:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['biz/crm:marketing:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['biz/crm:marketing:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="marketingList" @selection-change="handleSelectionChange" border>
        <el-table-column type="index" width="40" align="center" label="#" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="活动名称" prop="name" min-width="250px" />
        <el-table-column label="活动描述" prop="description" min-width="200px" show-overflow-tooltip />
        <el-table-column label="活动类型" align="center" prop="type" min-width="150px">
          <template #default="scope">
            <dict-tag :options="dict_markting_type" :value="scope.row.type" />
          </template>
        </el-table-column>
        <el-table-column label="活动状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="dict_markting_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="执行状态" align="center" width="100">
          <template #default="scope">
            <el-tag :type="getExecutionStatusType(scope.row)">{{ getExecutionStatusText(scope.row) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="开始时间" prop="startDate" width="100">
          <template #default="scope">
            <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="结束时间" prop="endDate" width="100">
          <template #default="scope">
            <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="150" max-width="250" show-overflow-tooltip />
        <el-table-column label="更新人" prop="updateBy" width="150">
          <template #default="scope">
            <span>{{ findUserName(scope.row.updateBy) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['crm:marketing:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top" v-if="scope.row.status === '01'">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['crm:marketing:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改市场活动对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="40%" append-to-body>
      <el-form ref="marketingFormRef" :model="form" :rules="rules" label-width="80px" label-position="top">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="活动名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入活动名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="活动类型" prop="type">
              <dict-select v-model="form.type" dict-key="dict_markting_type" placeholder="请选择活动类型" style="width: '100%'" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="活动描述" prop="description">
              <el-input v-model="form.description" type="textarea" placeholder="请输入内容" maxlength="500" show-word-limit :rows="5" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="活动状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in dict_markting_status" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效时间" prop="startDate">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                @change="handleDateRangeChangeInForm"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12"></el-col>
          <el-col :span="12"></el-col>
          <el-col :span="12"></el-col>
          <el-col :span="12"></el-col>
          <el-col :span="12"></el-col>
          <el-col :span="12"></el-col>
        </el-row>
        <el-form-item label="附件" prop="files">
          <file-upload v-model="form.files" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" maxlength="500" show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Marketing" lang="ts">
import { listMarketing, getMarketing, delMarketing, addMarketing, updateMarketing } from '@/api/crm/marketing';
import { MarketingVO, MarketingQuery, MarketingForm } from '@/api/crm/marketing/types';
import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
import DictSelect from '@/components/DictSelect/index.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { dict_markting_type, dict_markting_status } = toRefs<any>(proxy?.useDict('dict_markting_type', 'dict_markting_status'));
const { findUserName, loadUserList, userOptions } = useSysUserSelect();

const marketingList = ref<MarketingVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref<[string, string]>(['', '']);
const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeStartDate = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeEndDate = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const marketingFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

// 获取路由实例
const route = useRoute();
const router = useRouter();

const initFormData: MarketingForm = {
  id: undefined,
  name: undefined,
  description: undefined,
  type: undefined,
  status: '01',
  startDate: undefined,
  endDate: undefined,
  files: undefined,
  remark: undefined
};
const data = reactive<PageData<MarketingForm, MarketingQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    description: undefined,
    type: undefined,
    status: undefined,
    updateBy: undefined,
    params: {
      startDate: undefined,
      endDate: undefined,
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    name: [{ required: true, message: '活动名称不能为空', trigger: 'blur' }],
    description: [{ required: true, message: '活动描述不能为空', trigger: 'blur' }],
    type: [{ required: true, message: '活动类型不能为空', trigger: 'change' }],
    status: [{ required: true, message: '活动状态不能为空', trigger: 'change' }],
    startDate: [{ required: true, message: '开始时间不能为空', trigger: 'blur' }],
    endDate: [{ required: true, message: '结束时间不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询市场活动列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};

  // // 处理开始日期和结束日期
  // if (queryParams.value.startDate) {
  //   queryParams.value.params.beginStartDate = queryParams.value.startDate[0];
  //   queryParams.value.params.endStartDate = queryParams.value.startDate[1];
  //   queryParams.value.startDate = undefined; // 清除原始数组
  // }

  // if (queryParams.value.endDate) {
  //   queryParams.value.params.beginEndDate = queryParams.value.endDate[0];
  //   queryParams.value.params.endEndDate = queryParams.value.endDate[1];
  //   queryParams.value.endDate = undefined; // 清除原始数组
  // }

  proxy?.addDateRange(queryParams.value, dateRangeStartDate.value, 'StartDate');
  proxy?.addDateRange(queryParams.value, dateRangeEndDate.value, 'EndDate');

  // 处理更新时间
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');

  const res = await listMarketing(queryParams.value);
  marketingList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  dateRange.value = ['', ''];
  marketingFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeStartDate.value = ['', ''];
  dateRangeEndDate.value = ['', ''];
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: MarketingVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加市场活动';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: MarketingVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getMarketing(_id);
  Object.assign(form.value, res.data);
  dateRange.value = [form.value.startDate, form.value.endDate];
  dialog.visible = true;
  dialog.title = '修改市场活动';
};

/** 提交按钮 */
const submitForm = () => {
  marketingFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateMarketing(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addMarketing(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
  // console.log('提交后', queryParams.value);
};

/** 删除按钮操作 */
const handleDelete = async (row?: MarketingVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除市场活动编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delMarketing(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'biz/crm/marketing/export',
    {
      ...queryParams.value
    },
    `marketing_${new Date().getTime()}.xlsx`
  );
};

const handleDateRangeChangeInForm = (val: [string, string]) => {
  if (val) {
    form.value.startDate = val[0];
    form.value.endDate = val[1];
  } else {
    form.value.startDate = undefined;
    form.value.endDate = undefined;
  }

  // console.log('提交前', queryParams.value);
};

const handleDateRangeChange = (val: [string, string]) => {
  if (val) {
    queryParams.value.startDate = val[0];
    queryParams.value.endDate = val[1];
  } else {
    queryParams.value.startDate = undefined;
    queryParams.value.endDate = undefined;
  }
};

const getExecutionStatusText = (row: MarketingVO) => {
  const now = new Date().getTime();
  const start = new Date(row.startDate).getTime();
  const end = new Date(row.endDate).getTime();

  if (now < start) {
    return '未开始';
  } else if (now > end) {
    return '已结束';
  } else {
    return '进行中';
  }
};

const getExecutionStatusType = (row: MarketingVO) => {
  const status = getExecutionStatusText(row);
  const statusMap = {
    '未开始': 'info',
    '进行中': 'success',
    '已结束': 'warning'
  } as const;
  return statusMap[status] as 'success' | 'warning' | 'info' | 'primary' | 'danger';
};

// 外页面跳转逻辑维护
const handleOpenDialog = () => {
  // 打开市场机会详情
  const id = route.query.id;
  const openEdit = route.query.openEdit;

  if (id && openEdit === 'true') {
    nextTick(() => {
      handleUpdate({ id: id } as MarketingVO);
      router.replace({ query: {} });
    });
  }
};

onMounted(() => {
  getList();
  handleOpenDialog();
  loadUserList();
});
</script>
