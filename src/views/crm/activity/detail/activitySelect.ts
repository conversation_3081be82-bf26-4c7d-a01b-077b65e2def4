import { ref } from 'vue';
import { listActivity } from '@/api/crm/activity';
import type { ActivityVO } from '@/api/crm/activity/types';

export const useActivitySelect = () => {
  const activityOptions = ref<ActivityVO[]>([]);
  const activityLoading = ref(false);

  const loadActivityList = async (customerId: string | number) => {
    activityLoading.value = true;
    try {
      const res = await listActivity({ customerId, pageNum: 1, pageSize: 30 });
      activityOptions.value = res.rows;
    } finally {
      activityLoading.value = false;
    }
  };

  return {
    activityOptions,
    activityLoading,
    loadActivityList
  };
};
