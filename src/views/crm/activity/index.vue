<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px" class="search-form-container">
            <el-form-item label="客户名称" prop="customerId">
              <customer-select v-model="queryParams.customerId" placeholder="请选择客户档案" @change="handleQuery" />
            </el-form-item>
            <el-form-item label="联系人" prop="contactId">
              <el-input v-model="queryParams.contactId" placeholder="请输入联系人" clearable @keyup.enter="handleQuery" />
            </el-form-item>

            <el-form-item label="跟进日期" prop="activityAt" style="width: 94%">
              <el-date-picker
                v-model="dateRangeActivityAt"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>

            <el-form-item label="跟进人" prop="activityBy">
              <el-select v-model="queryParams.activityBy" placeholder="请选择跟进人" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
            <el-form-item label="跟进方式" prop="activityWay">
              <dict-select
                v-model="queryParams.activityWay"
                dict-key="dict_activity_way"
                placeholder="请选择跟进方式"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="跟进类型" prop="type">
              <dict-select
                v-model="queryParams.type"
                dict-key="dict_activity_type"
                placeholder="请选择跟进类型"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="跟进对象" prop="activityTo" v-if="showMoreCondition">
              <dict-select
                v-model="queryParams.activityTo"
                dict-key="dict_activity_to"
                placeholder="请选择跟进对象"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="更新人" prop="updateBy" v-if="showMoreCondition">
              <el-select v-model="queryParams.updateBy" placeholder="请输入更新人" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新时间" prop="updateTime" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['crm:activity:add']">新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['crm:activity:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['crm:activity:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['crm:activity:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="activityList" @selection-change="handleSelectionChange" border>
        <!-- <el-table-column type="selection" width="55" /> -->
        <el-table-column label="#" type="index" align="center" fixed="left" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="客户档案" prop="customerName" width="200" show-overflow-tooltip>
          <template #default="scope">
            <el-tooltip content="查看客户档案" placement="top">
              <el-link type="primary" @click="openCustomerDetailInView(scope.row.customerId)">{{ scope.row.customerName }}</el-link>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="联系人姓名" prop="contactName" width="150" />
        <el-table-column label="跟进日期" prop="activityAt" width="100">
          <template #default="scope">
            <span>{{ parseTime(scope.row.activityAt, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="跟进记录" prop="logDesc" min-width="200" :show-overflow-tooltip="true">
          <template #default="scope">
            <el-tooltip v-if="scope.row.remark" content="纪要详情" placement="top">
              <el-icon @click="openLink(scope.row.remark)"><Link /></el-icon>
            </el-tooltip>
            <span style="padding-left: 10px">{{ scope.row.logDesc }}</span>
          </template>
        </el-table-column>

        <el-table-column label="跟进人" prop="activityBy" width="150">
          <template #default="scope">
            <span>{{ findUserName(scope.row.activityBy) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="跟进方式" prop="activityWay" width="100">
          <template #default="scope">
            <dict-tag :options="dict_activity_way" :value="scope.row.activityWay" />
          </template>
        </el-table-column>
        <el-table-column label="跟进类型" prop="type" width="100">
          <template #default="scope">
            <dict-tag :options="dict_activity_type" :value="scope.row.type" />
          </template>
        </el-table-column>
        <el-table-column label="跟进对象" prop="activityTo" width="100">
          <template #default="scope">
            <dict-tag :options="dict_activity_to" :value="scope.row.activityTo" />
          </template>
        </el-table-column>
        <el-table-column label="更新人" prop="updateBy" width="150">
          <template #default="scope">
            <span>{{ findUserName(scope.row.updateBy) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" width="150" fixed="right">
          <template #default="scope">
            <el-tooltip content="查看" placement="top">
              <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['crm:activity:view']"></el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['crm:activity:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top" v-if="canOperated(scope.row.createTime)">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['crm:activity:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改跟进记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="60%" append-to-body @close="cancel">
      <el-form ref="activityFormRef" :model="form" :rules="rules" label-width="80px" label-position="top">
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="客户名称" prop="customerId">
              <template #label>
                <span>
                  客户名称
                  <el-tooltip content="查看客户档案" placement="top">
                    <el-icon class="el-icon--right" @click="openCustomerDetailInView(form.customerId)" v-if="form.customerId"><View /></el-icon>
                  </el-tooltip>
                </span>
              </template>
              <customer-select v-model="form.customerId" placeholder="请选择客户档案" @change="handleCustomerChange" :disabled="dialogEditStatus" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系人" prop="contactId">
              <el-select
                v-model="form.contactId"
                placeholder="请选择联系人"
                filterable
                remote
                reserve-keyword
                :loading="contactLoading"
                :disabled="!form.customerId || isView"
              >
                <el-option v-for="item in contactOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="跟进日期" prop="activityAt">
              <el-date-picker
                clearable
                v-model="form.activityAt"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择跟进日期"
                style="width: 100%"
                :disabled="isView"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="跟进人" prop="activityBy">
              <el-select v-model="form.activityBy" placeholder="请选择跟进人" clearable filterable :disabled="isView">
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="跟进方式" prop="activityWay">
              <dict-select v-model="form.activityWay" dict-key="dict_activity_way" placeholder="请选择跟进方式" :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="跟进类型" prop="type">
              <dict-select v-model="form.type" dict-key="dict_activity_type" placeholder="请选择跟进类型" :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="跟进纪要" prop="logDesc">
              <el-input
                v-model="form.logDesc"
                type="textarea"
                placeholder="请输入内容"
                :rows="8"
                show-word-limit
                :maxlength="500"
                :disabled="isView"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="纪要备注" prop="remark">
              <template #label>
                <span>
                  更多纪要内容
                  <el-tooltip content="请在飞书中维护纪要内容，然后将链接粘贴到此处" placement="top">
                    <el-icon class="el-icon--right">
                      <InfoFilled />
                    </el-icon>
                  </el-tooltip>
                  <el-tooltip v-if="form.remark" content="单击打开链接" placement="top">
                    <el-icon @click="openLink(form.remark)"><Link /></el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-input v-model="form.remark" placeholder="请输入内容" :disabled="isView" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="跟进对象" prop="activityTo">
              <dict-select
                v-model="form.activityTo"
                dict-key="dict_activity_to"
                placeholder="请选择跟进对象"
                :show-footer="false"
                @change="handleActivityToChange"
                :disabled="isView"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 客户线索的dict_key = 1； 客户档案的dict_key = 2； 销售机会的dict_key = 3 -->
            <el-form-item label="客户线索" prop="toId" v-if="form.activityTo === '1'">
              <opportunity-select v-model="form.toId" placeholder="请选择客户线索" :disabled="isView" />
            </el-form-item>
            <el-form-item label="客户档案" prop="toId" v-else-if="form.activityTo === '2'">
              <customer-select v-model="form.toId" placeholder="请选择客户档案" :disabled="form.activityTo === '2' || isView" />
            </el-form-item>
            <el-form-item label="销售机会" prop="toId" v-else-if="form.activityTo === '3'">
              <opportunity-select v-model="form.toId" placeholder="请选择销售机会" :disabled="isView" />
            </el-form-item>
            <el-form-item label="跟进对象" prop="toId" v-else>
              <el-input v-model="form.toId" placeholder="请先选择跟进对象" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="附件" prop="files">
              <file-upload v-model="form.files" :disabled="isView" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm" :disabled="isView">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Activity" lang="ts">
import { listActivity, getActivity, delActivity, addActivity, updateActivity } from '@/api/crm/activity';
import { ActivityVO, ActivityQuery, ActivityForm } from '@/api/crm/activity/types';
import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
import { useContactSelect } from '@/views/crm/contact/contactSelect';
const { contactOptions, contactLoading, loadContactList } = useContactSelect();
import { canOperated } from '@/utils/biz/canOperated';
import { useUserStore } from '@/store/modules/user';
const userStore = useUserStore();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { dict_activity_to, dict_activity_way, dict_activity_type } = toRefs<any>(
  proxy?.useDict('dict_activity_to', 'dict_activity_way', 'dict_activity_type')
);

const activityList = ref<ActivityVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeActivityAt = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);
const showMoreCondition = ref(false);

const dialogEditStatus = ref(false); // 是否是编辑状态,true为编辑，false为新增

const queryFormRef = ref<ElFormInstance>();
const activityFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ActivityForm = {
  id: undefined,
  customerId: undefined,
  contactId: undefined,
  activityAt: proxy?.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}'),
  activityBy: Number(userStore.userId),
  activityWay: undefined,
  type: undefined,
  logDesc: undefined,
  activityTo: undefined,
  toId: undefined,
  files: undefined,
  remark: undefined
};
const data = reactive<PageData<ActivityForm, ActivityQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    customerId: undefined,
    contactId: undefined,
    activityBy: undefined,
    activityWay: undefined,
    type: undefined,
    activityTo: undefined,
    toId: undefined,
    updateBy: undefined,
    params: {
      activityAt: undefined,
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    customerId: [{ required: true, message: '客户名称不能为空', trigger: 'blur' }],
    activityAt: [{ required: true, message: '跟进日期不能为空', trigger: 'blur' }],
    activityBy: [{ required: true, message: '跟进人不能为空', trigger: 'blur' }],
    activityWay: [{ required: true, message: '跟进方式不能为空', trigger: 'change' }],
    type: [{ required: true, message: '跟进类型不能为空', trigger: 'change' }],
    logDesc: [
      { required: true, message: '跟进记录不能为空', trigger: 'blur' },
      { max: 500, message: '跟进记录不能超过500个字符', trigger: 'blur' }
    ],
    remark: [
      {
        validator: (rule: any, value: string, callback: (error?: Error) => void) => {
          if (!value) {
            callback();
            return;
          }
          if (!value.startsWith('https://')) {
            callback(new Error('纪要链接必须以 https:// 开头'));
            return;
          }
          callback();
        },
        trigger: 'blur'
      }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

const { findUserName, loadUserList, userOptions } = useSysUserSelect();

// 获取路由实例
const route = useRoute();
const router = useRouter();

/** 查询跟进记录列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeActivityAt.value, 'ActivityAt');
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listActivity(queryParams.value);
  activityList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  isView.value = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  activityFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeActivityAt.value = ['', ''];
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ActivityVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  loadContactList(form.value.customerId);
  reset();
  dialog.visible = true;
  dialog.title = '添加跟进记录';
  dialogEditStatus.value = false;
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ActivityVO) => {
  loadContactList(form.value.customerId);
  isView.value = false;
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getActivity(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改跟进记录';
  dialogEditStatus.value = true;
};

/** 查看按钮操作 */
const isView = ref(false); // 是否是查看模式, 查看模式下，表单组件处于禁用状态
const handleView = async (row?: ActivityVO) => {
  loadContactList(form.value.customerId);
  reset();
  const res = await getActivity(row?.id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '查看跟进记录';
  isView.value = true;
  dialogEditStatus.value = true;
};

/** 提交按钮 */
const submitForm = () => {
  activityFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateActivity(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addActivity(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ActivityVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除跟进记录编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delActivity(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'crm/activity/export',
    {
      ...queryParams.value
    },
    `activity_${new Date().getTime()}.xlsx`
  );
};

// 客户选择变化
const handleCustomerChange = (value: string | number) => {
  form.value.contactId = undefined;
  if (form.value.activityTo === '2') {
    form.value.toId = value;
  }
  loadContactList(value);
};

// 新窗口打开外部链接
const openLink = (url: string) => {
  window.open(url, '_blank');
};

// 打开客户详情(编辑页面)
const openCustomerDetailInEdit = (id: string | number) => {
  const routeUrl = `/crm/bizMg/customer/?id=${id}&openEdit=true`;
  window.open(routeUrl, '_blank');
};

// 打开客户详情(查看页面)
const openCustomerDetailInView = (id: string | number) => {
  if (!id) return;
  const routeUrl = `/crm/bizMg/customer/?id=${id}&openView=true`;
  window.open(routeUrl, '_blank');
};

// 跟进对象变化
const handleActivityToChange = (value: string | number) => {
  if (form.value.activityTo === '2') {
    form.value.toId = form.value.customerId;
  }
};

// 外部跳转逻辑维护
const handleOpenDialog = () => {
  // 1. 打开跟进记录新增
  const customerId = route.query.customerId;
  const openAdd = route.query.openAdd;
  if (customerId && openAdd === 'true') {
    nextTick(() => {
      // 打开新增弹窗，并预填客户ID
      handleAdd();
      // 设置表单中的客户ID
      form.value.customerId = customerId as string;
      // 清除URL参数
      router.replace({ query: {} });
    });
  }
  // 2. 打开跟进记录详情(编辑页面)
  const id = route.query.id;
  const openEdit = route.query.openEdit;
  if (id && openEdit === 'true') {
    nextTick(() => {
      handleUpdate({ id: id } as ActivityVO);
      router.replace({ query: {} });
    });
  }

  // 3. 打开跟进记录详情(查看页面)
  const openView = route.query.openView;
  if (id && openView === 'true') {
    nextTick(() => {
      handleView({ id: id } as ActivityVO);
      router.replace({ query: {} });
    });
  }
};

onMounted(() => {
  getList();
  handleOpenDialog();
  loadUserList();
});
</script>
