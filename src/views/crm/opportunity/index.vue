<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px" class="search-form-container">
            <el-form-item label="机会名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入机会名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>

            <el-form-item label="客户档案" prop="customerId">
              <customer-select v-model="queryParams.customerId" placeholder="请选择客户档案" @change="handleQuery" />
            </el-form-item>

            <el-form-item label="销售场景" prop="saleScene">
              <dict-select
                v-model="queryParams.saleScene"
                dict-key="dict_crm_tag"
                placeholder="请选择销售场景"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>

            <el-form-item label="机会来源" prop="resourceKey">
              <dict-select
                v-model="queryParams.resourceKey"
                dict-key="dict_crm_resource"
                placeholder="请选择机会来源"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="采购用途" prop="demandPurpose">
              <dict-select
                v-model="queryParams.demandPurpose"
                dict-key="dict_demand_purpose"
                placeholder="请选择采购用途"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>

            <el-form-item label="意向产品" prop="productKey">
              <dict-select
                v-model="queryParams.productKey"
                dict-key="dict_crm_pro"
                placeholder="请选择意向产品"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>

            <el-form-item label="商机阶段" prop="phase">
              <dict-select
                v-model="queryParams.phase"
                dict-key="dict_opportunity_phase"
                placeholder="请选择商机阶段"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>

            <el-form-item label="输单原因" prop="loseReason" v-if="showMoreCondition">
              <dict-select
                v-model="queryParams.loseReason"
                dict-key="dict_lose_reason"
                placeholder="请选择输单原因"
                :show-footer="false"
                @change="handleQuery"
              />
            </el-form-item>

            <el-form-item label="预计成交日期" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeExpectDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                style="width: 240px"
                @change="handleQuery"
              />
            </el-form-item>

            <el-form-item label="机会编号" prop="code" v-if="showMoreCondition">
              <el-input v-model="queryParams.code" placeholder="请输入机会编号" clearable @keyup.enter="handleQuery" />
            </el-form-item>

            <el-form-item label="更新人" prop="updateBy" v-if="showMoreCondition">
              <el-select v-model="queryParams.updateBy" placeholder="请输入更新人" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>

            <el-form-item label="更新时间" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
                style="width: 240px"
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['crm:opportunity:add']">新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['crm:opportunity:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['crm:opportunity:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['crm:opportunity:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="opportunityList" @selection-change="handleSelectionChange" border>
        <!-- <el-table-column type="selection" width="55" /> -->
        <el-table-column label="#" type="index" align="center" fixed="left" />
        <el-table-column label="id" prop="id" v-if="false" />
        <!-- <el-table-column label="机会编号" prop="code" :min-width="140">
          <template #header>
            <span>
              机会编号
              <el-tooltip content="编号规则：SJ为固定字符，中间8位为创建日期，后3位是流水号" placement="top">
                <el-icon class="el-icon--right">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </span>
          </template>
          <template #default="scope">{{ scope.row.code }}</template>
        </el-table-column> -->
        <el-table-column label="机会名称" prop="name" :min-width="180" show-overflow-tooltip>
          <template #default="scope">
            <el-tooltip v-if="scope.row.saleStrategy" content="销售策略详情" placement="top">
              <el-icon @click="openLink(scope.row.saleStrategy)">
                <Link />
              </el-icon>
            </el-tooltip>
            <span style="padding-left: 10px">{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="客户档案" prop="customerId" :min-width="160" show-overflow-tooltip>
          <template #default="scope">
            <el-link type="primary" @click="openCustomerDetailInView(scope.row.customerId)">
              {{ scope.row.customer.name }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="销售场景" prop="saleScene" :min-width="150" show-overflow-tooltip>
          <template #default="scope">
            <dict-tag :options="dict_crm_tag" :value="scope.row.saleScene" />
          </template>
        </el-table-column>
        <el-table-column label="预计合同金额(元)" prop="expectAmount" :min-width="150">
          <template #default="scope">
            <span>{{ convertToWan(scope.row.expectAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="机会来源" prop="resourceKey" :min-width="120">
          <template #default="scope">
            <dict-tag :options="dict_crm_resource" :value="scope.row.resourceKey" />
          </template>
        </el-table-column>
        <el-table-column label="采购用途" prop="demandPurpose" :min-width="170">
          <template #default="scope">
            <dict-tag :options="dict_demand_purpose" :value="scope.row.demandPurpose" />
          </template>
        </el-table-column>
        <el-table-column label="意向产品" prop="productKey" :min-width="120">
          <template #default="scope">
            <dict-tag :options="dict_crm_pro" :value="scope.row.productKey" />
          </template>
        </el-table-column>
        <el-table-column label="商机阶段" prop="phase" :min-width="120">
          <template #default="scope">
            <dict-tag :options="dict_opportunity_phase" :value="scope.row.phase" />
          </template>
        </el-table-column>
        <el-table-column label="需求描述" prop="demandDesc" :min-width="200" show-overflow-tooltip />
        <el-table-column label="预计成交日期" prop="expectDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.expectDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="销售策略" prop="saleStrategy" :min-width="120" /> -->
        <el-table-column label="输单原因" prop="loseReason" :min-width="200">
          <template #default="scope">
            <dict-tag :options="dict_lose_reason" :value="scope.row.loseReason" />
          </template>
        </el-table-column>
        <el-table-column label="输单描述" prop="loseDesc" :min-width="150" show-overflow-tooltip />
        <el-table-column label="备注" prop="remark" :min-width="200" show-overflow-tooltip />
        <el-table-column label="更新人" prop="updateBy" :min-width="120">
          <template #default="scope">
            <span>{{ findUserName(scope.row.updateBy) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTime" width="180" :min-width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="150">
          <template #default="scope">
            <el-tooltip content="查看" placement="top">
              <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['crm:opportunity:view']"></el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['crm:opportunity:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top" v-if="canOperated(scope.row.createTime)">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['crm:opportunity:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改销售机会对话框 -->
    <el-dialog destroy-on-close :title="dialog.title" v-model="dialog.visible" width="80%" append-to-body @close="cancel">
      <el-row :gutter="10">
        <!-- 左侧锚点导航 -->
        <el-col :span="4">
          <el-anchor :current-anchor="currentAnchor">
            <el-anchor-link href="#basic" title="基础信息" />
            <el-anchor-link href="#contractor" title="承揽规则" />
            <el-anchor-link href="#loseReason" title="输单原因" v-if="form.phase === '20'" />
            <el-anchor-link href="#note" title="备注信息" />
          </el-anchor>
        </el-col>
        <!-- 表单内容 -->
        <el-col :span="20">
          <el-form ref="opportunityFormRef" :model="form" :rules="rules" label-width="70%" label-position="top">
            <div id="basic" class="form-section">
              <div class="section-title">
                <span class="title-text">基础信息</span>
              </div>
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-form-item label="客户档案" prop="customerId">
                    <template #label>
                      <span>
                        客户档案
                        <el-tooltip content="打开客户档案" placement="top" v-if="form.customerId">
                          <el-icon @click="openCustomerDetailInView(form.customerId)">
                            <Link />
                          </el-icon>
                        </el-tooltip>
                        <el-tooltip content="新增客户档案" placement="top" v-else>
                          <el-icon @click="openCustomerAddDialog()">
                            <Plus />
                          </el-icon>
                        </el-tooltip>
                      </span>
                    </template>
                    <customer-select v-model="form.customerId" :disabled="dialogEditStatus" @change="handleCustomerChange" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="机会名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入机会名称" :disabled="isView" maxlength="50" show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="销售场景" prop="saleScene">
                    <dict-select v-model="form.saleScene" dict-key="dict_crm_tag" placeholder="请选择销售场景" :disabled="isView" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="机会来源" prop="resourceKey">
                    <dict-select v-model="form.resourceKey" dict-key="dict_crm_resource" placeholder="请选择机会来源" :disabled="isView" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="采购用途" prop="demandPurpose">
                    <dict-select
                      v-model="form.demandPurpose"
                      dict-key="dict_demand_purpose"
                      placeholder="请选择采购用途"
                      multiple
                      :disabled="isView"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="意向产品" prop="productKey">
                    <dict-select
                      v-model="form.productKey"
                      dict-key="dict_crm_pro"
                      placeholder="请选择意向产品"
                      :disabled="isView"
                      multiple
                      collapse-tags
                      collapse-tags-tooltip
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="商机联系人" prop="contactId">
                    <template #label>
                      <span>
                        商机联系人
                        <el-tooltip content="添加联系人" placement="top">
                          <el-icon @click="handleAddContact">
                            <Plus />
                          </el-icon>
                        </el-tooltip>
                      </span>
                    </template>
                    <el-select
                      v-model="form.contactId"
                      placeholder="请选择商机联系人"
                      clearable
                      @change="handleQuery"
                      filterable
                      :loading="contactLoading"
                      :disabled="!form.customerId || isView"
                    >
                      <el-option v-for="item in contactOptions" :key="item.id" :label="item.name" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="商机阶段" prop="phase">
                    <template #label>
                      <span>
                        商机阶段
                        <el-tooltip content="了解更多" placement="top">
                          <!-- 链接为飞书文档，文档中介绍了商机阶段 -->
                          <el-icon @click="openLink('https://rcn3q3ujmqdd.feishu.cn/docx/B7aHd8SSooF7qcxmgcQctY7oneg?from=from_copylink')">
                            <Link />
                          </el-icon>
                        </el-tooltip>
                      </span>
                    </template>
                    <el-select v-model="form.phase" placeholder="请选择商机阶段" :disabled="isView" v-if="!dialogEditStatus">
                      <el-option v-for="item in dict_opportunity_phase" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                    <el-select v-model="form.phase" placeholder="请选择商机阶段" :disabled="isView" v-else>
                      <el-option v-for="item in filteredStatus" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="预计成交日期" prop="expectDate">
                    <el-date-picker
                      clearable
                      v-model="form.expectDate"
                      type="date"
                      value-format="YYYY-MM-DD"
                      placeholder="请选择预计成交日期"
                      style="width: 100%"
                      :disabled="isView"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="预计金额" prop="expectAmount">
                    <template #label>
                      <span> 预计金额 </span>
                      <span v-if="form.expectAmount" class="assist-text">（约：{{ convertToWan(form.expectAmount) }}）</span>
                    </template>
                    <el-input v-model="form.expectAmount" placeholder="请输入预计销售金额，整数即可" @input="handleAmountInput" :disabled="isView">
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="销售策略" prop="saleStrategy">
                    <template #label>
                      <span>
                        销售策略
                        <el-tooltip content="请在飞书中维护销售策略，然后将链接粘贴到此处" placement="top">
                          <el-icon class="el-icon--right">
                            <InfoFilled />
                          </el-icon>
                        </el-tooltip>
                        <el-tooltip v-if="form.saleStrategy" content="单击打开链接" placement="top">
                          <el-icon @click="openLink(form.saleStrategy)">
                            <Link />
                          </el-icon>
                        </el-tooltip>
                      </span>
                    </template>
                    <el-input v-model="form.saleStrategy" placeholder="请飞书文档链接" :disabled="isView" maxlength="500" show-word-limit />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="需求描述" prop="demandDesc">
                    <template #label>
                      <span>
                        需求描述
                        <el-tooltip content="描述决策链路" placement="top">
                          <el-icon class="el-icon--right">
                            <InfoFilled />
                          </el-icon>
                        </el-tooltip>
                      </span>
                    </template>
                    <el-input
                      v-model="form.demandDesc"
                      type="textarea"
                      placeholder="请输入内容，如决策链路等"
                      :rows="5"
                      :disabled="isView"
                      maxlength="500"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 承揽规则 -->
            <div id="contractor" class="form-section">
              <div class="section-title">
                <span class="title-text">承揽规则</span>
              </div>
              <el-row :gutter="10">
                <el-col :span="16">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-form-item label="承揽人类型" prop="contractorType">
                        <dict-select
                          v-model="form.contractorType"
                          dict-key="dict_contractor_type"
                          placeholder="请选择承揽人类型"
                          :show-footer="false"
                          :disabled="isView"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item v-if="form.contractorType === '1'" label="承揽人" prop="contractor">
                        <el-select v-model="form.contractor" placeholder="请选择承揽人" clearable @change="handleQuery" filterable :disabled="isView">
                          <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
                        </el-select>
                      </el-form-item>
                      <el-form-item v-else label="承揽人" prop="contractor">
                        <el-input v-model="form.contractor" placeholder="请输入承揽人" :disabled="isView" maxlength="20" show-word-limit />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="机会类型" prop="opportunityType">
                        <dict-select
                          v-model="form.opportunityType"
                          dict-key="dict_opportunity_type"
                          placeholder="请选择机会类型"
                          :show-footer="false"
                          :disabled="isView"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="机会范围" prop="opportunityScope">
                        <dict-select
                          v-model="form.opportunityScope"
                          dict-key="dict_opportunity_scope"
                          placeholder="请选择机会范围"
                          :show-footer="false"
                          :disabled="isView"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="承揽备注" prop="contractRemark">
                    <el-input
                      v-model="form.contractRemark"
                      type="textarea"
                      placeholder="请输入承揽备注"
                      :rows="5"
                      :disabled="isView"
                      maxlength="200"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 输单原因 -->
            <div id="loseReason" class="form-section" v-if="form.phase === '20'">
              <div class="section-title">
                <span class="title-text">输单原因</span>
              </div>
              <el-row :gutter="10">
                <el-col :span="8">
                  <el-form-item label="输单原因" prop="loseReason">
                    <el-select v-model="form.loseReason" placeholder="请选择输单原因" style="width: 100%" :disabled="isView">
                      <el-option v-for="dict in dict_lose_reason" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="输单描述" prop="loseDesc">
                    <el-input v-model="form.loseDesc" placeholder="请输入内容" :disabled="isView" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 备注信息 -->
            <div id="note" class="form-section">
              <div class="section-title">
                <span class="title-text">备注信息</span>
              </div>
              <el-row :gutter="10">
                <el-col :span="12">
                  <el-form-item label="备注" prop="remark">
                    <el-input
                      v-model="form.remark"
                      type="textarea"
                      placeholder="请输入内容"
                      :rows="5"
                      :disabled="isView"
                      maxlength="500"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="赢单附件" prop="files">
                    <file-upload v-model="form.files" :disabled="isView" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm" :disabled="isView">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Opportunity" lang="ts">
import { listOpportunity, getOpportunity, delOpportunity, addOpportunity, updateOpportunity } from '@/api/crm/opportunity';
import { OpportunityVO, OpportunityQuery, OpportunityForm } from '@/api/crm/opportunity/types';
import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
import { convertToWan } from '@/utils/convertToWan';
import { canOperated } from '@/utils/biz/canOperated';

import { useContactSelect } from '@/views/crm/contact/contactSelect';
const { contactOptions, contactLoading, loadContactList } = useContactSelect();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { dict_crm_resource, dict_demand_purpose, dict_opportunity_phase, dict_crm_pro, dict_lose_reason, dict_crm_tag } = toRefs<any>(
  proxy?.useDict('dict_crm_resource', 'dict_demand_purpose', 'dict_opportunity_phase', 'dict_crm_pro', 'dict_lose_reason', 'dict_crm_tag')
);

// 状态机选择组件的支撑
import { useNextStatusList } from '@/views/org/stateMachine/detail/nextStatusList';
const { nextStatusList, nextStatusLoading, filteredStatus, gainNextStatusList } = useNextStatusList();

const opportunityList = ref<OpportunityVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeExpectDate = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑

const queryFormRef = ref<ElFormInstance>();
const opportunityFormRef = ref<ElFormInstance>();

// 定义当前锚点
const currentAnchor = ref('#basic');

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: OpportunityForm = {
  id: undefined,
  code: undefined,
  name: undefined,
  customerId: undefined,
  resourceKey: undefined,
  demandPurpose: undefined,
  productKey: undefined,
  phase: undefined,
  demandDesc: undefined,
  expectAmount: undefined,
  expectDate: undefined,
  saleStrategy: undefined,
  contractorType: '1',
  contractor: undefined,
  opportunityType: '1',
  opportunityScope: '1',
  loseReason: undefined,
  loseDesc: undefined,
  remark: undefined,
  files: undefined
};
const data = reactive<PageData<OpportunityForm, OpportunityQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    code: undefined,
    name: undefined,
    customerId: undefined,
    resourceKey: undefined,
    demandPurpose: undefined,
    productKey: undefined,
    phase: undefined,
    loseReason: undefined,
    updateBy: undefined,
    updateTime: undefined,
    params: {
      expectDate: undefined,
      updateDate: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    name: [{ required: true, message: '机会名称不能为空', trigger: 'blur' }],
    customerId: [{ required: true, message: '客户档案不能为空', trigger: 'change' }],
    saleScene: [{ required: true, message: '销售场景不能为空', trigger: 'change' }],
    resourceKey: [{ required: true, message: '商机来源不能为空', trigger: 'change' }],
    demandPurpose: [{ required: true, message: '采购用途不能为空', trigger: 'change' }],
    productKey: [{ required: true, message: '意向产品不能为空', trigger: 'change' }],
    phase: [{ required: true, message: '商机阶段不能为空', trigger: 'change' }],
    demandDesc: [{ required: true, message: '需求描述不能为空', trigger: 'blur' }],
    expectAmount: [
      {
        validator: (rule: any, value: any, callback: (error?: Error) => void) => {
          if (value === undefined || value === null || value === '') {
            callback();
            return;
          }
          const numValue = Number(value);
          if (isNaN(numValue) || !Number.isInteger(numValue)) {
            callback(new Error('预计金额必须为整数'));
            return;
          }
          if (numValue <= 0) {
            callback(new Error('预计金额必须大于0'));
            return;
          }
          if (numValue > 999999999) {
            callback(new Error('预计金额不能超过99999.9999万'));
            return;
          }
          callback();
        },
        trigger: 'blur'
      }
    ],
    expectDate: [{ required: true, message: '预计成交日期不能为空', trigger: 'blur' }],
    saleStrategy: [
      {
        validator: (rule: any, value: string, callback: (error?: Error) => void) => {
          if (!value) {
            callback();
            return;
          }
          if (!value.startsWith('https://')) {
            callback(new Error('销售策略，填写在线文档链接必须以 https:// 开头'));
            return;
          }
          callback();
        },
        trigger: 'blur'
      }
    ],
    contractorType: [{ required: true, message: '承揽人类型不能为空', trigger: 'change' }],
    contractor: [{ required: true, message: '承揽人不能为空', trigger: 'change' }],
    opportunityScope: [{ required: true, message: '机会范围不能为空', trigger: 'change' }],
    opportunityType: [{ required: true, message: '机会类型不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

const { findUserName, loadUserList, userOptions } = useSysUserSelect();

// 获取路由实例
const route = useRoute();
const router = useRouter();

/** 查询销售机会列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeExpectDate.value, 'ExpectDate');
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listOpportunity(queryParams.value);
  opportunityList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  isView.value = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  opportunityFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeExpectDate.value = ['', ''];
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: OpportunityVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加销售机会';
  dialogEditStatus.value = false;
};

/** 修改按钮操作 */
const handleUpdate = async (row?: OpportunityVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getOpportunity(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改销售机会' + '(' + form.value.code + ')';
  dialogEditStatus.value = true;
  loadContactList(form.value.customerId);

  // 客户状态的状态机相关
  const currentStatus = form.value.phase;
  gainNextStatusList('dict_opportunity_phase', dict_opportunity_phase.value, currentStatus);
};

/** 提交按钮 */
const submitForm = () => {
  opportunityFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateOpportunity(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addOpportunity(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 查看按钮操作 */
const isView = ref(false); // 是否是查看模式, 查看模式下，表单组件处于禁用状态
const handleView = async (row?: OpportunityVO) => {
  reset();
  const res = await getOpportunity(row?.id);
  Object.assign(form.value, res.data);
  isView.value = true;
  dialog.visible = true;
  dialog.title = '查看销售机会' + '(' + form.value.code + ')';
  dialogEditStatus.value = true;
  loadContactList(form.value.customerId);
};

/** 删除按钮操作 */
const handleDelete = async (row?: OpportunityVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除销售机会编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delOpportunity(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'crm/opportunity/export',
    {
      ...queryParams.value
    },
    `opportunity_${new Date().getTime()}.xlsx`
  );
};

const openLink = (url: string) => {
  window.open(url, '_blank');
};

// 打开客户详情(编辑页面)
const openCustomerDetailInEdit = (id: string | number) => {
  const routeUrl = `/crm/bizMg/customer/?id=${id}&openEdit=true`;
  window.open(routeUrl, '_blank');
};

// 打开客户详情(查看页面)
const openCustomerDetailInView = (id: string | number) => {
  const routeUrl = `/crm/bizMg/customer/?id=${id}&openView=true`;
  window.open(routeUrl, '_blank');
};

// 打开客户弹窗
const openCustomerAddDialog = () => {
  const routeUrl = `/crm/bizMg/customer/?openAdd=true`;
  window.open(routeUrl, '_blank');
};

const handleAmountInput = (value: string) => {
  // 移除非数字字符
  let newValue = value.replace(/[^\d]/g, '');

  // 移除前导零
  newValue = newValue.replace(/^0+/, '');

  // 如果为空则设为undefined
  if (!newValue) {
    form.value.expectAmount = undefined;
    return;
  }

  // 转换为数字并检查范围
  const numValue = parseInt(newValue);
  if (numValue > 99999999) {
    form.value.expectAmount = 99999999;
  } else {
    form.value.expectAmount = numValue;
  }
};

// 客户选择变化
const handleCustomerChange = (value: string | number) => {
  loadContactList(value);
};

// 打开联系人新增
const handleAddContact = () => {
  // 确保当前客户已保存且有ID
  if (!form.value.customerId) {
    proxy?.$modal.msgError('请先保存客户信息');
    return;
  }

  // 打开联系人新增页面
  const routeUrl = `/crm/bizMg/contact?customerId=${form.value.customerId}&openAdd=true`;
  window.open(routeUrl, '_blank');
};

// 外部跳转逻辑维护
const handleOpenDialog = () => {
  // 1. 打开销售机会新增
  const customerId = route.query.customerId;
  const openAdd = route.query.openAdd;
  if (customerId && openAdd === 'true') {
    nextTick(() => {
      // 打开新增弹窗，并预填客户ID
      handleAdd();
      // 设置表单中的客户ID
      form.value.customerId = customerId as string;
      // 清除URL参数
      router.replace({ query: {} });
    });
  }
  // 2. 打开销售机会详情
  const opportunityId = route.query.id;
  const openEdit = route.query.openEdit;
  if (opportunityId && openEdit === 'true') {
    nextTick(() => {
      handleUpdate({ id: opportunityId } as OpportunityVO);
      router.replace({ query: {} });
    });
  }
  // 3. 打开销售机会详情(查看页面)
  const openView = route.query.openView;
  if (opportunityId && openView === 'true') {
    nextTick(() => {
      handleView({ id: opportunityId } as OpportunityVO);
      router.replace({ query: {} });
    });
  }
};

onMounted(() => {
  getList();
  handleOpenDialog();
  loadUserList();
});
</script>

<style scoped>
@import '@/assets/styles/anchorform.scss';
</style>
