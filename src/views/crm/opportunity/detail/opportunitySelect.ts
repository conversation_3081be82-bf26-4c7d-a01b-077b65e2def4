// 销售机会选择组件的支撑，可以传入客户id，则只查询该客户的销售机会
// 这里只适合小数据量的查询

import { ref, computed } from 'vue';
import { listOpportunity } from '@/api/crm/opportunity';
import type { OpportunityVO } from '@/api/crm/opportunity/types';

export const useOpportunitySelect = (props = { immediate: false }) => {
  const opportunityOptions = ref<OpportunityVO[]>([]);
  const opportunityLoading = ref(false);
  const searchKeyword = ref('');

  // 添加计算属性用于过滤选项
  const filteredOptions = computed(() => {
    if (!searchKeyword.value) return opportunityOptions.value;
    return opportunityOptions.value.filter((item) => item.name?.toLowerCase().includes(searchKeyword.value.toLowerCase()));
  });

  const loadOpportunityList = async (customerId?: string | number) => {
    try {
      opportunityLoading.value = true;
      const res = await listOpportunity({
        customerId: customerId,
        pageNum: 1,
        pageSize: 20
      });
      opportunityOptions.value = res.rows;
    } catch (error) {
      console.error('加载销售机会列表失败:', error);
      opportunityOptions.value = [];
    } finally {
      opportunityLoading.value = false;
    }
  };

  // 如果需要立即加载数据
  if (props.immediate) {
    loadOpportunityList();
  }

  return {
    opportunityOptions,
    filteredOptions,
    opportunityLoading,
    searchKeyword,
    loadOpportunityList
  };
};
