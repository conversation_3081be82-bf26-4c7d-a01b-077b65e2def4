<template>
  <div class="container">
    <div class="editor-container">
      <Editor v-model="content" :height="400" :minHeight="300" :readOnly="false" :fileSize="5" />
    </div>
  </div>
</template>

<script setup lang="ts">
const content = ref('');
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.editor-container {
  width: 500px;
  height: 500px;
}
</style>
