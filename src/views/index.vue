<template>
  <div class="app-container home">
    <h2>百果园CSO系统</h2>
    <div class="mb-4">这个页面最终会被一个功能完善的工作台替代，目前的工作台是一个简单的页面，用于展示已完成的工作和未完成的工作。</div>
    <el-row :gutter="10">
      <el-col :span="9">
        <el-space direction="vertical" :size="10"> </el-space>
        <el-row>
          <el-col :span="24">
            <el-card shadow="hover">
              <template #header> 系统待办事项 （2025-03-04更新） </template>
              <li>订单归集-PC</li>
              <li>企业购商城重构1.0</li>
              <li>大仓和内购点库存管理</li>
              <li>对账和收款模块</li>
              <li>分销管理（实体好吃卡分销功能）</li>
              <li>接龙等分销功能</li>
              <li style="color: brown">随心兑 的商业开放</li>
              <li style="color: brown">及时达H5 的商业开放 （下周准备与会员电商团队沟通，确定研发资源）</li>
            </el-card>
          </el-col>
          <el-col :span="24" style="margin-top: 10px">
            <el-card shadow="hover">
              <template #header> 处理中 </template>
              <li>产品和方案管理</li>
              <li style="color: red">果礼卡小程序 （2月28日开始整体需求设计）,在等小程序备案，4月准备上线</li>
              <li>百果臻选（定制商城小程序）于4月13日进入产品设计阶段</li>
              <li style="color: red">三方支付 的商业开放 （3月24日百果科技产品宣讲）</li>
            </el-card>
          </el-col>
          <el-col :span="24" style="margin-top: 10px">
            <el-card shadow="hover">
              <template #header> 系统已办事项 </template>
              <li>客户关系管理 (2月27日上线)</li>
              <li>...</li>
            </el-card>
          </el-col>
          <el-col :span="24" style="margin-top: 10px">
            <el-card shadow="hover">
              <template #header> 我们将拥有的应用 </template>
              <li>
                <el-tag type="success">PC</el-tag>
                <span>CSO管理台</span>
              </li>
              <li>
                <el-tag type="primary">小程序</el-tag>
                <span>百果园果礼</span>
              </li>
              <li>
                <el-tag type="primary">小程序</el-tag>
                <span>百果臻选 定制商城小程序（一个客户一品一价）</span>
              </li>
              <li>
                <el-tag type="primary">小程序</el-tag>
                <span>企业购商城：一套商品统一价格</span>
              </li>
              <li>
                <el-tag type="primary">小程序</el-tag>
                <span>业务员助手</span>
              </li>
              <li>
                <el-tag type="primary">小程序</el-tag>
                <span>接龙小程序</span>
              </li>
              <li>
                <el-tag type="primary">小程序</el-tag>
                <span>分销小程序</span>
              </li>
            </el-card>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div style="display: flex; align-items: center; justify-content: space-between">
              <div style="display: flex; align-items: center; justify-content: left">
                <span>帮助文档 </span>
                <el-tooltip content="刷新" placement="top">
                  <el-button link plain icon="Refresh" class="ml-2" @click="refreshHelpDocList"></el-button>
                </el-tooltip>
              </div>
              <el-select v-model="helpDocModule" placeholder="请选择模块" clearable @change="loadHelpDocList(helpDocModule)" style="width: 150px">
                <el-option v-for="item in dict_sys_module" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
          </template>
          <div v-for="item in helpDocList" :key="item.id" style="display: flex; align-items: center; justify-content: space-between; padding: 10px 0">
            <div style="display: flex; align-items: center; justify-content: left">
              <dict-tag :options="dict_sys_module" :value="item.moduleKey" />
              <el-link class="ml-2" @click="goTarget(item.url)">
                <el-text class="w-240px" truncated>{{ item.title }}</el-text>
              </el-link>
            </div>
            <!-- <span style="color: #999">{{ item.updateTime?.split(' ')[0] }}</span> -->
            <span style="color: #999">{{ proxy.parseTime(item.updateTime, '{m}-{d} {h}:{i}') }}</span>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Index" lang="ts">
import { useHelpDocSelect } from '@/views/org/helpDoc/docSelect';
const { helpDocList, loadHelpDocList } = useHelpDocSelect();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { dict_sys_module } = toRefs<any>(proxy?.useDict('dict_sys_module'));

const helpDocModule = ref<string>();

const goTarget = (url: string) => {
  window.open(url, '__blank');
};

// 刷新帮助文档列表
const refreshHelpDocList = () => {
  loadHelpDocList();
  helpDocModule.value = undefined;
};

onMounted(() => {
  loadHelpDocList();
});
</script>

<style scoped lang="scss">
.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }
  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }
  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: 'open sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }
}
</style>
