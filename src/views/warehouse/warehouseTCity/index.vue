<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="大仓" prop="warehouseId">
              <warehouse-select v-model="queryParams.warehouseId" placeholder="请选择大仓" @change="handleQuery" />
            </el-form-item>
            <!-- <el-form-item label="大仓" prop="warehouseId">
              <el-input v-model="queryParams.warehouseId" placeholder="请输入大仓" clearable @keyup.enter="handleQuery" />
            </el-form-item> -->
            <el-form-item label="省份" prop="provinceCode">
              <!-- <el-input v-model="queryParams.provinceCode" placeholder="请输入省份" clearable @keyup.enter="handleQuery" /> -->
              <province-select
                v-model="queryParams.provinceCode"
                :provinceCode="queryParams.provinceCode"
                placeholder="请选择省份"
                @change="handleProvinceChangeInQuery"
              />
            </el-form-item>
            <el-form-item label="城市" prop="cityCode">
              <!-- <el-input
                v-model="queryParams.cityCode"
                placeholder="请输入城市"
                clearable
                @keyup.enter="handleQuery"
                :disabled="queryParams.provinceCode"
              /> -->
              <city-select
                ref="citySelectRef"
                v-model="queryParams.cityCode"
                placeholder="请选择城市"
                @change="handleQuery"
                :provinceCode="queryParams.provinceCode"
              />
            </el-form-item>
            <el-form-item label="更新人" prop="updateBy" v-if="showMoreCondition">
              <el-input v-model="queryParams.updateBy" placeholder="请输入更新人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['warehouse:warehouseTCity:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['warehouse:warehouseTCity:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['warehouse:warehouseTCity:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['warehouse:warehouseTCity:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="warehouseTCityList" @selection-change="handleSelectionChange" border>
        <el-table-column type="index" width="55" label="序号" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="大仓" prop="warehouseName" min-width="200" />
        <el-table-column label="省份" prop="provinceName" min-width="200" />
        <!-- <el-table-column label="省份" prop="provinceCode" min-width="200" /> -->
        <el-table-column label="城市" prop="cityName" min-width="200" />
        <el-table-column label="组织" prop="createOrgName" min-width="200" />
        <el-table-column label="备注" prop="remark" min-width="200" show-overflow-tooltip />
        <el-table-column label="更新人" prop="updateNickName" min-width="150" />
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" width="150" fixed="right">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['warehouse:warehouseTCity:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['warehouse:warehouseTCity:remove']"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改大仓发货城市对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="warehouseTCityFormRef" :model="form" :rules="rules" label-width="80px" label-position="top">
        <el-form-item label="大仓" prop="warehouseId">
          <warehouse-select v-model="form.warehouseId" placeholder="请选择大仓" />
        </el-form-item>
        <el-form-item label="发货城市" required>
          <el-cascader
            v-model="citySelectedOptions"
            :options="cityOptions"
            placeholder="请选择省份"
            style="width: 100%"
            filterable
            @change="handleCityChange"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" maxlength="200" show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="WarehouseTCity" lang="ts">
import { listWarehouseTCity, getWarehouseTCity, delWarehouseTCity, addWarehouseTCity, updateWarehouseTCity } from '@/api/warehouse/warehouseTCity';
import { WarehouseTCityVO, WarehouseTCityQuery, WarehouseTCityForm } from '@/api/warehouse/warehouseTCity/types';

import { provinceAndCityData, regionData } from 'element-china-area-data';
import type { CascaderOption } from 'element-plus';

// 类型断言
// const cityOptions = provinceAndCityData as unknown as CascaderOption[];
const regionOptions = regionData as unknown as CascaderOption[];

// 将regionOptions中的第3级(县级)数据剔除
const cityOptions = regionOptions.map((province) => {
  return {
    ...province,
    children: province.children.map((city) => {
      return {
        value: city.value,
        label: city.label
      };
    })
  };
});

const citySelectedOptions = ref<(string | number)[]>([]);

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const warehouseTCityList = ref<WarehouseTCityVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑

const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const warehouseTCityFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: WarehouseTCityForm = {
  id: undefined,
  warehouseId: undefined,
  provinceCode: undefined,
  cityCode: undefined,
  remark: undefined
};
const data = reactive<PageData<WarehouseTCityForm, WarehouseTCityQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    warehouseId: undefined,
    provinceCode: undefined,
    cityCode: undefined,
    updateBy: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    warehouseId: [{ required: true, message: '大仓不能为空', trigger: 'blur' }],
    provinceCode: [{ required: true, message: '省份不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 添加 ref 定义
const citySelectRef = ref();

/** 查询大仓发货城市列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listWarehouseTCity(queryParams.value);
  warehouseTCityList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  warehouseTCityFormRef.value?.resetFields();
  citySelectedOptions.value = [];
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: WarehouseTCityVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加大仓发货城市';
  // console.log('cityOptions', cityOptions);
};

/** 修改按钮操作 */
const handleUpdate = async (row?: WarehouseTCityVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getWarehouseTCity(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改大仓发货城市';

  // 组装省市信息
  citySelectedOptions.value = [form.value.provinceCode, form.value.cityCode];
};

/** 提交按钮 */
const submitForm = () => {
  warehouseTCityFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateWarehouseTCity(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addWarehouseTCity(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: WarehouseTCityVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除大仓发货城市编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delWarehouseTCity(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'warehouse/warehouseTCity/export',
    {
      ...queryParams.value
    },
    `warehouseTCity_${new Date().getTime()}.xlsx`
  );
};

// 筛选面板中，处理省份选择变化
const handleProvinceChangeInQuery = () => {
  handleQuery();
  // 调用citySelect的remoteCityListMethod
  citySelectRef.value?.remoteCityListMethod('', '', '');
};

// 筛选面板中，处理城市选择变化
const handleCityChange = (data) => {
  form.value.provinceCode = data[0];
  form.value.cityCode = data[1];
  console.log(data);
  console.log('form.value', form.value);
};

// 获取路由实例
const route = useRoute();
const router = useRouter();
// 外部跳转逻辑维护
const handleOpenDialog = async () => {
  // 1. 打开大仓发货城市新增
  const warehouseId = route.query.warehouseId;
  const openAdd = route.query.openAdd;
  if (warehouseId && openAdd === 'true') {
    nextTick(() => {
      handleAdd();
      // 设置表单中的大仓ID
      form.value.warehouseId = warehouseId as string;
      // 清除URL参数
      router.replace({ query: {} });
    });
  }

  // 2. 仅查询大仓发货城市
  const queryWarehouseId = route.query.queryWarehouseId;
  if (queryWarehouseId) {
    // 移除 nextTick，直接设置和查询
    queryParams.value.warehouseId = queryWarehouseId as string;
    await handleQuery(); // 确保查询执行完成
    // 不要立即清除 URL 参数，等查询完成后再清除
    router.replace({ query: {} });
  }
};

onMounted(async () => {
  // 先执行 handleOpenDialog，等待其完成后再执行 getList
  await handleOpenDialog();
  if (!route.query.queryWarehouseId) {
    // 只有在没有指定查询参数时才执行默认的 getList
    await getList();
  }
});
</script>
