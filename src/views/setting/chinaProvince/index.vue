<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="省份编码" prop="code">
              <el-input v-model="queryParams.code" placeholder="请输入省份编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="省份名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入省份名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <!-- <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['setting:chinaProvince:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['setting:chinaProvince:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['setting:chinaProvince:remove']"
              >删除</el-button
            >
          </el-col> -->
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['setting:chinaProvince:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="chinaProvinceList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="省份编码" prop="code" />
        <el-table-column label="省份名称" prop="name" width="250px" />
        <el-table-column label="备注" prop="remark" />
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="150px">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['setting:chinaProvince:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['setting:chinaProvince:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改省份对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="chinaProvinceFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="省份编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入省份编码" :disabled="dialogEdit" />
        </el-form-item>
        <el-form-item label="省份名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入省份名称" :disabled="dialogEdit" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ChinaProvince" lang="ts">
import { listChinaProvince, getChinaProvince, delChinaProvince, addChinaProvince, updateChinaProvince } from '@/api/setting/chinaProvince';
import { ChinaProvinceVO, ChinaProvinceQuery, ChinaProvinceForm } from '@/api/setting/chinaProvince/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const chinaProvinceList = ref<ChinaProvinceVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const dialogEdit = ref(true); // 是否是编辑状态,true为编辑，false为新增

const queryFormRef = ref<ElFormInstance>();
const chinaProvinceFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ChinaProvinceForm = {
  id: undefined,
  code: undefined,
  name: undefined,
  remark: undefined
};
const data = reactive<PageData<ChinaProvinceForm, ChinaProvinceQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    code: undefined,
    name: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    code: [{ required: true, message: '省份编码不能为空', trigger: 'blur' }],
    name: [{ required: true, message: '省份名称不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询省份列表 */
const getList = async () => {
  loading.value = true;
  const res = await listChinaProvince(queryParams.value);
  chinaProvinceList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  chinaProvinceFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ChinaProvinceVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加省份';
  dialogEdit.value = false;
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ChinaProvinceVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getChinaProvince(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改省份';
  dialogEdit.value = true;
};

/** 提交按钮 */
const submitForm = () => {
  chinaProvinceFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateChinaProvince(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addChinaProvince(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ChinaProvinceVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除省份编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delChinaProvince(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'setting/chinaProvince/export',
    {
      ...queryParams.value
    },
    `chinaProvince_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
