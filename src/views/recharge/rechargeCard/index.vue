<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="批次号" prop="batchNumber">
              <el-input v-model="queryParams.batchNumber" placeholder="请输入批次号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="开始流水号" prop="cardNoStart">
              <el-input v-model="queryParams.cardNoStart" placeholder="请输入开始充值码流水号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="结束流水号" prop="cardNoEnd">
              <el-input v-model="queryParams.cardNoEnd" placeholder="请输入结束充值码流水号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="充值码" prop="cardCode">
              <el-input v-model="queryParams.cardCode" placeholder="请输入充值码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="销售状态" prop="saleStatus">
              <el-select v-model="queryParams.saleStatus" placeholder="请选择销售状态" clearable @change="handleQuery">
                <el-option v-for="dict in mall_card_sale_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="售码订单id" prop="saleOrderId" v-if="showMoreCondition">
              <el-input v-model="queryParams.saleOrderId" placeholder="请输入售码订单id" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="售码时间" style="width: 308px" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeSaleTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="可用状态" prop="availableStatus">
              <el-select v-model="queryParams.availableStatus" placeholder="请选择可用状态" clearable @change="handleQuery">
                <el-option v-for="dict in mall_card_available_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="开码时间" style="width: 308px" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeIssueTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="使用状态" prop="usageStatus">
              <el-select v-model="queryParams.usageStatus" placeholder="请选择使用状态" clearable @change="handleQuery">
                <el-option v-for="dict in mall_recharge_usage_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="兑换时间" style="width: 308px" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeRechargeTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="钱包手机" prop="rechargePhone" v-if="showMoreCondition">
              <el-input v-model="queryParams.rechargePhone" placeholder="请输入钱包手机" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="作废时间" style="width: 308px" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeVoidTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="生效时间" style="width: 308px" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeEffectiveTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="失效时间" style="width: 308px" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeExpireTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="更新者" prop="updateBy" v-if="showMoreCondition">
              <el-input v-model="queryParams.updateBy" placeholder="请输入更新者" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-tooltip content="不含卡密" placement="top">
              <el-button type="warning" plain icon="Download" @click="handleCardExport" v-hasPermi="['recharge:rechargeCard:export']"
                >导出卡号</el-button
              >
            </el-tooltip>
          </el-col>
          <el-col :span="1.5">
            <el-tooltip content="仅卡密" placement="top">
              <el-button type="warning" plain icon="Download" @click="handleSecretExport" v-hasPermi="['recharge:rechargeCard:exportFull']"
                >导出卡密</el-button
              >
            </el-tooltip>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="Calendar"
              @click="handleAdjustExpireDateBatch"
              :disabled="multiple"
              v-hasPermi="['recharge:rechargeCard:adjust']"
            >
              批量延期
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Lock" @click="handleLossBatch" :disabled="multiple" v-hasPermi="['recharge:rechargeCard:loss']"
              >挂失充值码</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="Unlock"
              @click="handleUnLossBatch"
              :disabled="multiple"
              v-hasPermi="['recharge:rechargeCard:unLoss']"
              >解挂充值码</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" @click="handleVoidBatch" :disabled="multiple" v-hasPermi="['recharge:rechargeCard:void']"
              >作废充值码</el-button
            >
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="rechargeCardList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="#" width="55" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="流水号" prop="cardNo" width="180">
          <template #header>
            <span>流水号</span>
            <el-tooltip content="流水号=批次号+6位自增数值构成，即前8位为批次号;列表是从大到小排序。" placement="top">
              <el-icon class="el-icon--right">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </template>
          <template #default="scope">
            {{ scope.row.cardNo }}
          </template>
        </el-table-column>
        <el-table-column label="充值码" prop="cardCode" width="180" />
        <el-table-column label="码密" prop="cardSecret" />
        <el-table-column label="面值(元)" prop="faceValue" width="100">
          <template #default="scope">
            <span>{{ centToYuan(scope.row.faceValue) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="销售状态" prop="saleStatus">
          <template #default="scope">
            <dict-tag :options="mall_card_sale_status" :value="scope.row.saleStatus" />
          </template>
        </el-table-column>
        <el-table-column label="售码订单" prop="saleOrderId" width="200" />
        <el-table-column label="售码时间" prop="saleTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.saleTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="可用状态" prop="availableStatus">
          <template #default="scope">
            <dict-tag :options="mall_card_available_status" :value="scope.row.availableStatus" />
          </template>
        </el-table-column>
        <el-table-column label="开码时间" prop="issueTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.issueTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="使用状态" prop="usageStatus">
          <template #default="scope">
            <dict-tag :options="mall_recharge_usage_status" :value="scope.row.usageStatus" />
          </template>
        </el-table-column>
        <el-table-column label="兑换时间" prop="rechargeTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.rechargeTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="钱包手机" prop="rechargePhone" width="150" />
        <el-table-column label="作废时间" prop="voidTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.voidTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="生效日期" prop="effectiveTime" width="150">
          <template #default="scope">
            <span>{{ parseTime(scope.row.effectiveTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="失效日期" prop="expireTime" width="180">
          <template #default="scope">
            <div class="flex items-center relative group">
              <span>{{ parseTime(scope.row.expireTime, '{y}-{m}-{d}') }}</span>
              <el-button
                v-if="scope.row.availableStatus === '20' && scope.row.usageStatus !== '91' && scope.row.usageStatus !== '92'"
                class="opacity-0 group-hover:opacity-100 ml-2 absolute right-0"
                link
                type="primary"
                icon="Edit"
                @click.stop="handleAdjustExpireDate(scope.row)"
                v-hasPermi="['recharge:rechargeCard:adjust']"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="250" show-overflow-tooltip />
        <el-table-column label="批次号" prop="batchNumber" width="120">
          <template #default="scope">{{ scope.row.batchNumber }} </template>
        </el-table-column>
        <el-table-column label="更新者" prop="updateNickName" width="150" show-overflow-tooltip />
        <el-table-column label="更新时间" prop="updateTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="180">
          <template #default="scope">
            <el-tooltip
              content="延期"
              v-if="scope.row.availableStatus === '20' && scope.row.usageStatus !== '91' && scope.row.usageStatus !== '92'"
              placement="top"
              v-hasPermi="['recharge:rechargeCard:adjust']"
            >
              <el-button link type="primary" icon="Calendar" @click="handleAdjustExpireDate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip
              content="挂失"
              v-if="scope.row.availableStatus === '20' && scope.row.usageStatus !== '91' && scope.row.usageStatus !== '92'"
              placement="top"
              v-hasPermi="['recharge:rechargeCard:loss']"
            >
              <el-button link type="primary" icon="Lock" @click="handleLoss(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip
              content="解挂"
              v-if="scope.row.availableStatus === '30' && scope.row.usageStatus !== '91' && scope.row.usageStatus !== '92'"
              placement="top"
              v-hasPermi="['recharge:rechargeCard:unLoss']"
            >
              <el-button link type="primary" icon="Unlock" @click="handleUnLoss(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip v-if="scope.row.usageStatus === '11'" content="作废" placement="top" v-hasPermi="['recharge:rechargeCard:void']">
              <el-button link type="primary" icon="Delete" @click="handleVoid(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 添加备注对话框 -->
    <el-dialog
      :title="
        currentOperation === 'loss'
          ? currentRechargeCard && currentIds.length === 1
            ? `挂失充值码 ${currentRechargeCard.cardNo}`
            : '挂失充值码'
          : currentOperation === 'unLoss'
            ? currentRechargeCard && currentIds.length === 1
              ? `解挂充值码 ${currentRechargeCard.cardNo}`
              : '解挂充值码'
            : currentRechargeCard && currentIds.length === 1
              ? `作废充值码 ${currentRechargeCard.cardNo}`
              : '作废充值码'
      "
      v-model="remarkDialogVisible"
      width="500px"
      append-to-body
      draggable
    >
      <div class="dialog-body-text">
        {{
          currentOperation === 'loss'
            ? '挂失后，相关的充值码将不能使用。'
            : currentOperation === 'unLoss'
              ? '解挂后，相关的充值码将恢复可使用状态。'
              : '作废后，相关的充值码将不能使用，且不能恢复，请输入作废原因。'
        }}
      </div>
      <div v-if="currentRechargeCard && currentIds.length === 1" class="card-info mb-3">
        <div class="info-item">
          <span class="label">流水号：</span>
          <span class="value">{{ currentRechargeCard.cardNo }}</span>
        </div>
        <div class="info-item">
          <span class="label">充值码：</span>
          <span class="value">{{ currentRechargeCard.cardCode }}</span>
        </div>
        <div class="info-item">
          <span class="label">面值：</span>
          <span class="value">{{ centToYuan(currentRechargeCard.faceValue) }}元</span>
        </div>
      </div>
      <div v-else-if="currentIds.length > 1" class="mb-3">
        已选择 <span class="text-primary font-bold">{{ currentIds.length }}</span> 条记录
      </div>
      <el-form>
        <el-form-item label="备注" :required="currentOperation === 'void' || currentOperation === 'loss' || currentOperation === 'unLoss'">
          <el-input
            v-model="remarkValue"
            type="textarea"
            :placeholder="currentOperation === 'void' ? '请输入作废原因' : '请输入备注信息'"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="remarkDialogVisible = false">取消</el-button>
          <el-button :loading="buttonLoading" :type="currentOperation === 'void' ? 'danger' : 'primary'" @click="submitRemark">{{
            currentOperation === 'void' ? '确定作废' : '确定'
          }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加或修改充值码对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body @close="cancel" draggable>
      <el-form ref="rechargeCardFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="批次号" prop="batchNumber">
          <el-input v-model="form.batchNumber" placeholder="请输入批次号" />
        </el-form-item>
        <el-form-item label="充值码流水号，批次+6位自增" prop="cardNo">
          <el-input v-model="form.cardNo" placeholder="请输入充值码流水号，批次+6位自增" />
        </el-form-item>
        <el-form-item label="充值码" prop="cardCode">
          <el-input v-model="form.cardCode" placeholder="请输入充值码" />
        </el-form-item>
        <el-form-item label="面值" prop="faceValue">
          <el-input v-model="form.faceValue" placeholder="请输入面值" />
        </el-form-item>
        <el-form-item label="销售状态" prop="saleStatus">
          <el-select v-model="form.saleStatus" placeholder="请选择销售状态">
            <el-option v-for="dict in mall_card_sale_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="售码订单id" prop="saleOrderId">
          <el-input v-model="form.saleOrderId" placeholder="请输入售码订单id" />
        </el-form-item>
        <el-form-item label="售码时间" prop="saleTime">
          <el-date-picker clearable v-model="form.saleTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择售码时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="可用状态" prop="availableStatus">
          <el-select v-model="form.availableStatus" placeholder="请选择可用状态">
            <el-option v-for="dict in mall_card_available_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开码时间" prop="issueTime">
          <el-date-picker clearable v-model="form.issueTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择开码时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="使用状态，关联字典「mall_recharge_usage_status」" prop="usageStatus">
          <el-select v-model="form.usageStatus" placeholder="请选择使用状态，关联字典「mall_recharge_usage_status」">
            <el-option v-for="dict in mall_recharge_usage_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="兑换时间" prop="rechargeTime">
          <el-date-picker clearable v-model="form.rechargeTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择兑换时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="钱包手机" prop="rechargePhone">
          <el-input v-model="form.rechargePhone" placeholder="请输入钱包手机" />
        </el-form-item>
        <el-form-item label="作废时间" prop="voidTime">
          <el-date-picker clearable v-model="form.voidTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择作废时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="生效时间" prop="effectiveTime">
          <el-date-picker clearable v-model="form.effectiveTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择生效时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="失效时间" prop="expireTime">
          <el-date-picker clearable v-model="form.expireTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择失效时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加延期对话框 -->
    <!-- 单个调整失效日期对话框 -->
    <el-dialog v-model="adjustExpireDateDialogVisible" title="延期" width="500px" append-to-body>
      <div class="dialog-body-text">请设置新的失效日期，调整后将覆盖原有失效日期。</div>
      <el-form>
        <el-form-item label="充值码" v-if="currentRechargeCard">
          <span>{{ currentRechargeCard.cardNo }}</span>
        </el-form-item>
        <el-form-item label="当前失效日期" v-if="currentRechargeCard">
          <span>{{ parseTime(currentRechargeCard.expireTime, '{y}-{m}-{d}') }}</span>
        </el-form-item>
        <div v-else-if="currentIds.length > 1" class="mb-3">
          已选择 <span class="text-primary font-bold">{{ currentIds.length }}</span> 条记录
        </div>
        <el-form-item label="新失效日期" required>
          <el-date-picker
            v-model="newExpireDate"
            type="date"
            placeholder="选择日期"
            value-format="YYYY-MM-DD"
            :disabledDate="disabledDate"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="日期变化" v-if="currentRechargeCard && dateChangeDays !== 0">
          <span :class="dateChangeDays > 0 ? 'text-success' : 'text-danger'">
            {{ dateChangeDays > 0 ? '延后' : '提前' }} <strong>{{ Math.abs(dateChangeDays) }}</strong> 天
          </span>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="adjustExpireDateRemark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelAdjustExpireDate">取消</el-button>
          <el-button :loading="buttonLoading" type="primary" @click="confirmAdjustExpireDate">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="RechargeCard" lang="ts">
import {
  listRechargeCard,
  getRechargeCard,
  delRechargeCard,
  addRechargeCard,
  updateRechargeCard,
  lossRechargeCard,
  unLossRechargeCard,
  voidRechargeCard,
  adjustRechargeCardExpireTime
} from '@/api/recharge/rechargeCard';
import { RechargeCardVO, RechargeCardQuery, RechargeCardForm } from '@/api/recharge/rechargeCard/types';
import { useRoute } from 'vue-router';
import { watch } from 'vue';
const route = useRoute();
const router = useRouter();

import { centToYuan } from '@/utils/moneyUtils';
import { parseTime } from '@/utils/ruoyi';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_card_sale_status, mall_recharge_usage_status, mall_card_available_status } = toRefs<any>(
  proxy?.useDict('mall_card_sale_status', 'mall_recharge_usage_status', 'mall_card_available_status')
);

const rechargeCardList = ref<RechargeCardVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑

// 备注对话框相关
const remarkDialogVisible = ref(false);
const remarkValue = ref('');
const currentOperation = ref('');
const currentIds = ref<Array<string | number>>([]);
const currentRechargeCard = ref<RechargeCardVO | null>(null);

// 延期对话框相关
const adjustExpireDateDialogVisible = ref(false);
const newExpireDate = ref('');
const adjustExpireDateRemark = ref('');
const dateChangeDays = ref(0); // 新增：日期变化的天数

const dateRangeSaleTime = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeIssueTime = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeRechargeTime = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeVoidTime = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeEffectiveTime = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeExpireTime = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const rechargeCardFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: RechargeCardForm = {
  id: undefined,
  batchNumber: undefined,
  cardNo: undefined,
  cardCode: undefined,
  faceValue: undefined,
  saleStatus: undefined,
  saleOrderId: undefined,
  saleTime: undefined,
  availableStatus: undefined,
  issueTime: undefined,
  usageStatus: undefined,
  rechargeTime: undefined,
  rechargePhone: undefined,
  voidTime: undefined,
  effectiveTime: undefined,
  expireTime: undefined,
  remark: undefined
};
const data = reactive<PageData<RechargeCardForm, RechargeCardQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    batchNumber: undefined,
    cardNoStart: undefined,
    cardNoEnd: undefined,
    cardCode: undefined,
    saleStatus: undefined,
    saleOrderId: undefined,
    availableStatus: undefined,
    usageStatus: undefined,
    rechargePhone: undefined,
    updateBy: undefined,
    params: {
      saleTime: undefined,
      issueTime: undefined,
      rechargeTime: undefined,
      voidTime: undefined,
      effectiveTime: undefined,
      expireTime: undefined,
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    faceValue: [{ required: true, message: '面值不能为空', trigger: 'blur' }],
    saleStatus: [{ required: true, message: '销售状态不能为空', trigger: 'change' }],
    availableStatus: [{ required: true, message: '可用状态不能为空', trigger: 'change' }],
    usageStatus: [{ required: true, message: '使用状态，关联字典「mall_recharge_usage_status」不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询充值码列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeSaleTime.value, 'SaleTime');
  proxy?.addDateRange(queryParams.value, dateRangeIssueTime.value, 'IssueTime');
  proxy?.addDateRange(queryParams.value, dateRangeRechargeTime.value, 'RechargeTime');
  proxy?.addDateRange(queryParams.value, dateRangeVoidTime.value, 'VoidTime');
  proxy?.addDateRange(queryParams.value, dateRangeEffectiveTime.value, 'EffectiveTime');
  proxy?.addDateRange(queryParams.value, dateRangeExpireTime.value, 'ExpireTime');
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listRechargeCard(queryParams.value);
  rechargeCardList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  rechargeCardFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeSaleTime.value = ['', ''];
  dateRangeIssueTime.value = ['', ''];
  dateRangeRechargeTime.value = ['', ''];
  dateRangeVoidTime.value = ['', ''];
  dateRangeEffectiveTime.value = ['', ''];
  dateRangeExpireTime.value = ['', ''];
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: RechargeCardVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加充值码';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: RechargeCardVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getRechargeCard(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改充值码';
};

/** 提交按钮 */
const submitForm = () => {
  rechargeCardFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateRechargeCard(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addRechargeCard(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: RechargeCardVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除充值码编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delRechargeCard(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出卡号按钮操作 */
const handleCardExport = () => {
  proxy?.download(
    'recharge/rechargeCard/export',
    {
      ...queryParams.value
    },
    `百果充值码_${new Date().getTime()}.xlsx`
  );
};

/** 导出卡密按钮操作 */
const handleSecretExport = () => {
  proxy?.download(
    'recharge/rechargeCard/exportFull',
    {
      ...queryParams.value
    },
    `百果充值码_${new Date().getTime()}.xlsx`
  );
};

/** 挂失充值码操作 */
const handleLoss = (row?: RechargeCardVO) => {
  const _ids = row?.id ? [row.id] : ids.value;
  if (!_ids.length) {
    proxy?.$modal.msgError('请选择至少一条记录');
    return;
  }

  // 单个操作，按钮已经通过v-if控制，直接处理
  if (row) {
    currentIds.value = _ids;
    currentOperation.value = 'loss';
    currentRechargeCard.value = row;
    remarkValue.value = row.remark || ''; // 设置当前卡片的备注
    remarkDialogVisible.value = true;
    return;
  }

  // 批量操作，进行过滤
  const validRecords = rechargeCardList.value.filter(
    (item) => _ids.includes(item.id) && item.availableStatus === '20' && item.usageStatus !== '91' && item.usageStatus !== '92'
  );

  if (validRecords.length === 0) {
    proxy?.$modal.msgError('所选记录中没有符合挂失条件的充值码');
    return;
  }

  if (validRecords.length < _ids.length) {
    proxy?.$modal.msgWarning(`已过滤不符合条件的记录，实际将处理 ${validRecords.length} 条记录`);
  }

  currentIds.value = validRecords.map((item) => item.id);
  currentOperation.value = 'loss';
  currentRechargeCard.value = null;
  remarkValue.value = ''; // 批量操作时清空备注
  remarkDialogVisible.value = true;
};

/** 解挂充值码操作 */
const handleUnLoss = (row?: RechargeCardVO) => {
  const _ids = row?.id ? [row.id] : ids.value;
  if (!_ids.length) {
    proxy?.$modal.msgError('请选择至少一条记录');
    return;
  }

  // 单个操作，按钮已经通过v-if控制，直接处理
  if (row) {
    currentIds.value = _ids;
    currentOperation.value = 'unLoss';
    currentRechargeCard.value = row;
    remarkValue.value = row.remark || ''; // 设置当前卡片的备注
    remarkDialogVisible.value = true;
    return;
  }

  // 批量操作，进行过滤
  const validRecords = rechargeCardList.value.filter(
    (item) => _ids.includes(item.id) && item.availableStatus === '30' && item.usageStatus !== '91' && item.usageStatus !== '92'
  );

  if (validRecords.length === 0) {
    proxy?.$modal.msgError('所选记录中没有符合解挂条件的充值码');
    return;
  }

  if (validRecords.length < _ids.length) {
    proxy?.$modal.msgWarning(`已过滤不符合条件的记录，实际将处理 ${validRecords.length} 条记录`);
  }

  currentIds.value = validRecords.map((item) => item.id);
  currentOperation.value = 'unLoss';
  currentRechargeCard.value = null;
  remarkValue.value = ''; // 批量操作时清空备注
  remarkDialogVisible.value = true;
};

/** 作废充值码操作 */
const handleVoid = (row?: RechargeCardVO) => {
  const _ids = row?.id ? [row.id] : ids.value;
  if (!_ids.length) {
    proxy?.$modal.msgError('请选择至少一条记录');
    return;
  }

  // 单个操作，按钮已经通过v-if控制，直接处理
  if (row) {
    currentIds.value = _ids;
    currentOperation.value = 'void';
    currentRechargeCard.value = row;
    remarkValue.value = ''; // 作废需要重新输入原因，不回显原备注
    remarkDialogVisible.value = true;
    return;
  }

  // 批量操作，进行过滤
  const validRecords = rechargeCardList.value.filter((item) => _ids.includes(item.id) && item.usageStatus === '11');

  if (validRecords.length === 0) {
    proxy?.$modal.msgError('所选记录中没有符合作废条件的充值码');
    return;
  }

  if (validRecords.length < _ids.length) {
    proxy?.$modal.msgWarning(`已过滤不符合条件的记录，实际将处理 ${validRecords.length} 条记录`);
  }

  currentIds.value = validRecords.map((item) => item.id);
  currentOperation.value = 'void';
  currentRechargeCard.value = null;
  remarkValue.value = ''; // 作废需要重新输入原因
  remarkDialogVisible.value = true;
};

/** 提交备注操作 */
const submitRemark = async () => {
  if (!remarkValue.value) {
    proxy?.$modal.msgError('请输入备注信息');
    return;
  }

  buttonLoading.value = true;
  try {
    if (currentOperation.value === 'loss') {
      await lossRechargeCard(currentIds.value, remarkValue.value);
      proxy?.$modal.msgSuccess('挂失成功');
    } else if (currentOperation.value === 'unLoss') {
      await unLossRechargeCard(currentIds.value, remarkValue.value);
      proxy?.$modal.msgSuccess('解挂成功');
    } else if (currentOperation.value === 'void') {
      await voidRechargeCard(currentIds.value, remarkValue.value);
      proxy?.$modal.msgSuccess('作废成功');
    }
    remarkDialogVisible.value = false;
    remarkValue.value = '';
    await getList();
  } catch (error) {
    console.error(error);
  } finally {
    buttonLoading.value = false;
  }
};

/** 监听路由参数 */
const listenRoute = () => {
  // 获取路由参数
  const batchNumber = route.query?.batchNumber;
  const saleOrderId = route.query?.orderId;
  if (batchNumber && saleOrderId) {
    queryParams.value.batchNumber = batchNumber as string;
    queryParams.value.saleOrderId = saleOrderId as string;
    router.replace({ query: {} });
  } else if (batchNumber) {
    queryParams.value.batchNumber = batchNumber as string;
    queryParams.value.saleStatus = '10'; // 未销售的dict key 为 "10"
    router.replace({ query: {} });
  } else if (saleOrderId) {
    queryParams.value.saleOrderId = saleOrderId as string;
    router.replace({ query: {} });
  }
};

/** 批量挂失充值码操作 */
const handleLossBatch = () => {
  if (ids.value.length === 0) {
    proxy?.$modal.msgError('请选择要挂失的充值码');
    return;
  }

  // 过滤出可用状态为正常(20)且非作废(91)且非已使用(92)的充值码
  const validRecords = rechargeCardList.value.filter(
    (item) => ids.value.includes(item.id) && item.availableStatus === '20' && item.usageStatus !== '91' && item.usageStatus !== '92'
  );

  if (validRecords.length === 0) {
    proxy?.$modal.msgError('所选记录中没有符合挂失条件的充值码');
    return;
  }

  if (validRecords.length < ids.value.length) {
    proxy?.$modal.msgWarning(`已选择${ids.value.length}条记录，其中${validRecords.length}条符合挂失条件`);
  }

  currentIds.value = validRecords.map((item) => item.id);
  currentOperation.value = 'loss';
  currentRechargeCard.value = null;
  remarkValue.value = ''; // 批量操作时清空备注
  remarkDialogVisible.value = true;
};

/** 批量解挂充值码操作 */
const handleUnLossBatch = () => {
  if (ids.value.length === 0) {
    proxy?.$modal.msgError('请选择要解挂的充值码');
    return;
  }

  // 过滤出可用状态为挂失(30)且非作废(91)且非已使用(92)的充值码
  const validRecords = rechargeCardList.value.filter(
    (item) => ids.value.includes(item.id) && item.availableStatus === '30' && item.usageStatus !== '91' && item.usageStatus !== '92'
  );

  if (validRecords.length === 0) {
    proxy?.$modal.msgError('所选记录中没有符合解挂条件的充值码');
    return;
  }

  if (validRecords.length < ids.value.length) {
    proxy?.$modal.msgError(`已选择${ids.value.length}条记录，其中${validRecords.length}条符合解挂条件`);
  }

  currentIds.value = validRecords.map((item) => item.id);
  currentOperation.value = 'unLoss';
  currentRechargeCard.value = null;
  remarkValue.value = ''; // 批量操作时清空备注
  remarkDialogVisible.value = true;
};

/** 批量作废充值码操作 */
const handleVoidBatch = () => {
  if (ids.value.length === 0) {
    proxy?.$modal.msgError('请选择要作废的充值码');
    return;
  }

  // 过滤出使用状态为未使用(11)的充值码
  const validRecords = rechargeCardList.value.filter((item) => ids.value.includes(item.id) && item.usageStatus === '11');

  if (validRecords.length === 0) {
    proxy?.$modal.msgError('所选记录中没有符合作废条件的充值码');
    return;
  }

  if (validRecords.length < ids.value.length) {
    proxy?.$modal.msgWarning(`已选择${ids.value.length}条记录，其中${validRecords.length}条符合作废条件`);
  }

  currentIds.value = validRecords.map((item) => item.id);
  currentOperation.value = 'void';
  currentRechargeCard.value = null;
  remarkValue.value = ''; // 作废需要重新输入原因
  remarkDialogVisible.value = true;
};

/** 批量延期操作 */
const handleAdjustExpireDateBatch = () => {
  if (ids.value.length === 0) {
    proxy?.$modal.msgError('请选择要延期的充值码');
    return;
  }

  // 过滤出可用状态为正常(20)且非作废(91)且非已使用(92)的充值码
  const validRecords = rechargeCardList.value.filter(
    (item) => ids.value.includes(item.id) && item.availableStatus === '20' && item.usageStatus !== '91' && item.usageStatus !== '92'
  );

  if (validRecords.length === 0) {
    proxy?.$modal.msgWarning('所选记录中没有符合延期条件的充值码');
    return;
  }

  if (validRecords.length < ids.value.length) {
    proxy?.$modal.msgWarning(`已选择${ids.value.length}条记录，其中${validRecords.length}条符合延期条件`);
  }

  currentIds.value = validRecords.map((item) => item.id);
  currentOperation.value = 'adjust';
  currentRechargeCard.value = null;
  adjustExpireDateDialogVisible.value = true;
};

/** 单个延期操作 */
const handleAdjustExpireDate = (row?: RechargeCardVO) => {
  if (!row) {
    proxy?.$modal.msgWarning('请选择要延期的充值码');
    return;
  }

  currentIds.value = [row.id];
  currentOperation.value = 'adjust';
  currentRechargeCard.value = row;

  // 设置默认的新失效日期为当前记录的失效日期
  if (row.expireTime) {
    newExpireDate.value = row.expireTime.substring(0, 10);
    // 初始化日期变化天数为0，因为初始值和当前值相同
    dateChangeDays.value = 0;
  } else {
    newExpireDate.value = '';
  }

  adjustExpireDateDialogVisible.value = true;
};

/** 禁用过去的日期 */
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 8.64e7; // 今天之前的日期不可选
};

/** 计算日期差异天数 */
const calculateDateDifference = (newDate: string, oldDate: string) => {
  if (!newDate || !oldDate) return 0;

  // 确保使用日期部分进行比较
  let date1, date2;

  // 处理可能的日期时间字符串，只保留日期部分
  if (typeof newDate === 'string') {
    date1 = new Date(newDate.substring(0, 10));
  } else {
    date1 = new Date(newDate);
  }

  if (typeof oldDate === 'string') {
    date2 = new Date(oldDate.substring(0, 10));
  } else {
    date2 = new Date(oldDate);
  }

  // 重置时间部分，只比较日期
  date1.setHours(0, 0, 0, 0);
  date2.setHours(0, 0, 0, 0);

  // 计算时间差（毫秒）
  const diffTime = date1.getTime() - date2.getTime();

  // 转换为天数（向下取整）
  return Math.floor(diffTime / (1000 * 60 * 60 * 24));
};

/** 监听新日期变化 */
watch(
  newExpireDate,
  (val) => {
    if (currentRechargeCard.value) {
      // 单个调整
      if (val && currentRechargeCard.value.expireTime) {
        dateChangeDays.value = calculateDateDifference(val, currentRechargeCard.value.expireTime);
      } else {
        dateChangeDays.value = 0;
      }
    }
  },
  { immediate: true }
);

/** 提交延期操作 */
const confirmAdjustExpireDate = async () => {
  if (!newExpireDate.value) {
    proxy?.$modal.msgError('请选择新的失效日期');
    return;
  }

  buttonLoading.value = true;
  try {
    await adjustRechargeCardExpireTime({
      ids: currentIds.value,
      expireTime: newExpireDate.value + ' 23:59:59',
      remark: adjustExpireDateRemark.value
    });
    proxy?.$modal.msgSuccess('延期成功');
    adjustExpireDateDialogVisible.value = false;
    newExpireDate.value = '';
    adjustExpireDateRemark.value = '';
    await getList();
  } catch (error) {
    console.error(error);
  } finally {
    buttonLoading.value = false;
  }
};

/** 取消延期操作 */
const cancelAdjustExpireDate = () => {
  adjustExpireDateDialogVisible.value = false;
  newExpireDate.value = '';
  adjustExpireDateRemark.value = '';
  dateChangeDays.value = 0;
};

onMounted(() => {
  listenRoute();
  getList();
});
</script>

<style scoped>
.dialog-body-text {
  margin-bottom: 20px;
  color: #666;
}
.card-info {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
}
.info-item {
  display: flex;
  margin-bottom: 8px;
}
.info-item:last-child {
  margin-bottom: 0;
}
.label {
  width: 70px;
  color: #606266;
  font-weight: 500;
}
.value {
  flex: 1;
  color: #333;
}
.text-primary {
  color: #409eff;
}
.font-bold {
  font-weight: bold;
}
.mb-3 {
  margin-bottom: 12px;
}
</style>
