<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="订单号" prop="orderNo">
              <el-input v-model="queryParams.orderNo" placeholder="请输入订单号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="订单类型" prop="orderType">
              <el-select v-model="queryParams.orderType" placeholder="请选择订单类型" clearable>
                <el-option v-for="dict in mall_card_order_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="订单用途" prop="purpose" v-if="showMoreCondition">
              <el-select v-model="queryParams.purpose" placeholder="请选择订单用途" clearable>
                <el-option v-for="dict in mall_sale_order_purpose" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>

            <el-form-item label="承做部门" prop="ownerDept" v-if="showMoreCondition">
              <el-tree-select
                v-model="queryParams.ownerDeptId"
                :data="deptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                placeholder="请选择承做部门"
                check-strictly
                filterable
                clearable
                @change="handleDeptChangeQuery"
              />
            </el-form-item>

            <el-form-item label="承做人" prop="owner" v-if="showMoreCondition">
              <el-select v-model="queryParams.ownerId" placeholder="请输入承做人" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>

            <!-- <el-form-item label="承做人" prop="ownerId">
              <el-input v-model="queryParams.ownerId" placeholder="请输入承做人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="承做部门" prop="ownerDeptId">
              <el-input v-model="queryParams.ownerDeptId" placeholder="请输入承做部门" clearable @keyup.enter="handleQuery" />
            </el-form-item> -->
            <el-form-item label="客户店铺" prop="customerShopId">
              <el-input v-model="queryParams.customerShopId" placeholder="请输入客户店铺" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户" prop="customerId">
              <el-input v-model="queryParams.customerId" placeholder="请输入客户" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="订单状态" prop="orderStatus">
              <el-select v-model="queryParams.orderStatus" placeholder="请选择订单状态" clearable>
                <el-option v-for="dict in mall_sale_order_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="订单状态修改时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeStatusChangeTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="是否预收款" prop="isAdvanceReceipt" v-if="showMoreCondition">
              <el-select v-model="queryParams.isAdvanceReceipt" placeholder="请选择是否预收款" clearable>
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="收款日期" prop="receiptDate" v-if="showMoreCondition">
              <el-date-picker clearable v-model="queryParams.receiptDate" type="date" value-format="YYYY-MM-DD" placeholder="请选择收款日期" />
            </el-form-item>
            <el-form-item label="开卡时间" prop="openCardTime" v-if="showMoreCondition">
              <el-date-picker clearable v-model="queryParams.openCardTime" type="date" value-format="YYYY-MM-DD" placeholder="请选择开卡时间" />
            </el-form-item>
            <el-form-item label="更新者" prop="updateBy">
              <!-- <el-input v-model="queryParams.updateBy" placeholder="请输入更新者" clearable @keyup.enter="handleQuery" /> -->

              <el-select v-model="queryParams.updateBy" placeholder="请输入更新者" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['recharge:rechargeOrder:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['recharge:rechargeOrder:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="rechargeOrderList" @selection-change="handleSelectionChange" border>
        <el-table-column type="index" width="55" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="订单号" prop="orderNo" width="150">
          <template #default="scope">
            <el-tooltip :content="`订单id：${scope.row.id}，单击查看订单关联的充值码`" placement="top">
              <el-link type="primary" @click="openRechargeCodeList(scope.row.id)">
                <span>{{ scope.row.orderNo }}</span>
              </el-link>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="订单状态" prop="orderStatus">
          <template #default="scope">
            <dict-tag :options="mall_sale_order_status" :value="scope.row.orderStatus" />
          </template>
        </el-table-column>
        <el-table-column label="订单类型" prop="orderType" width="100">
          <template #default="scope">
            <dict-tag :options="mall_card_order_type" :value="scope.row.orderType" />
          </template>
        </el-table-column>
        <el-table-column label="客户店铺" prop="customerShopName" width="150" show-overflow-tooltip />
        <el-table-column label="客户" prop="customerName" width="200" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ scope.row.customerName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="订单金额(元)" prop="orderAmount" width="130">
          <template #header>
            <span>订单金额(元)</span>
            <el-tooltip content="订单金额=总明细金额-抹零金额，即应收金额" placement="top">
              <el-icon class="el-icon--right">
                <InfoFilled />
              </el-icon>
            </el-tooltip>
          </template>
          <template #default="scope">
            <span style="color: #f56c6c; font-weight: bold">{{ centToYuan(scope.row.orderAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="折扣率" prop="discountPercentage" width="100">
          <template #header>
            <span>折扣率</span>
            <el-tooltip content="折扣率=订单金额/总面值" placement="top">
              <el-icon class="el-icon--right">
                <InfoFilled />
              </el-icon>
            </el-tooltip>
          </template>
          <template #default="scope">
            <span>{{ scope.row.discountPercentage || '- -' }}</span>
            <span v-if="scope.row.discountPercentage">%</span>
          </template>
        </el-table-column>
        <el-table-column label="订单用途" prop="purpose" width="100">
          <template #default="scope">
            <dict-tag :options="mall_sale_order_purpose" :value="scope.row.purpose" />
          </template>
        </el-table-column>
        <el-table-column label="承做人" prop="ownerNickName" width="150" show-overflow-tooltip />
        <el-table-column label="承做部门" prop="ownerDeptName" width="150" show-overflow-tooltip />
        <el-table-column label="总面值(元)" prop="totalValues" width="100">
          <template #default="scope">
            <span>{{ centToYuan(scope.row.totalValues) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="总明细金额(元)" prop="totalAmount" width="130">
          <template #default="scope">
            <span>{{ centToYuan(scope.row.totalAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="抹零金额(元)" prop="discountAmount" width="130">
          <template #header>
            <span>抹零金额(元)</span>
            <el-tooltip content="业务员可能会对客户采取抹零优惠" placement="top">
              <el-icon class="el-icon--right">
                <InfoFilled />
              </el-icon>
            </el-tooltip>
          </template>
          <template #default="scope">
            <span>{{ centToYuan(scope.row.discountAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="订单状态修改时间" prop="statusChangeTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.statusChangeTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否预收款" prop="isAdvanceReceipt" width="100">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isAdvanceReceipt" />
          </template>
        </el-table-column>
        <el-table-column label="预收金额(元)" prop="receiptAmount" width="100">
          <template #default="scope">
            <span v-if="scope.row.receiptAmount !== 0">{{ centToYuan(scope.row.receiptAmount) }}</span>
            <span v-else>- -</span>
          </template>
        </el-table-column>
        <el-table-column label="收款日期" prop="receiptDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.receiptDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="收款备注" prop="receiptNote" width="150" show-overflow-tooltip />
        <el-table-column label="收款附件" prop="receiptFiles" />
        <el-table-column label="开卡时间" prop="openCardTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.openCardTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="开卡凭证" prop="certificateFiles" width="80" /> -->
        <el-table-column label="兑换有效期" prop="rechargeStartDate" width="200">
          <template #header>
            <span>兑换有效期</span>
            <el-tooltip content="订单关联的充值码的兑换有效期" placement="top">
              <el-icon class="el-icon--right">
                <InfoFilled />
              </el-icon>
            </el-tooltip>
          </template>
          <template #default="scope">
            <span v-if="scope.row.rechargeStartDate && scope.row.rechargeEndDate">
              {{ parseTime(scope.row.rechargeStartDate, '{y}-{m}-{d}') }} 到 {{ parseTime(scope.row.rechargeEndDate, '{y}-{m}-{d}') }}
            </span>
            <span v-else>- -</span>
          </template>
        </el-table-column>
        <el-table-column label="开卡备注" prop="openCardNote" min-width="150" show-overflow-tooltip />
        <el-table-column label="备注（审核等）" prop="remark" :min-width="250" show-overflow-tooltip />
        <el-table-column label="更新者" prop="updateNickName" min-width="150" show-overflow-tooltip />
        <el-table-column label="更新时间" prop="updateTime" min-width="150">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="150">
          <template #default="scope">
            <!-- 查看订单 -->
            <el-tooltip content="查看" placement="top" v-if="scope.row.orderStatus !== '01'">
              <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['recharge:rechargeOrder:view']"></el-button>
            </el-tooltip>
            <!-- 修改订单 -->
            <el-tooltip content="修改" placement="top" v-if="['01', '02'].includes(scope.row.orderStatus)">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['recharge:rechargeOrder:edit']"></el-button>
            </el-tooltip>
            <!-- 提交订单 -->
            <el-tooltip
              :content="
                scope.row.detailCount > 0
                  ? `提交之后，需要等待相关同事审核。你如果知道是谁审核，请在企微中联系他吧。`
                  : `订单未添加商品明细，请点击“编辑”添加商品明细`
              "
              placement="top"
              v-if="['01', '02'].includes(scope.row.orderStatus)"
            >
              <el-button
                link
                type="primary"
                icon="Position"
                @click="handleSubmitOrder(scope.row)"
                v-hasPermi="['recharge:rechargeOrder:edit']"
              ></el-button>
            </el-tooltip>
            <!-- 删除订单 -->
            <el-tooltip content="删除" placement="top" v-if="['01', '02'].includes(scope.row.orderStatus)">
              <el-button
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['recharge:rechargeOrder:remove']"
              ></el-button>
            </el-tooltip>
            <!-- 审核订单 -->
            <el-tooltip content="审核后，就可以出卡给客户" placement="top" v-if="scope.row.orderStatus === '11'">
              <el-button
                link
                type="primary"
                icon="Promotion"
                @click="handleAudit(scope.row)"
                v-hasPermi="['recharge:rechargeOrder:audit']"
              ></el-button>
            </el-tooltip>
            <!-- 出卡 -->
            <el-tooltip content="出卡,为订单准备相应数量的充值码" placement="top" v-if="scope.row.orderStatus === '21'">
              <el-button
                link
                type="primary"
                icon="Promotion"
                @click="handleCardOut(scope.row)"
                v-hasPermi="['recharge:rechargeOrder:audit']"
              ></el-button>
            </el-tooltip>
            <!-- 开卡 -->
            <el-tooltip content="如果你确定客户已经收到了卡片，就可以进行开卡操作" placement="top" v-if="scope.row.orderStatus === '31'">
              <el-button
                link
                type="primary"
                icon="Promotion"
                @click="handleOpenCard(scope.row)"
                v-hasPermi="['recharge:rechargeOrder:openCard']"
              ></el-button>
            </el-tooltip>
            <!-- 导出卡密 -->
            <el-tooltip content="导出卡密" placement="top" v-if="scope.row.orderStatus === '40'">
              <el-button
                link
                type="primary"
                icon="Download"
                @click="handleExportCardSecret(scope.row)"
                v-hasPermi="['recharge:rechargeOrder:exportCardSecret']"
              ></el-button>
            </el-tooltip>
            <!-- 抹零 -->
            <el-tooltip content="开卡前，可结合商务沟通情况，对订单进行抹零操作" placement="top" v-if="scope.row.orderStatus === '31'">
              <el-button link type="primary" icon="Edit" @click="handleZero(scope.row)" v-hasPermi="['recharge:rechargeOrder:edit']"></el-button>
            </el-tooltip>
            <!-- 作废订单 -->
            <el-tooltip content="将订单作废，订单内的充值码也将无法使用" placement="top" v-if="['21', '31'].includes(scope.row.orderStatus)">
              <el-button link type="danger" icon="Delete" @click="handleVoid(scope.row)" v-hasPermi="['recharge:rechargeOrder:void']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改售码订单对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="80%" append-to-body @close="cancel" draggable>
      <el-row :gutter="10">
        <!-- 左侧锚点导航 -->
        <el-col :span="4">
          <el-anchor>
            <el-anchor-link href="#basic" title="客户信息" />
            <el-anchor-link href="#preAmount" title="预收款信息" v-if="form.purpose === '1' && dialogStatus != 'add'" />
            <el-anchor-link href="#goodInfo" title="商品信息" />
            <el-anchor-link href="#openCardInfo" title="开卡记录" v-if="form.orderStatus === '40' && dialogStatus != 'add'" />
          </el-anchor>
        </el-col>
        <!-- 表单内容 -->
        <el-col :span="20">
          <el-form ref="rechargeOrderFormRef" :model="form" :rules="rules" label-width="120px" label-position="top">
            <!-- 客户信息 -->
            <div id="basic" class="form-section">
              <div class="section-title">
                <span class="title-text">客户信息</span>
              </div>
              <!-- 表单信息 -->
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-form-item label="订单号" prop="orderNo">
                    <el-input v-model="form.orderNo" placeholder="创建后自动生成" disabled />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="订单类型" prop="orderType">
                    <dict-select
                      dict-key="mall_card_order_type"
                      v-model="form.orderType"
                      placeholder="请选择订单类型"
                      :disabled="dialogStatus === 'view'"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="订单用途" prop="purpose">
                    <dict-select
                      dict-key="mall_sale_order_purpose"
                      v-model="form.purpose"
                      placeholder="请选择订单用途"
                      :disabled="dialogStatus === 'view'"
                      :show-footer="false"
                    />
                  </el-form-item>
                </el-col>
                <!-- 承做信息 -->
                <el-col :span="6">
                  <el-form-item label="承做部门" prop="ownerDeptId">
                    <el-tree-select
                      v-model="form.ownerDeptId"
                      :data="deptOptions"
                      :props="{ value: 'id', label: 'label', children: 'children' }"
                      value-key="id"
                      placeholder="请选择承做部门"
                      check-strictly
                      filterable
                      @change="handleDeptChangeInForm"
                      :disabled="dialogStatus === 'view'"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="承做人（A角）" prop="ownerId" :required="!!form.ownerDeptId">
                    <el-select
                      v-model="form.ownerId"
                      placeholder="请输入承做人"
                      clearable
                      @change="handleOwnerChange"
                      filterable
                      :disabled="!form.ownerDeptId || dialogStatus === 'view'"
                    >
                      <el-option v-for="item in userOptionsByDeptId" :key="item.userId" :label="item.nickName" :value="item.userId" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="客户" prop="customerId">
                    <template #label>
                      <span>
                        客户档案
                        <el-tooltip content="打开客户档案" placement="top" v-if="form.customerId">
                          <el-icon @click="openCustomerDetailInView(form.customerId)">
                            <Link />
                          </el-icon>
                        </el-tooltip>
                        <el-tooltip content="新增客户档案" placement="top" v-else>
                          <el-icon @click="openCustomerAddDialog()">
                            <Plus />
                          </el-icon>
                        </el-tooltip>
                      </span>
                    </template>
                    <customer-select
                      v-model="form.customerId"
                      @change="handleCustomerChange"
                      style="width: 100%"
                      :disabled="dialogStatus === 'view'"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="客户店铺" prop="customerShopId">
                    <template #label>
                      <span>
                        客户店铺
                        <template v-if="form.customerId">
                          <el-tooltip content="新增客户店铺" placement="top">
                            <el-icon @click="openCustomerShopAddDialog()">
                              <Plus />
                            </el-icon>
                          </el-tooltip>
                        </template>
                      </span>
                    </template>
                    <el-select
                      v-model="form.customerShopId"
                      placeholder="请选择客户店铺"
                      filterable
                      :loading="customerShopLoading"
                      style="width: 100%"
                      :disabled="!form.customerId || dialogStatus === 'view'"
                      clearable
                    >
                      <el-option v-for="item in customerShopOptions" :key="item.id" :label="item.shopName" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="发票抬头" prop="invoiceTitleId">
                    <template #label>
                      <span>
                        发票抬头
                        <template v-if="form.customerId">
                          <el-tooltip content="新增发票抬头" placement="top">
                            <el-icon @click="openTaxInvoiceTitleAddDialog()">
                              <Plus />
                            </el-icon>
                          </el-tooltip>
                        </template>
                      </span>
                    </template>

                    <el-select
                      v-model="form.invoiceTitleId"
                      placeholder="请选择开票抬头"
                      filterable
                      :loading="taxInvoiceTitleLoading"
                      style="width: 100%"
                      :disabled="!form.customerId || dialogStatus === 'view'"
                    >
                      <el-option v-for="item in taxInvoiceTitleOptions" :key="item.id" :label="item.companyName" :value="item.id" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6" v-if="dialogStatus">
                  <el-form-item label="订单状态" prop="orderStatus">
                    <el-select v-model="form.orderStatus" placeholder="请选择订单状态" disabled>
                      <el-option v-for="dict in mall_sale_order_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                    </el-select>
                    <!-- <dict-select dict-key="mall_sale_order_status" v-model="form.orderStatus" placeholder="请选择订单状态" /> -->
                  </el-form-item>
                </el-col>
                <!-- <el-col :span="6" v-if="dialogStatus">
                  <el-form-item label="订单状态修改时间" prop="statusChangeTime">
                    <el-date-picker
                      clearable
                      v-model="form.statusChangeTime"
                      type="datetime"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      placeholder="请选择订单状态修改时间"
                      disabled
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col> -->
                <el-col :span="6">
                  <el-form-item label="客户要求" prop="customerDemand">
                    <el-input
                      v-model="form.customerDemand"
                      placeholder="请输入客户要求"
                      type="textarea"
                      :rows="3"
                      :maxlength="200"
                      show-word-limit
                      :disabled="dialogStatus === 'view'"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="备注" prop="remark">
                    <el-input
                      v-model="form.remark"
                      placeholder="请输入备注"
                      type="textarea"
                      :rows="3"
                      :maxlength="200"
                      show-word-limit
                      :disabled="dialogStatus === 'view'"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 预收款信息 -->
            <div id="preAmount" class="form-section" v-if="form.purpose === '1' && dialogStatus != 'add'">
              <div class="section-title">
                <span class="title-text">预收款信息</span>
              </div>
              <!-- 表单信息 -->
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-form-item label="是否预收款" prop="isAdvanceReceipt">
                    <el-radio-group v-model="form.isAdvanceReceipt" :disabled="dialogStatus === 'view'">
                      <el-radio v-for="dict in sys_yes_no" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="预收金额" prop="receiptAmount" v-if="form.isAdvanceReceipt === 'Y'">
                    <el-input v-model="form.receiptAmount" placeholder="请输入预收金额" :disabled="dialogStatus === 'view'" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="收款日期" prop="receiptDate" v-if="form.isAdvanceReceipt === 'Y'">
                    <el-date-picker
                      clearable
                      v-model="form.receiptDate"
                      type="datetime"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      placeholder="请选择收款日期"
                      :disabled="dialogStatus === 'view'"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="收款备注" prop="receiptNote" v-if="form.isAdvanceReceipt === 'Y'">
                    <el-input v-model="form.receiptNote" type="textarea" placeholder="请输入内容" :disabled="dialogStatus === 'view'" />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="收款附件" prop="receiptFiles" v-if="form.isAdvanceReceipt === 'Y'">
                    <file-upload v-model="form.receiptFiles" :disabled="dialogStatus === 'view'" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 商品信息 -->
            <div id="goodInfo" class="form-section">
              <div class="section-title">
                <span class="title-text">商品信息</span>
              </div>
              <div v-if="dialogStatus === 'add'">
                <el-empty :image-size="100" description="创建订单后再添加商品信息" />
              </div>
              <div v-else>
                <!-- 表单信息 -->
                <el-row :gutter="10">
                  <!-- 汇总统计 -->
                  <el-col :span="5">
                    <el-form-item label="总面值(元)" prop="totalValuesYuan">
                      <el-input v-model="form.totalValuesYuan" placeholder="请输入总面值" disabled />
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item label="总需求小计(元)" prop="totalAmountYuan">
                      <el-input v-model="form.totalAmountYuan" placeholder="请输入总明细金额" disabled />
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item label="抹零优惠(元)" prop="discountAmountYuan">
                      <el-input-number
                        v-model="form.discountAmountYuan"
                        placeholder="抹零金额"
                        :min="0"
                        :max="form.totalAmountYuan"
                        :step="1"
                        :precision="2"
                        :disabled="dialogStatus === 'view'"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item label="订单金额(元)" prop="orderAmountYuan">
                      <el-input v-model="form.orderAmountYuan" placeholder="请输入订单金额" disabled />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item label="折扣率(%)" prop="discountPercentage">
                      <el-input v-model="form.discountPercentage" placeholder="请输入折扣率" disabled />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 明细表格 -->
                <el-col :span="24">
                  <DetailTable
                    :rechargeOrderId="form.id"
                    :orderType="form.orderType"
                    :orderStatus="form.orderStatus"
                    :orderDialogStatus="dialogStatus"
                    @calculateTotalValues="calculateTotalValues"
                  />
                </el-col>
              </div>
            </div>

            <!-- 开卡记录 -->
            <div id="openCardInfo" class="form-section" v-if="form.orderStatus === '40'">
              <div class="section-title">
                <span class="title-text">开卡记录</span>
              </div>
              <!-- 表单信息 -->
              <el-row :gutter="10">
                <el-col :span="8">
                  <el-form-item label="开卡时间" prop="openCardTime">
                    <el-date-picker
                      clearable
                      v-model="form.openCardTime"
                      type="datetime"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      placeholder="请选择开卡时间"
                      :disabled="dialogStatus === 'view'"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="开卡备注" prop="openCardNote">
                    <el-input v-model="form.openCardNote" placeholder="请输入开卡备注" :disabled="dialogStatus === 'view'" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="开卡凭证" prop="certificateFiles">
                    <file-upload v-model="form.certificateFiles" :disabled="dialogStatus === 'view'" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </el-col>
      </el-row>
      <template #footer v-if="dialogStatus !== 'view'">
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 审核弹窗 -->
    <el-dialog :title="auditDialog.title" v-model="auditDialog.visible" width="40%" append-to-body destroy-on-close @close="cancel" draggable>
      <el-text type="warning" class="mb-2"> 审核通过将进入到出卡环节；不通过则需要修改后重新提交。 </el-text>
      <el-form ref="rechargeOrderFormRef" :model="form" :rules="auditRules" label-width="80px">
        <el-form-item label="审核结果" prop="orderStatus">
          <el-radio-group v-model="form.orderStatus">
            <el-radio value="21">审核通过</el-radio>
            <el-radio value="02">审核不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入审核备注" type="textarea" :rows="3" :maxlength="200" show-word-limit />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitAudit">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 出卡弹窗 -->
    <card-out-dialog ref="cardOutDialogRef" @refresh="getList" />

    <!-- 开卡弹窗 -->
    <el-dialog :title="openCardDialog.title" v-model="openCardDialog.visible" width="500px" append-to-body destroy-on-close @close="cancel" draggable>
      <div class="tips-info mb-[16px]">
        <span>为了避免我方或者客户的资产损失，目前订单中的{{ cardCount }}张卡都处于锁定状态,</span>
        <span style="color: red">需要开卡，客户才能激活绑定卡券。</span>
        <p>作为业务方，你应该确保客户已经收到了相应数量卡券。</p>
        <p>请上传可以证明客户已经收到货了截图凭证，以便在系统做相应的记录。</p>
      </div>
      <el-form ref="rechargeOrderFormRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="开卡凭证" prop="certificateFiles" :rules="{ required: true, message: '请上传开卡凭证' }">
          <FileUpload v-model="form.certificateFiles" :limit="5" />
        </el-form-item>
        <el-form-item label="兑换有效期" prop="rechargeValidityDates">
          <template #label>
            <span>
              兑换有效期
              <el-tooltip content="你可以在此重设兑换码的兑换有效期" placement="top">
                <el-icon><InfoFilled /></el-icon>
              </el-tooltip>
            </span>
          </template>
          <el-date-picker
            clearable
            v-model="form.rechargeValidityDates"
            type="daterange"
            value-format="YYYY-MM-DD HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="开卡备注" prop="openCardNote" :rules="{ required: true, message: '请输入备注' }">
          <el-input
            v-model="form.openCardNote"
            type="textarea"
            placeholder="请输入备注，如客户微信确认收货"
            :rows="4"
            maxlength="250"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer v-if="['edit', 'add'].includes(dialogStatus)">
        <div class="dialog-footer">
          <el-button type="primary" @click="submitOpenCard" :loading="buttonLoading">立即开卡</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 抹零弹窗 -->
    <el-dialog :title="zeroDialog.title" v-model="zeroDialog.visible" width="40%" append-to-body destroy-on-close @close="cancel" draggable>
      <el-form ref="rechargeOrderFormRef" :model="form" :rules="rules" label-position="top" label-width="80px">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="总面值(元)" prop="totalValuesYuan">
              <el-input v-model="form.totalValuesYuan" placeholder="请输入总面值" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总需求小计(元)" prop="totalAmountYuan">
              <el-input v-model="form.totalAmountYuan" placeholder="请输入总明细金额" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="抹零优惠(元)" prop="discountAmountYuan">
              <el-input-number
                v-model="form.discountAmountYuan"
                placeholder="抹零金额"
                :min="0"
                :max="form.totalAmountYuan"
                :step="1"
                :precision="2"
                :disabled="dialogStatus === 'view'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单金额(元)" prop="orderAmountYuan">
              <el-input v-model="form.orderAmountYuan" placeholder="请输入订单金额" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="折扣率(%)" prop="discountPercentage">
              <el-input v-model="form.discountPercentage" placeholder="请输入折扣率" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 作废订单弹窗 -->
    <el-dialog v-model="voidDialog.visible" title="作废订单" width="500px" append-to-body>
      <div class="void-dialog-content">
        <div class="dialog-body-text">
          <p>你确定要作废这个订单吗？</p>
          <p>
            <span class="text-danger font-bold">订单号：{{ voidDialog.orderNo }}</span>
          </p>
          <p>作废后，订单状态将变为<span class="text-danger font-bold">已作废</span>，且订单内的充值码将无法使用。</p>
          <p>请输入作废原因：</p>
        </div>
        <el-input v-model="voidDialog.form.remark" type="textarea" placeholder="请输入作废原因" :rows="3" maxlength="200" show-word-limit />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="voidDialog.visible = false">取消</el-button>
          <el-button type="danger" @click="confirmVoid" :loading="voidDialog.loading">确定作废</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="RechargeOrder" lang="ts">
import {
  listRechargeOrder,
  getRechargeOrder,
  delRechargeOrder,
  addRechargeOrder,
  updateRechargeOrder,
  submitRechargeOrder,
  auditRechargeOrder,
  openCard,
  voidRechargeOrder
} from '@/api/recharge/rechargeOrder';
import { RechargeOrderVO, RechargeOrderQuery, RechargeOrderForm } from '@/api/recharge/rechargeOrder/types';
import DetailTable from './components/DetailTable.vue';
import CardOutDialog from './components/CardOutDialog.vue';

// 商品明细

import {
  listRechargeOrderDetail,
  getRechargeOrderDetail,
  delRechargeOrderDetail,
  addRechargeOrderDetail,
  updateRechargeOrderDetail,
  checkBatchNumberUnique,
  cardOutSubmit,
  cardOutCancel
} from '@/api/recharge/rechargeOrderDetail';
import { RechargeOrderDetailVO, RechargeOrderDetailQuery, RechargeOrderDetailForm } from '@/api/recharge/rechargeOrderDetail/types';

import { RechargeCardQuery } from '@/api/recharge/rechargeCard/types';

import { centToYuan, yuanToCent } from '@/utils/moneyUtils';
import { parseTime } from '@/utils/ruoyi';
import dayjs from 'dayjs';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_card_order_type, sys_yes_no, mall_sale_order_purpose, mall_sale_order_status } = toRefs<any>(
  proxy?.useDict('mall_card_order_type', 'sys_yes_no', 'mall_sale_order_purpose', 'mall_sale_order_status')
);

const rechargeOrderList = ref<RechargeOrderVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogStatus = ref('add'); // add: 新增，edit: 编辑,view: 查看

const cardCount = ref(null);

/** 审核弹窗 */
const auditDialog = reactive<DialogOption>({
  visible: false,
  title: '审核订单'
});

/** 开卡弹窗 */
const openCardDialog = reactive<DialogOption>({
  visible: false,
  title: '开卡'
});

/** 抹零弹窗 */
const zeroDialog = reactive<DialogOption>({
  visible: false,
  title: '抹零'
});

/** 作废订单弹窗 */
const voidDialog = reactive({
  visible: false,
  loading: false,
  orderNo: '',
  orderId: undefined,
  form: {
    remark: ''
  }
});

/** 出卡弹窗 */
const cardOutDialogRef = ref(null);

import { useDeptSelect } from '@/hooks/useBusiness/deptSelect';
const { deptOptions, loadDeptTree } = useDeptSelect();

import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
const { loadUserList, userOptions } = useSysUserSelect();

// 表单中用户（承做人和协做人等的）选择
import { useUserSelectByDeptId } from '@/views/system/user/detail/userSelectByDeptId';
const { userOptionsByDeptId, loadUserListByDeptId, userLoadingByDeptId } = useUserSelectByDeptId();

const dateRangeStatusChangeTime = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const rechargeOrderFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: RechargeOrderForm = {
  id: undefined,
  orderNo: undefined,
  orderType: '2',
  purpose: '1',
  ownerId: undefined,
  ownerDeptId: undefined,
  customerShopId: undefined,
  customerId: undefined,
  invoiceTitleId: undefined,
  rechargeValidityDates: [dayjs().format('YYYY-MM-DD HH:mm:ss'), dayjs().add(365, 'day').format('YYYY-MM-DD HH:mm:ss')],
  rechargeStartDate: undefined,
  rechargeEndDate: undefined,
  customerDemand: undefined,
  remark: undefined,
  totalValues: undefined,
  totalAmount: undefined,
  discountAmount: undefined,
  orderAmount: undefined,
  discountPercentage: undefined,
  orderStatus: '01',
  statusChangeTime: undefined,
  isAdvanceReceipt: 'N',
  receiptAmount: undefined,
  receiptDate: undefined,
  receiptNote: undefined,
  receiptFiles: undefined,
  openCardTime: undefined,
  certificateFiles: undefined,
  openCardNote: undefined
};
const data = reactive<PageData<RechargeOrderForm, RechargeOrderQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderNo: undefined,
    orderType: undefined,
    purpose: undefined,
    ownerId: undefined,
    ownerDeptId: undefined,
    customerShopId: undefined,
    customerId: undefined,
    totalValues: undefined,
    orderStatus: undefined,
    isAdvanceReceipt: undefined,
    receiptAmount: undefined,
    receiptDate: undefined,
    openCardTime: undefined,
    updateBy: undefined,
    params: {
      statusChangeTime: undefined,
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    orderType: [{ required: true, message: '订单类型不能为空', trigger: 'change' }],
    purpose: [{ required: true, message: '订单用途不能为空', trigger: 'change' }],
    ownerId: [{ required: true, message: '承做人不能为空', trigger: 'blur' }],
    ownerDeptId: [{ required: true, message: '承做部门不能为空', trigger: 'blur' }],
    customerShopId: [{ required: true, message: '客户店铺不能为空', trigger: 'blur' }],
    customerId: [{ required: true, message: '客户不能为空', trigger: 'blur' }],
    orderStatus: [{ required: true, message: '订单状态不能为空', trigger: 'change' }],
    isAdvanceReceipt: [{ required: true, message: '是否预收款不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

const auditRules = ref({
  orderStatus: [{ required: true, message: '审核结果不能为空', trigger: 'change' }],
  remark: [{ required: true, message: '审核备注不能为空', trigger: 'blur' }]
});

// 发票抬头选择相关
import { useTaxInvoiceTitleSelect } from '@/views/crm/taxInvoiceTitle/detail/taxInvoiceTitleSelect';
const { taxInvoiceTitleOptions, taxInvoiceTitleLoading, loadTaxInvoiceTitleList } = useTaxInvoiceTitleSelect();

// 店铺选择相关
import { useCustomerShopSelect } from '@/views/recharge/customerShop/assist/customerShopSelect';
const { customerShopOptions, customerShopLoading, loadCustomerShopList } = useCustomerShopSelect();

/** 查询售码订单列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeStatusChangeTime.value, 'StatusChangeTime');
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listRechargeOrder(queryParams.value);
  rechargeOrderList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  dialogStatus.value = 'add';
  auditDialog.visible = false;
  openCardDialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  rechargeOrderFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeStatusChangeTime.value = ['', ''];
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: RechargeOrderVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加售码订单';
  dialogStatus.value = 'add';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: RechargeOrderVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getRechargeOrder(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改售码订单';
  dialogStatus.value = 'edit';

  // 加载承做人信息
  loadUserListByDeptId(form.value.ownerDeptId);
  loadCustomerShopList({ customerId: form.value.customerId, shopStatus: '0' } as CustomerShopQuery);
  // 金额信息转换
  calculateTotalValues();
  form.value.discountAmountYuan = Number(centToYuan(form.value.discountAmount));
};

/** 查看按钮操作 */
const handleView = async (row?: RechargeOrderVO) => {
  const res = await getRechargeOrder(row?.id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '查看售码订单';
  dialogStatus.value = 'view';

  // 加载承做人信息
  loadUserListByDeptId(form.value.ownerDeptId);
  loadCustomerShopList({ customerId: form.value.customerId, shopStatus: '0' } as CustomerShopQuery);
  // 金额信息转换
  form.value.totalValuesYuan = Number(centToYuan(form.value.totalValues));
  form.value.totalAmountYuan = Number(centToYuan(form.value.totalAmount));
  form.value.orderAmountYuan = Number(centToYuan(form.value.orderAmount));
  form.value.discountAmountYuan = Number(centToYuan(form.value.discountAmount));
  // console.log('res.data', res.data);
};

/** 提交按钮 */
const submitForm = () => {
  rechargeOrderFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateRechargeOrder(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addRechargeOrder(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      zeroDialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: RechargeOrderVO) => {
  await proxy?.$modal.confirm('是否确认删除订单号为"' + row?.orderNo + '"的售码订单吗？').finally(() => (loading.value = false));
  await delRechargeOrder(row?.id);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'recharge/rechargeOrder/export',
    {
      ...queryParams.value
    },
    `rechargeOrder_${new Date().getTime()}.xlsx`
  );
};

// 部门选项改变时的触发
const handleDeptChangeInForm = async (deptId: string) => {
  form.value.ownerId = undefined;
  loadUserListByDeptId(deptId);
};

// 处理承做人变更，自动更新承做部门
const handleOwnerChange = async (userId) => {
  if (!userId) return;

  // 从所有用户中找到当前选择的用户
  const selectedUser = userOptions.value.find((item) => item.userId === userId);
  if (!selectedUser) return;

  // 如果选择的用户所在部门与当前选择的部门不一致
  if (selectedUser.deptId && selectedUser.deptId !== form.value.ownerDeptId) {
    try {
      await proxy?.$modal.confirm('指派承做人后，承做部门需要自动更新为该用户所在部门，是否确认？');
      form.value.ownerDeptId = selectedUser.deptId;
      loadUserListByDeptId(selectedUser.deptId);
    } catch (error) {
      // 用户取消则恢复之前的选择
      form.value.ownerId = undefined;
    }
  }
};

// 查询时，处理归属部门选择变化
const handleDeptChangeQuery = (data) => {
  queryParams.value.ownerDeptId = data;
  handleQuery();
};

// 处理客户选择变更
import { CustomerShopQuery } from '@/api/recharge/customerShop/types';
import { TaxInvoiceTitleQuery } from '@/api/crm/taxInvoiceTitle/types';
import { edit } from '@/api/workflow/definition';

const handleCustomerChange = (customerId) => {
  if (customerId) {
    // 客户变更后，加载该客户的店铺列表,状态为启用(0)的店铺
    loadCustomerShopList({ customerId, shopStatus: '0' } as CustomerShopQuery);
    // 客户变更后，加载该客户的开票抬头列表,状态为启用(0)的开票抬头
    loadTaxInvoiceTitleList({ customerId, status: '0' } as TaxInvoiceTitleQuery);
  } else {
    // 清空店铺列表+清空已选择的店铺
    customerShopOptions.value = [];
    form.value.customerShopId = undefined;

    // 清空开票抬头列表+清空已选择的开票抬头
    taxInvoiceTitleOptions.value = [];
    form.value.invoiceTitleId = undefined;
  }
};

// 打开客户详情(查看页面)
const openCustomerDetailInView = (id: string | number) => {
  const routeUrl = `/crm/bizMg/customer/?id=${id}&openView=true`;
  window.open(routeUrl, '_blank');
};

// 打开客户弹窗（新建）
const openCustomerAddDialog = () => {
  const routeUrl = `/crm/bizMg/customer/?openAdd=true`;
  window.open(routeUrl, '_blank');
};

// 打开客户店铺弹窗(新建)
const openCustomerShopAddDialog = () => {
  const routeUrl = `/mall/recharge/customerShop/?customerId=${form.value.customerId}&openAdd=true`;
  window.open(routeUrl, '_blank');
};

// 打开发票抬头弹窗(新建)
const openTaxInvoiceTitleAddDialog = () => {
  const routeUrl = `/crm/bizMg/taxInvoiceTitle/?customerId=${form.value.customerId}&openAdd=true`;
  window.open(routeUrl, '_blank');
};

// 监听折扣金额变化
watch(
  () => form.value.discountAmountYuan,
  (newVal) => {
    if (newVal !== undefined && form.value.totalAmount !== undefined) {
      //  这里还存在问题//////////////////////////////////////
      form.value.discountAmount = Number(yuanToCent(newVal));
      form.value.orderAmount = form.value.totalAmount - form.value.discountAmount;
      form.value.orderAmountYuan = Number(centToYuan(form.value.orderAmount));
      // 重新计算折扣率
      if (form.value.totalValues && form.value.totalValues > 0) {
        form.value.discountPercentage = Number(((form.value.orderAmount / form.value.totalValues) * 100).toFixed(2));
      }
    }
  },
  { immediate: true }
);

// 计算总面值等
const calculateTotalValues = async () => {
  // 重新获取订单明细列表信息
  const query: RechargeOrderDetailQuery = {
    pageNum: 1,
    pageSize: 100,
    orderId: form.value.id
  };
  const res = await listRechargeOrderDetail(query);
  // console.log(res);
  form.value.totalValues = res.rows.reduce((acc, curr) => acc + curr.faceValue * curr.qty, 0);
  form.value.totalAmount = res.rows.reduce((acc, curr) => acc + curr.demandTotal, 0);
  form.value.totalValuesYuan = Number(centToYuan(form.value.totalValues));
  form.value.totalAmountYuan = Number(centToYuan(form.value.totalAmount));
  form.value.orderAmount = form.value.totalAmount - (form.value.discountAmount || 0);
  form.value.orderAmountYuan = Number(centToYuan(form.value.orderAmount));
  // 重新计算折扣率
  if (form.value.totalValues && form.value.totalValues > 0) {
    form.value.discountPercentage = Number(((form.value.orderAmount / form.value.totalValues) * 100).toFixed(2));
  }
};

/** 提交审核 */
const handleSubmitOrder = (row: RechargeOrderVO) => {
  if (row.detailCount > 0) {
    ElMessageBox.confirm('确定提交此售码订单？', '提交订单', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      message: h('div', null, [
        h('p', null, '确定提交此售码订单？'),
        h(
          'p',
          { style: 'color: #909399; font-size: 13px; margin-top: 10px;' },
          '提交之后，需要等待相关同事审核。你如果知道是谁审核，请在企微中联系他吧。'
        )
      ])
    }).then(() => {
      loading.value = true;
      submitRechargeOrder(row.id)
        .then(() => {
          proxy.$modal.msgSuccess('提交成功');
          getList();
        })
        .finally(() => {
          loading.value = false;
        });
    });
  } else {
    proxy.$modal.msgError('订单未添加商品明细，请点击“编辑”添加商品明细');
    handleUpdate(row);
  }
};

/** 打开审核弹窗 */
const handleAudit = async (row: RechargeOrderVO) => {
  reset();
  const res = await getRechargeOrder(row.id);
  Object.assign(form.value, res.data);
  auditDialog.visible = true;
  console.log(form.value);
};

/** 提交审核 */
const submitAudit = async () => {
  rechargeOrderFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      await auditRechargeOrder(form.value).finally(() => {
        buttonLoading.value = false;
      });
      proxy.$modal.msgSuccess('操作成功');
      auditDialog.visible = false;
      await getList();
    }
  });
};

/** 打开出卡弹窗 */
const handleCardOut = (row: RechargeOrderVO) => {
  cardOutDialogRef.value.openDialog(row, dialogStatus.value);
  dialog.visible = false;
};

/** 打开开卡弹窗 */
const handleOpenCard = (row: RechargeOrderVO) => {
  openCardDialog.visible = true;
  openCardDialog.title = '订单「' + row.orderNo + '」开卡';
  form.value.id = row.id;
};

/** 提交开卡 */
const submitOpenCard = async () => {
  rechargeOrderFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      const data = {
        id: form.value.id,
        certificateFiles: form.value.certificateFiles,
        openCardNote: form.value.openCardNote,
        rechargeStartDate: form.value.rechargeValidityDates[0],
        rechargeEndDate: form.value.rechargeValidityDates[1]
      };
      console.log('submitOpenCard', data);
      await openCard(data).finally(() => {
        openCardDialog.visible = false;
        getList();
      });
    }
  });
};

/** 打开充值码列表 */
const openRechargeCodeList = (id: string | number) => {
  const routeUrl = `/mall/recharge/rechargeCard?orderId=${id}`;
  window.open(routeUrl, '_blank');
};

/** 导出卡密 */
const queryCard = ref<RechargeCardQuery>();
const handleExportCardSecret = (row: RechargeOrderVO) => {
  const orderId = row.id;
  queryCard.value = {
    saleOrderId: orderId,
    pageNum: 1,
    pageSize: 10
  };
  proxy?.download(
    'recharge/rechargeCard/exportFull',
    {
      ...queryCard.value
    },
    `百果充值码_按订单号_${row.orderNo}_${new Date().getTime()}.xlsx`
  );
};

/** 抹零 */
const handleZero = async (row: RechargeOrderVO) => {
  reset();
  const res = await getRechargeOrder(row.id);
  Object.assign(form.value, res.data);
  zeroDialog.visible = true;
  zeroDialog.title = '订单「' + row.orderNo + '」抹零';

  // 金额信息转换
  calculateTotalValues();
  form.value.discountAmountYuan = Number(centToYuan(form.value.discountAmount));
};

/** 作废订单 */
const handleVoid = (row: RechargeOrderVO) => {
  voidDialog.orderNo = row.orderNo;
  voidDialog.orderId = row.id;
  voidDialog.form.remark = '';
  voidDialog.visible = true;
};

const confirmVoid = async () => {
  if (!voidDialog.form.remark) {
    proxy?.$modal.msgError('请输入作废原因');
    return;
  }

  try {
    voidDialog.loading = true;
    const res = await voidRechargeOrder({ id: voidDialog.orderId, remark: voidDialog.form.remark });
    voidDialog.loading = false;

    if (res.code === 200) {
      proxy?.$modal.msgSuccess('作废成功，该订单及内部的充值码已无法使用');
      voidDialog.visible = false;
      getList();
    }
  } catch (error) {
    voidDialog.loading = false;
    console.error('作废订单失败', error);
    proxy?.$modal.msgError('作废订单失败');
  }
};

onMounted(() => {
  getList();
  loadUserList();
  loadDeptTree();
});
</script>

<style lang="scss" scoped>
@import '@/assets/styles/anchorform.scss';
.tips-info {
  padding: 12px;
  background-color: #f4f4f5;
  border-radius: 4px;
}
.tips-info p {
  margin: 0;
  line-height: 1.6;
  color: #909399;
  font-size: 13px;
}

.void-dialog-content {
  padding: 0 10px;

  p {
    margin-bottom: 15px;
    font-size: 14px;
  }

  .batch-info {
    margin-bottom: 20px;
    padding: 10px 15px;
    background-color: #f8f8f8;
    border-radius: 4px;

    .batch-item {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        color: #f56c6c;
        font-weight: bold;
        margin-right: 5px;
      }

      .value {
        font-weight: 500;
      }
    }
  }
}

.dialog-body-text {
  margin-bottom: 20px;

  p {
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 1.5;
  }

  .text-danger {
    color: #f56c6c;
  }

  .font-bold {
    font-weight: bold;
  }
}
</style>
