<template>
  <div style="padding-bottom: 30px">
    <div>
      <el-row :gutter="10" class="mb8" style="margin-bottom: 10px">
        <el-col :span="1.5" v-if="props.orderDialogStatus === 'edit'">
          <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['recharge:rechargeOrderDetail:add']">新增</el-button>
        </el-col>

        <!-- <span>订单状态：{{ props.orderStatus }}</span> -->
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="rechargeOrderDetailList" @selection-change="handleSelectionChange" border>
        <el-table-column type="index" label="序号" width="55" />
        <el-table-column label="id" prop="id" v-if="false" />
        <!-- <el-table-column label="销售订单id"  prop="orderId" /> -->
        <el-table-column label="批次号" prop="batchNumber" width="200" show-overflow-tooltip>
          <template #default="scope">
            <el-tooltip content="快速查询处于可售状态的卡号，如看流水号" placement="top">
              <el-link type="primary" @click="queryCardPage(scope.row.batchNumber)">{{ scope.row.batchNumber }}</el-link>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="面值(元)" prop="faceValue" width="100">
          <template #default="scope">
            <span>{{ centToYuan(scope.row.faceValue) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="需求数量" prop="qty" width="100" />
        <el-table-column label="出卡数量" prop="authQty" width="100" v-if="props.orderStatus !== '01'">
          <template #default="scope">
            <el-tooltip content="点击查看已出卡的卡号" placement="top">
              <el-link type="primary" @click="queryCardOut(scope.row.batchNumber, props.rechargeOrderId)">{{ scope.row.authQty || '- -' }}</el-link>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="单价(元)" prop="unitPrice" width="100">
          <template #default="scope">
            <span style="color: red">{{ centToYuan(scope.row.unitPrice) || '- -' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="需求小计(元)" prop="demandTotal" width="100">
          <template #default="scope">
            <span>{{ centToYuan(scope.row.demandTotal) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="出卡小计(元)" prop="authTotal" width="100" v-if="props.orderStatus !== '01'">
          <template #default="scope">
            <span style="color: red">{{ centToYuan(scope.row.authTotal) || '- -' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="200" show-overflow-tooltip />
        <el-table-column label="更新者" prop="updateNickName" width="150" show-overflow-tooltip />
        <el-table-column label="更新时间" prop="updateTime" width="130">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          class-name="small-padding fixed-width"
          fixed="right"
          width="150"
          v-if="['edit', 'add'].includes(props.orderDialogStatus)"
        >
          <template #default="scope">
            <!-- 订单状态为「01」或「02」时，显示修改按钮 -->
            <el-tooltip content="修改" placement="top" v-if="props.orderDialogStatus === 'edit'">
              <el-button
                link
                type="primary"
                icon="Edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['recharge:rechargeOrderDetail:edit']"
              ></el-button>
            </el-tooltip>
            <!-- 订单状态为「01」或「02」时，显示删除按钮 -->
            <el-tooltip content="删除" placement="top" v-if="props.orderDialogStatus === 'edit'">
              <el-button
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['recharge:rechargeOrderDetail:remove']"
              ></el-button>
            </el-tooltip>
            <!-- 订单状态为「21」时，显示出卡按钮 -->
            <el-tooltip content="按批次添加卡号" placement="top" v-if="props.orderStatus == '21' && scope.row.qty > scope.row.authQty">
              <el-button link type="success" icon="Plus" @click="handleCardOutAdd(scope.row)" />
            </el-tooltip>
            <el-tooltip content="撤销" placement="top" v-if="props.orderStatus == '21' && scope.row.authQty > 0">
              <el-button link type="primary" icon="Delete" @click="handleCardOutCancel(scope.row)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
    <!-- 添加或修改售码订单明细对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="60%" append-to-body @close="cancel" draggable>
      <el-form ref="rechargeOrderDetailFormRef" :model="form" :rules="rules" label-width="120px" label-position="top">
        <!-- <el-form-item label="销售订单id" prop="orderId">
          <el-input v-model="form.orderId" placeholder="请输入销售订单id" />
        </el-form-item> -->
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="批次号" prop="batchNumber">
              <template #label>
                <span>批次号</span>
                <el-tooltip content="只展示状态「已完成」且可销售数量大于0的批次" placement="top">
                  <el-icon :size="14">
                    <InfoFilled />
                  </el-icon>
                </el-tooltip>
                <el-tooltip content="快速查询处于可售状态的卡号，如看流水号" placement="top">
                  <el-icon :size="14" @click="queryCardPage(form.batchNumber)">
                    <Link />
                  </el-icon>
                </el-tooltip>
              </template>
              <!-- orderType并未生效！！！！！！！！！！！！！！！！！ -->
              <RechargeBatchSelect
                v-model="form.batchNumber"
                @change="handleBatchNumberChange"
                :orderType="props.orderType"
                :disabled="dialogEditStatus"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="批次名称" prop="batchName">
              <el-input v-model="batchVo.batchName" placeholder="请输入批次名称" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="批次面值（元）">
              <el-input :value="form.faceValueYuan" placeholder="请输入批次面值" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="生成数量(张)" prop="generateQty">
              <el-input :value="batchVo.generateQty" placeholder="请输入生成数量" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="销售中数量(张)" prop="onSaleQty">
              <el-input :value="batchVo.onSaleQty || 0" placeholder="请输入销售中数量" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="可售数量(张)" prop="authQty">
              <el-input :value="batchVo.availableSaleQty || 0" placeholder="请输入可售数量" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="需求数量(张)" prop="qty">
              <el-input-number
                style="width: 100%"
                v-model="form.qty"
                placeholder="请输入需求数量"
                :min="1"
                :precision="0"
                :disabled="isDisabled || isCardOutAdd"
                @change="handleQtyChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="销售折扣(%)" prop="discount">
              <el-input-number
                v-model="form.discount"
                style="width: 100%"
                placeholder="请输入折扣"
                :min="0.01"
                :max="100"
                :precision="2"
                :disabled="isDisabled || isCardOutAdd"
                @change="handleDiscountChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="售价(元)" prop="unitPrice">
              <el-input-number
                v-model="form.unitPriceYuan"
                style="width: 100%"
                placeholder="请输入单价"
                :min="0.01"
                :precision="2"
                :disabled="isDisabled || isCardOutAdd"
                @change="handleSalePriceChange"
              />
            </el-form-item>
          </el-col>
          <!-- 订单状态为「21」时，显示已出卡数量、流水号-开始、流水号-结束 -->
          <el-col :span="8" v-if="props.orderStatus == '21'">
            <el-form-item label="已出卡数量" prop="authQty">
              <el-input :model-value="form.authQty || 0" placeholder="请输入已出卡数量" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="props.orderStatus == '21'">
            <el-form-item label="流水号-开始" prop="cardNoStart">
              <el-input v-model="form.cardNoStart" placeholder="请输入流水号" :disabled="!!form.cardNoEnd" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="props.orderStatus == '21'">
            <el-form-item label="流水号-结束" prop="cardNoEnd">
              <el-input
                v-model="form.cardNoEnd"
                placeholder="请输入流水号-结束"
                :disabled="!form.cardNoStart"
                @blur="handleCardNoSelect()"
                @keyup.enter="handleCardNoSelect()"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="props.orderStatus == '21'">
            <el-form-item label="此次出卡数量(张)" prop="cardBeOutQty">
              <el-input v-model="form.cardBeOutQty" placeholder="根据流水号自动计算" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="需求小计(元)" prop="demandTotalYuan">
              <el-input v-model="form.demandTotalYuan" placeholder="自动计算：需求数量 * 单价" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" placeholder="请输入备注" :maxlength="250" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm" v-if="['01', '02'].includes(props.orderStatus)">确 定</el-button>
          <el-button :loading="buttonLoading" type="primary" @click="submitCardOut" v-if="props.orderStatus == '21'">立即出卡</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import {
  listRechargeOrderDetail,
  getRechargeOrderDetail,
  delRechargeOrderDetail,
  addRechargeOrderDetail,
  updateRechargeOrderDetail,
  checkBatchNumberUnique,
  cardOutSubmit,
  cardOutCancel
} from '@/api/recharge/rechargeOrderDetail';
import { RechargeOrderDetailVO, RechargeOrderDetailQuery, RechargeOrderDetailForm } from '@/api/recharge/rechargeOrderDetail/types';

import { getRechargeBatchByBatchNumber } from '@/api/recharge/rechargeBatch';
import { RechargeBatchVO, RechargeBatchQuery, RechargeBatchForm } from '@/api/recharge/rechargeBatch/types';

// 充值码相关api
import { getCardNoCount } from '@/api/recharge/rechargeCard';
import { RechargeCardVO, RechargeCardQuery, RechargeCardForm } from '@/api/recharge/rechargeCard/types';

import { centToYuan, yuanToCent } from '@/utils/moneyUtils';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const props = defineProps<{
  rechargeOrderId: string | number;
  orderType: string;
  orderStatus?: string;
  orderDialogStatus?: string;
}>();

/** 准备传给父组件（即弹窗）的值orderStatistic */
const orderStatistic = ref<any>({
  totalFaceValue: 0, // 总面值金额
  totalDemandTotal: 0 // 总需求小计金额
});

// 定义要传递给父组件的事件
const emit = defineEmits(['update:orderStatistic', 'calculateTotalValues']);

/** 添加出卡信息参数 */
const isCardOutAdd = ref(false);

const rechargeOrderDetailList = ref<RechargeOrderDetailVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑

/** 处理批次号选择变化 */
const initBatchVOData: RechargeBatchVO = {
  id: undefined,
  batchNumber: undefined,
  batchName: undefined,
  batchStatus: undefined,
  saleScope: undefined,
  generateQty: undefined,
  cardForm: undefined,
  faceValue: undefined,
  effectiveTime: undefined,
  expireTime: undefined,
  remark: undefined,
  availableSaleQty: undefined,
  onSaleQty: undefined,
  updateBy: undefined,
  updateNickName: undefined,
  updateTime: undefined
};
const batchVo = ref<RechargeBatchVO>(initBatchVOData);

const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const rechargeOrderDetailFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: RechargeOrderDetailForm = {
  id: undefined,
  orderId: props.rechargeOrderId,
  batchNumber: undefined,
  qty: undefined,
  authQty: undefined,
  unitPrice: undefined,
  demandTotal: undefined,
  remark: undefined,
  discount: undefined,
  faceValueYuan: undefined,
  unitPriceYuan: undefined,
  demandTotalYuan: undefined,
  cardNoStart: undefined,
  cardNoEnd: undefined,
  cardBeOutQty: undefined
};

const data = reactive<PageData<RechargeOrderDetailForm, RechargeOrderDetailQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderId: props.rechargeOrderId,
    batchNumber: undefined,
    updateBy: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    orderId: [{ required: true, message: '销售订单id不能为空', trigger: 'blur' }],
    batchNumber: [
      { required: true, message: '批次号不能为空', trigger: ['change'] },
      {
        validator: (rule, value, callback) => {
          if (value) {
            queryUnique.id = form.value.id;
            queryUnique.orderId = props.rechargeOrderId;
            queryUnique.batchNumber = form.value.batchNumber;
            checkBatchNumberUnique(queryUnique)
              .then((res) => {
                if (res.data) {
                  callback(new Error('批次号已存在该订单中'));
                } else {
                  callback();
                }
              })
              .catch(() => {
                callback(new Error('验证批次号失败'));
              });
          } else {
            callback();
          }
        },
        trigger: ['change']
      }
    ],
    qty: [
      { required: true, message: '需求数量不能为空', trigger: 'blur' },
      {
        validator: (rule: any, value: number, callback: (error?: Error) => void) => {
          if (!batchVo.value.onSaleQty) {
            callback();
            return;
          }
          if (value > batchVo.value.availableSaleQty) {
            callback(new Error(`需求数量不能大于可售数量${batchVo.value.availableSaleQty}`));
          } else {
            callback();
          }
        },
        trigger: ['blur', 'change']
      }
    ],
    unitPrice: [
      { required: true, message: '单价不能为空', trigger: 'blur' },
      {
        validator: (rule: any, value: number, callback: (error?: Error) => void) => {
          if (!batchVo.value.faceValue) {
            callback();
            return;
          }
          if (value > batchVo.value.faceValue) {
            callback(new Error(`单价不能大于批次面值${batchVo.value.faceValue}`));
          } else {
            callback();
          }
        },
        trigger: ['blur', 'change']
      }
    ],
    detailTotal: [{ required: true, message: '明细小计不能为空', trigger: 'blur' }],
    cardBeOutQty: [{ required: true, message: '此次出卡数量不能为空', trigger: 'blur' }],
    cardNoStart: [
      {
        validator: (rule: any, value: string, callback: (error?: Error) => void) => {
          if (props.orderStatus === '21' && !value) {
            callback(new Error('请输入流水号'));
          } else {
            callback();
          }
        },
        trigger: ['blur', 'change']
      }
    ],
    cardNoEnd: [
      {
        validator: (rule: any, value: string, callback: (error?: Error) => void) => {
          if (props.orderStatus === '21' && !value) {
            callback(new Error('请输入流水号'));
          } else {
            callback();
          }
        },
        trigger: ['blur', 'change']
      }
    ],
    discount: [
      { required: true, message: '折扣不能为空', trigger: 'blur' },
      {
        validator: (rule: any, value: number, callback: (error?: Error) => void) => {
          if (value > 100) {
            callback(new Error('折扣不能大于100'));
          } else {
            callback();
          }
        },
        trigger: ['blur', 'change']
      }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 校验批次号唯一性查询体 */
const queryUnique: RechargeOrderDetailForm = {
  id: undefined,
  orderId: props.rechargeOrderId,
  batchNumber: undefined
};

/** 查询售码订单明细列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listRechargeOrderDetail(queryParams.value);
  rechargeOrderDetailList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  dialogEditStatus.value = false;
  isCardOutAdd.value = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  rechargeOrderDetailFormRef.value?.resetFields();
  batchVo.value = { ...initBatchVOData };
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: RechargeOrderDetailVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '选择批次，并填写需求信息';
  dialogEditStatus.value = false;
};

/** 修改按钮操作 */
const handleUpdate = async (row?: RechargeOrderDetailVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getRechargeOrderDetail(_id);
  Object.assign(form.value, res.data);
  console.log('handleUpdate', form.value.batchNumber);
  dialog.visible = true;
  dialog.title = '修改需求信息';
  dialogEditStatus.value = true;

  // 获取批次信息
  const resBatch = await getRechargeBatchByBatchNumber(form.value.batchNumber);
  batchVo.value = {
    ...batchVo.value,
    ...resBatch.data
  };
  form.value.discount = parseFloat(((form.value.unitPrice / batchVo.value.faceValue) * 100).toFixed(2));
};

/** 提交按钮 */
const submitForm = () => {
  rechargeOrderDetailFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateRechargeOrderDetail(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addRechargeOrderDetail(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
      emit('calculateTotalValues');
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: RechargeOrderDetailVO) => {
  await proxy?.$modal.confirm('是否确认删除制码批次编号为"' + row?.batchNumber + '"的需求项？').finally(() => (loading.value = false));
  await delRechargeOrderDetail(row?.id);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 按批次添加卡号弹窗 */
const handleCardOutAdd = (row?: RechargeOrderDetailVO) => {
  // console.log('handleCardOutAdd', row);
  handleUpdate(row);
  dialog.title = '按批次出卡';
  dialogEditStatus.value = true;
  isCardOutAdd.value = true;
};

// 添加批次和需求数量时，需求数量输入框禁用
const isDisabled = ref(true);

// 监听批次号变化，当有值时启用输入框
watch(
  () => form.value.batchNumber,
  (newVal) => {
    if (newVal) {
      isDisabled.value = false;
    } else {
      isDisabled.value = true;
    }
  }
);

/** 处理批次号选择变化 */
const handleBatchNumberChange = async (value: string) => {
  console.log('handleBatchNumberChange', value);
  if (value) {
    const res = await getRechargeBatchByBatchNumber(value);
    if (res.data.availableSaleQty > 0) {
      batchVo.value = {
        ...batchVo.value,
        availableSaleQty: res.data.availableSaleQty,
        onSaleQty: res.data.onSaleQty || 0,
        generateQty: res.data.generateQty,
        faceValue: res.data.faceValue,
        batchName: res.data.batchName
      };

      // 如果已经有qty值，触发验证
      if (form.value.qty) {
        rechargeOrderDetailFormRef.value?.validateField('qty');
      }

      // 清空需求数量
      form.value.qty = undefined;
      form.value.discount = undefined;
      form.value.unitPrice = undefined;
      form.value.demandTotal = undefined;
    } else {
      ElMessage.warning('该批次已售完');
      batchVo.value = { ...initBatchVOData };
    }
  } else {
    batchVo.value = { ...initBatchVOData };
  }
};

/** 处理折扣变化 */
const handleDiscountChange = () => {
  if (form.value.discount > 0) {
    form.value.unitPrice = parseFloat((batchVo.value.faceValue * (form.value.discount / 100)).toFixed(2));

    // 计算小计
    if (form.value.qty && form.value.unitPrice) {
      form.value.demandTotal = parseFloat((form.value.qty * form.value.unitPrice).toFixed(2));
    }
  }
};

/** 处理需求数量变化 */
const handleQtyChange = () => {
  if (form.value.qty > batchVo.value.availableSaleQty) {
    ElMessage.warning(`需求数量不能大于可售数量${batchVo.value.availableSaleQty}`);
    form.value.qty = batchVo.value.availableSaleQty;
  }
  // 计算小计
  if (form.value.qty && form.value.unitPrice) {
    form.value.demandTotal = parseFloat((form.value.qty * form.value.unitPrice).toFixed(2));
  }
};

/** 处理售价变化 */
const handleSalePriceChange = () => {
  if (form.value.unitPrice > 0) {
    if (form.value.unitPrice > batchVo.value.faceValue) {
      ElMessage.warning(`单价不能大于批次面值${batchVo.value.faceValue}`);
      form.value.unitPrice = batchVo.value.faceValue;
    }
    form.value.discount = parseFloat(((form.value.unitPrice / batchVo.value.faceValue) * 100).toFixed(2));
    // 触发数量验证
    rechargeOrderDetailFormRef.value?.validateField('qty');
    // 计算小计
    if (form.value.qty && form.value.unitPrice) {
      form.value.demandTotal = parseFloat((form.value.qty * form.value.unitPrice).toFixed(2));
    }
  }
};

// 监听需求数量变化
watch(
  () => form.value?.qty,
  (newVal) => {
    if (newVal && rechargeOrderDetailFormRef.value) {
      rechargeOrderDetailFormRef.value.validateField('qty');
    }
  }
);

// 监听单价变化
watch(
  () => form.value?.unitPrice,
  (newVal) => {
    if (newVal && rechargeOrderDetailFormRef.value) {
      rechargeOrderDetailFormRef.value.validateField('unitPrice');
    }
  }
);

/** 监听面值变化 */
watch(
  () => batchVo.value?.faceValue,
  (newVal) => {
    form.value.faceValueYuan = Number(centToYuan(newVal));
  },
  { immediate: true }
);

/** 监听单价(分)变化 */
watch(
  () => form.value.unitPrice,
  (newVal) => {
    form.value.unitPriceYuan = Number(centToYuan(newVal));
  },
  { immediate: true }
);

/** 监听单价(元)变化 */
watch(
  () => form.value.unitPriceYuan,
  (newVal) => {
    form.value.unitPrice = Number(yuanToCent(newVal));
  },
  { immediate: true }
);
/** 监听小计变化(元) */
watch(
  () => form.value.demandTotal,
  (newVal) => {
    form.value.demandTotalYuan = Number(centToYuan(newVal));
  },
  { immediate: true }
);

/** 监听小计(元)变化 */
watch(
  () => form.value.demandTotalYuan,
  (newVal) => {
    form.value.demandTotal = Number(yuanToCent(newVal));
  },
  { immediate: true }
);

/** 监听订单id变化 */
watch(
  () => props.rechargeOrderId,
  (newVal) => {
    queryParams.value.orderId = newVal;
    getList();
  }
);

/** 计算总面值和总需求小计 */
watch(
  () => rechargeOrderDetailList.value,
  (newVal: RechargeOrderDetailVO[]) => {
    // 计算新的统计值
    const newTotalDemandTotal = newVal.reduce((acc, curr) => acc + (curr.demandTotal || 0), 0);
    const newTotalFaceValue = newVal.reduce((acc, curr) => acc + (curr.faceValue || 0) * (curr.qty || 0), 0);

    // 只有当值真正发生变化时才更新和触发事件
    if (newTotalDemandTotal !== orderStatistic.value.totalDemandTotal || newTotalFaceValue !== orderStatistic.value.totalFaceValue) {
      orderStatistic.value = {
        totalDemandTotal: newTotalDemandTotal,
        totalFaceValue: newTotalFaceValue
      };

      // 向父组件发送更新后的 orderStatistic 数据
      emit('update:orderStatistic', orderStatistic.value);
    }
  },
  {
    deep: true // 深度监听数组内部变化
  }
);

/** 查询批次关联的卡号 */
const queryCardPage = (batchNumber: string) => {
  const routeUrl = `/mall/recharge/rechargeCard?batchNumber=${batchNumber}`;
  window.open(routeUrl, '_blank');
};

/** 查询批次关联的卡号(已出卡) */
const queryCardOut = (batchNumber: string, orderId: number | string) => {
  const routeUrl = `/mall/recharge/rechargeCard?batchNumber=${batchNumber}&orderId=${orderId}`;
  window.open(routeUrl, '_blank');
};

/** 处理流水号-结束输入框失去焦点事件 */
const handleCardNoSelect = async () => {
  // console.log('handleCardNoSelect', form.value);
  const query: RechargeCardQuery = {
    saleOrderId: form.value.id,
    batchNumber: form.value.batchNumber,
    cardNoStart: form.value.cardNoStart,
    cardNoEnd: form.value.cardNoEnd,
    pageNum: 1,
    pageSize: 1000 //  后端不会对这个参数做响应
  };
  const res = await getCardNoCount(query);
  if (res.data === 0) {
    proxy?.$modal.msgError(`流水号范围内无可售充值码,请重新选择`);
    form.value.cardBeOutQty = null;
  }
  if (res.data + form.value.authQty > form.value.qty) {
    proxy?.$modal.msgError(`累计出卡数量不能超过需求数量`);
    form.value.cardBeOutQty = null;
  } else {
    form.value.cardBeOutQty = res.data;
  }
};

/** 立即出卡按钮操作 */
const submitCardOut = () => {
  rechargeOrderDetailFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      console.log('submitCardOut', form.value);

      /** 组装数据 */
      const data: RechargeOrderDetailForm = {
        id: form.value.id,
        batchNumber: form.value.batchNumber,
        orderId: form.value.orderId,
        cardNoStart: form.value.cardNoStart,
        cardNoEnd: form.value.cardNoEnd
      };

      await cardOutSubmit(data).finally(() => (buttonLoading.value = false));
      proxy?.$modal.msgSuccess('出卡成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 撤销出卡按钮操作 */
const handleCardOutCancel = async (row?: RechargeOrderDetailVO) => {
  // console.log('handleCardOutCancel', row);
  const data: RechargeOrderDetailForm = {
    id: row?.id,
    batchNumber: row?.batchNumber,
    orderId: row?.orderId
  };
  await cardOutCancel(data).finally(() => (buttonLoading.value = false));
  proxy?.$modal.msgSuccess('撤销成功');
  await getList();
};

onMounted(() => {
  getList();
});
</script>
