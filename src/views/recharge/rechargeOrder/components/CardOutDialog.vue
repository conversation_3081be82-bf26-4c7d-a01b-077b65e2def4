<template>
  <el-dialog :title="dialog.title" v-model="dialog.visible" width="70%" append-to-body destroy-on-close @close="handleClose" draggable>
    <detail-table
      ref="detailTableRef"
      :rechargeOrderId="rechargeOrder.id"
      :orderType="rechargeOrder.orderType"
      :orderStatus="rechargeOrder.orderStatus"
      :orderDialogStatus="orderDialogStatus"
    />
    <template #footer>
      <el-button type="primary" :loading="buttonLoading" @click="handleWithdrawCard">完成出卡</el-button>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { RechargeOrderVO } from '@/api/recharge/rechargeOrder/types';
import DetailTable from './DetailTable.vue';
import { cardOutFinish } from '@/api/recharge/rechargeOrder';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const rechargeOrder = ref<RechargeOrderVO>();
const detailTableRef = ref<InstanceType<typeof DetailTable>>();
const buttonLoading = ref(false);
const dialog = ref({
  visible: false,
  title: ''
});

const orderDialogStatus = ref<string | null>(null);

const emit = defineEmits(['refresh']);

const handleClose = () => {
  dialog.value.visible = false;
  emit('refresh');
};

const openDialog = async (row: RechargeOrderVO, orderDialogEditStatus: string) => {
  dialog.value.visible = true;
  dialog.value.title = '订单「' + row.orderNo + '」出卡';
  rechargeOrder.value = row;
  orderDialogStatus.value = orderDialogEditStatus;
};

const handleWithdrawCard = async () => {
  buttonLoading.value = true;
  await cardOutFinish(rechargeOrder.value?.id).finally(() => (buttonLoading.value = false));
  proxy?.$modal.msgSuccess('完成出卡');
  handleClose();
};

defineExpose({
  openDialog
});
</script>
