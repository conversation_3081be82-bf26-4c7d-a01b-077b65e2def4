<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="客户店铺" prop="customerShopId">
              <el-input v-model="queryParams.customerShopId" placeholder="请输入客户店铺" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="商品" prop="goodsId">
              <el-input v-model="queryParams.goodsId" placeholder="请输入商品" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="结算价，跟B结算" prop="settlePrice">
              <el-input v-model="queryParams.settlePrice" placeholder="请输入结算价，跟B结算" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="销售价，C下单价" prop="sellingPrice">
              <el-input v-model="queryParams.sellingPrice" placeholder="请输入销售价，C下单价" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="更新者" prop="updateBy">
              <el-input v-model="queryParams.updateBy" placeholder="请输入更新者" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['recharge:shopGoods:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['recharge:shopGoods:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['recharge:shopGoods:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['recharge:shopGoods:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="shopGoodsList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="id" align="center" prop="id" v-if="true" />
        <el-table-column label="客户店铺" align="center" prop="customerShopId" />
        <el-table-column label="商品" align="center" prop="goodsId" />
        <el-table-column label="结算价，跟B结算" align="center" prop="settlePrice" />
        <el-table-column label="销售价，C下单价" align="center" prop="sellingPrice" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="更新者" align="center" prop="updateBy" />
        <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['recharge:shopGoods:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['recharge:shopGoods:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改客户店铺明细对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body @close="cancel" draggable>
      <el-form ref="shopGoodsFormRef" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="客户店铺" prop="customerShopId">
          <el-input v-model="form.customerShopId" placeholder="请输入客户店铺" />
        </el-form-item>
        <el-form-item label="商品" prop="goodsId">
          <el-input v-model="form.goodsId" placeholder="请输入商品" />
        </el-form-item>
        <el-form-item label="结算价，跟B结算" prop="settlePrice">
          <el-input v-model="form.settlePrice" placeholder="请输入结算价，跟B结算" />
        </el-form-item>
        <el-form-item label="销售价，C下单价" prop="sellingPrice">
          <el-input v-model="form.sellingPrice" placeholder="请输入销售价，C下单价" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ShopGoods" lang="ts">
import { listShopGoods, getShopGoods, delShopGoods, addShopGoods, updateShopGoods } from '@/api/recharge/shopGoods';
import { ShopGoodsVO, ShopGoodsQuery, ShopGoodsForm } from '@/api/recharge/shopGoods/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const shopGoodsList = ref<ShopGoodsVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑

const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const shopGoodsFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ShopGoodsForm = {
  id: undefined,
  customerShopId: undefined,
  goodsId: undefined,
  settlePrice: undefined,
  sellingPrice: undefined,
  remark: undefined
};
const data = reactive<PageData<ShopGoodsForm, ShopGoodsQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    customerShopId: undefined,
    goodsId: undefined,
    settlePrice: undefined,
    sellingPrice: undefined,
    updateBy: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    customerShopId: [{ required: true, message: '客户店铺不能为空', trigger: 'blur' }],
    goodsId: [{ required: true, message: '商品不能为空', trigger: 'blur' }],
    sellingPrice: [{ required: true, message: '销售价，C下单价不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询客户店铺明细列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listShopGoods(queryParams.value);
  shopGoodsList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  shopGoodsFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ShopGoodsVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加店铺商品';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ShopGoodsVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getShopGoods(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改店铺商品';
};

/** 提交按钮 */
const submitForm = () => {
  shopGoodsFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateShopGoods(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addShopGoods(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ShopGoodsVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除店铺商品编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delShopGoods(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'recharge/shopGoods/export',
    {
      ...queryParams.value
    },
    `shopGoods_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
