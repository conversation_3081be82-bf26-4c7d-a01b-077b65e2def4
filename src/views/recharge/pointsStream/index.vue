<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="积分账户" prop="pointsAccountId">
              <el-input v-model="queryParams.pointsAccountId" placeholder="请输入积分账户" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="充值人手机" prop="rechargeOpPhone">
              <el-input v-model="queryParams.rechargeOpPhone" placeholder="请输入充值人手机" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="流水原因" prop="streamReason">
              <el-select v-model="queryParams.streamReason" placeholder="请选择流水原因" clearable>
                <el-option v-for="dict in mall_points_stream_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="流水金额" prop="streamAmount">
              <el-select v-model="queryParams.streamAmount" placeholder="请选择流水金额" clearable>
                <el-option v-for="dict in mall_points_stream_reason" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="流水前余额" prop="balanceBefore">
              <el-input v-model="queryParams.balanceBefore" placeholder="请输入流水前余额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="流水后余额" prop="balanceAfter">
              <el-input v-model="queryParams.balanceAfter" placeholder="请输入流水后余额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="更新者" prop="updateBy">
              <el-input v-model="queryParams.updateBy" placeholder="请输入更新者" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['recharge:pointsStream:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="pointsStreamList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="积分账户" prop="pointsAccountId" />
        <el-table-column label="充值人手机" prop="rechargeOpPhone" />
        <el-table-column label="流水类型" prop="streamType" />
        <el-table-column label="流水原因" prop="streamReason">
          <template #default="scope">
            <dict-tag :options="mall_points_stream_type" :value="scope.row.streamReason" />
          </template>
        </el-table-column>
        <el-table-column label="流水金额" prop="streamAmount">
          <template #default="scope">
            <dict-tag :options="mall_points_stream_reason" :value="scope.row.streamAmount" />
          </template>
        </el-table-column>
        <el-table-column label="流水前余额" prop="balanceBefore" />
        <el-table-column label="流水后余额" prop="balanceAfter" />
        <el-table-column label="备注" prop="remark" />
        <!-- <el-table-column label="更新者" prop="updateBy" />
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column> -->
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="100">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['recharge:pointsStream:edit']"></el-button>
            </el-tooltip>
            <!-- <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['recharge:pointsStream:remove']"></el-button>
            </el-tooltip> -->
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改积分流水对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body @close="cancel" draggable>
      <el-form ref="pointsStreamFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="积分账户" prop="pointsAccountId">
          <el-input v-model="form.pointsAccountId" placeholder="请输入积分账户" />
        </el-form-item>
        <el-form-item label="充值人手机" prop="rechargeOpPhone">
          <el-input v-model="form.rechargeOpPhone" placeholder="请输入充值人手机" />
        </el-form-item>
        <el-form-item label="流水原因" prop="streamReason">
          <el-select v-model="form.streamReason" placeholder="请选择流水原因">
            <el-option v-for="dict in mall_points_stream_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="流水金额" prop="streamAmount">
          <el-select v-model="form.streamAmount" placeholder="请选择流水金额">
            <el-option v-for="dict in mall_points_stream_reason" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="流水前余额" prop="balanceBefore">
          <el-input v-model="form.balanceBefore" placeholder="请输入流水前余额" />
        </el-form-item>
        <el-form-item label="流水后余额" prop="balanceAfter">
          <el-input v-model="form.balanceAfter" placeholder="请输入流水后余额" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PointsStream" lang="ts">
import { listPointsStream, getPointsStream, delPointsStream, addPointsStream, updatePointsStream } from '@/api/recharge/pointsStream';
import { PointsStreamVO, PointsStreamQuery, PointsStreamForm } from '@/api/recharge/pointsStream/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_points_stream_type, mall_points_stream_reason } = toRefs<any>(proxy?.useDict('mall_points_stream_type', 'mall_points_stream_reason'));

const pointsStreamList = ref<PointsStreamVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑

const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const pointsStreamFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: PointsStreamForm = {
  id: undefined,
  pointsAccountId: undefined,
  rechargeOpPhone: undefined,
  streamType: undefined,
  streamReason: undefined,
  streamAmount: undefined,
  balanceBefore: undefined,
  balanceAfter: undefined,
  remark: undefined
};
const data = reactive<PageData<PointsStreamForm, PointsStreamQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    pointsAccountId: undefined,
    rechargeOpPhone: undefined,
    streamType: undefined,
    streamReason: undefined,
    streamAmount: undefined,
    balanceBefore: undefined,
    balanceAfter: undefined,
    updateBy: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    pointsAccountId: [{ required: true, message: '积分账户不能为空', trigger: 'blur' }],
    streamType: [{ required: true, message: '流水类型不能为空', trigger: 'change' }],
    streamReason: [{ required: true, message: '流水原因不能为空', trigger: 'change' }],
    streamAmount: [{ required: true, message: '流水金额不能为空', trigger: 'change' }],
    balanceBefore: [{ required: true, message: '流水前余额不能为空', trigger: 'blur' }],
    balanceAfter: [{ required: true, message: '流水后余额不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询积分流水列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listPointsStream(queryParams.value);
  pointsStreamList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  pointsStreamFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: PointsStreamVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加积分流水';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: PointsStreamVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getPointsStream(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改积分流水';
};

/** 提交按钮 */
const submitForm = () => {
  pointsStreamFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updatePointsStream(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addPointsStream(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: PointsStreamVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除积分流水编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delPointsStream(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'recharge/pointsStream/export',
    {
      ...queryParams.value
    },
    `pointsStream_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
