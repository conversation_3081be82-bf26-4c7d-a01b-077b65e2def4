<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="批次号" prop="batchNumber">
              <el-input v-model="queryParams.batchNumber" placeholder="请输入批次号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="批次名称" prop="batchName">
              <el-input v-model="queryParams.batchName" placeholder="请输入批次名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="批次状态" prop="batchStatus">
              <el-select v-model="queryParams.batchStatus" placeholder="请选择批次状态" clearable @change="handleQuery">
                <el-option v-for="dict in mall_card_make_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="销售范围" prop="saleScope">
              <el-select v-model="queryParams.saleScope" placeholder="请选择销售范围" clearable @change="handleQuery">
                <el-option v-for="dict in mall_card_sale_scope" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="卡面形态" prop="cardForm" v-if="showMoreCondition">
              <el-select v-model="queryParams.cardForm" placeholder="请选择卡面形态" clearable @change="handleQuery">
                <el-option v-for="dict in mall_card_form" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="生效时间" style="width: 308px" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeEffectiveTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="失效时间" style="width: 308px" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeExpireTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="更新者" prop="updateBy">
              <el-select v-model="queryParams.updateBy" placeholder="请输入更新者" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['recharge:rechargeBatch:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['recharge:rechargeBatch:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="rechargeBatchList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="批次号" prop="batchNumber" width="150">
          <template #default="scope">
            <el-tooltip content="快速查看该批次号生成的充值码" placement="top">
              <el-link type="primary" @click="queryCardPage(scope.row.batchNumber)">{{ scope.row.batchNumber }}</el-link>
            </el-tooltip>
            <el-tooltip content="批次名称" placement="top">
              <div style="color: #999; font-size: 12px">{{ scope.row.batchName }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <!-- <el-table-column label="批次名称" prop="batchName" /> -->
        <el-table-column label="面值(元)" prop="faceValue" width="100">
          <template #default="scope">
            <span>{{ centToYuan(scope.row.faceValue) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="卡面形态" prop="cardForm">
          <template #default="scope">
            <dict-tag :options="mall_card_form" :value="scope.row.cardForm" />
          </template>
        </el-table-column>
        <el-table-column label="批次状态" prop="batchStatus" width="100">
          <template #default="scope">
            <dict-tag :options="mall_card_make_status" :value="scope.row.batchStatus" />
          </template>
        </el-table-column>
        <el-table-column label="制码数量" prop="generateQty" width="100">
          <template #default="scope">
            <span>{{ scope.row.generateQty }}</span>
            <span style="color: #999; font-size: 12px; padding-left: 5px">个</span>
          </template>
        </el-table-column>
        <el-table-column label="可售数量" prop="availableSaleQty" width="100">
          <template #header>
            <span>可售数量</span>
            <el-tooltip content="统计符合一下条件的充值码数量：可用状态 = 锁定，使用状态 = 待激活，销售状态 = 未销售" placement="top">
              <el-icon class="el-icon--right">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </template>
          <template #default="scope">
            <span>{{ scope.row.availableSaleQty || 0 }}</span>
            <span style="color: #999; font-size: 12px; padding-left: 5px">个</span>
          </template>
        </el-table-column>
        <el-table-column label="销售中数量" prop="onSaleQty" width="110">
          <template #header>
            <span>销售中数量</span>
            <el-tooltip content="统计符合一下条件的充值码数量：可用状态 = 锁定，使用状态 = 待激活，销售状态 = 销售中" placement="top">
              <el-icon class="el-icon--right">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </template>
          <template #default="scope">
            <span>{{ scope.row.onSaleQty || 0 }}</span>
            <span style="color: #999; font-size: 12px; padding-left: 5px">个</span>
          </template>
        </el-table-column>
        <el-table-column label="生效日期" prop="effectiveTime" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.effectiveTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="失效日期" prop="expireTime" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.expireTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="销售范围" prop="saleScope" width="100">
          <template #default="scope">
            <dict-tag :options="mall_card_sale_scope" :value="scope.row.saleScope" />
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="250" show-overflow-tooltip />
        <el-table-column label="更新者" prop="updateNickName" width="150" />
        <el-table-column label="更新时间" prop="updateTime" width="150">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="150">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['recharge:rechargeBatch:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="提交" placement="top" v-if="scope.row.batchStatus === '01'">
              <el-button
                link
                type="primary"
                icon="Promotion"
                @click="handleSubmit(scope.row)"
                v-hasPermi="['recharge:rechargeBatch:submit']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="制作实体卡" placement="top" v-if="scope.row.batchStatus === '21'">
              <el-button
                link
                type="primary"
                icon="Promotion"
                @click="handleMakePhysicalCard(scope.row)"
                v-hasPermi="['recharge:rechargeBatch:makeCard']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="导出卡密" placement="top" v-if="['21', '41', '51'].includes(scope.row.batchStatus)">
              <el-button
                link
                type="primary"
                icon="Download"
                @click="handleExportCardSecret(scope.row)"
                v-hasPermi="['recharge:rechargeBatch:exportCard']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="完成实体卡制卡" placement="top" v-if="scope.row.batchStatus === '41'">
              <el-button
                link
                type="primary"
                icon="Promotion"
                @click="handleFinishMakePhysicalCard(scope.row)"
                v-hasPermi="['recharge:rechargeBatch:makeCardFinish']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="作废充值码" placement="top" v-if="['21', '41', '51'].includes(scope.row.batchStatus)">
              <el-button
                link
                type="danger"
                icon="Delete"
                @click="handleVoidByBatchNumber(scope.row)"
                v-hasPermi="['recharge:rechargeCard:void']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top" v-if="scope.row.batchStatus === '01'">
              <el-button
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['recharge:rechargeBatch:remove']"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改制码批次对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="50%" append-to-body @close="cancel" draggable>
      <el-form ref="rechargeBatchFormRef" :model="form" :rules="rules" label-width="120px" label-position="top">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="批次号" prop="batchNumber">
              <el-input v-model="form.batchNumber" placeholder="创建时系统自动生成" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="批次名称" prop="batchName">
              <el-input v-model="form.batchName" placeholder="请输入批次名称" :maxlength="20" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="面值(元)" prop="faceValue">
              <el-input-number
                placeholder="最大5000元，只支持整数"
                :min="1"
                :max="5000"
                :step="1"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                @change="(val) => (form.faceValue = val ? yuanToCent(Number(val)) : undefined)"
                :model-value="form.faceValue ? Number(centToYuan(form.faceValue)) : undefined"
                :disabled="form.batchStatus != '01'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="制码数量(个)" prop="generateQty">
              <el-input-number
                v-model="form.generateQty"
                placeholder="一次生成码的数量，不超过2万个"
                :min="1"
                :max="20000"
                :step="1"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                :disabled="form.batchStatus != '01'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="销售范围" prop="saleScope">
              <dict-select
                v-model="form.saleScope"
                dict-key="mall_card_sale_scope"
                placeholder="请选择销售范围"
                :disabled="form.batchStatus != '01'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="卡面形态" prop="cardForm">
              <el-radio-group v-model="form.cardForm" :disabled="form.batchStatus != '01'">
                <el-radio v-for="dict in mall_card_form" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效日期" prop="effectiveDates">
              <el-date-picker
                clearable
                v-model="effectiveDates"
                type="daterange"
                value-format="YYYY-MM-DD HH:mm:ss"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
                @change="handleEffectiveDatesChange"
                :disabled="form.batchStatus != '01'"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" placeholder="请输入备注" :maxlength="200" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="12"></el-col>
          <el-col :span="12"></el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 作废充值码对话框 -->
    <el-dialog v-model="voidDialog.visible" title="作废批次" width="500px" append-to-body>
      <div class="dialog-body-text">
        <p>确定要作废该批次下未售出的卡号吗？此操作将不可逆转，请谨慎操作。</p>
        <p>
          <span class="text-danger font-bold">批次号：{{ voidDialog.batchNumber }}</span>
        </p>
        <p>批次名称：{{ voidDialog.batchName }}</p>
      </div>
      <el-form>
        <el-form-item label="备注" required>
          <el-input v-model="voidDialog.form.remark" type="textarea" placeholder="请输入作废原因" :rows="3" maxlength="200" show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="voidDialog.visible = false">取消</el-button>
          <el-button type="danger" @click="confirmVoid" :loading="voidDialog.loading">确定作废</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="RechargeBatch" lang="ts">
import {
  listRechargeBatch,
  getRechargeBatch,
  delRechargeBatch,
  addRechargeBatch,
  updateRechargeBatch,
  submitRechargeBatch,
  batchToMakePhysicalCard,
  finishMakePhysicalCard,
  voidByBatchNumber
} from '@/api/recharge/rechargeBatch';
import { RechargeBatchVO, RechargeBatchQuery, RechargeBatchForm } from '@/api/recharge/rechargeBatch/types';
import dayjs from 'dayjs';

import { centToYuan, yuanToCent } from '@/utils/moneyUtils';
import { parseTime } from '@/utils/ruoyi';

import { RechargeCardVO, RechargeCardQuery, RechargeCardForm } from '@/api/recharge/rechargeCard/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_card_form, mall_card_make_status, mall_card_sale_scope } = toRefs<any>(
  proxy?.useDict('mall_card_form', 'mall_card_make_status', 'mall_card_sale_scope')
);

const rechargeBatchList = ref<RechargeBatchVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑

const dateRangeEffectiveTime = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeExpireTime = ref<[DateModelType, DateModelType]>(['', '']);
const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const effectiveDates = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const rechargeBatchFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const voidDialog = reactive({
  visible: false,
  loading: false,
  batchNumber: '',
  batchName: '',
  form: {
    remark: ''
  }
});

const initFormData: RechargeBatchForm = {
  id: undefined,
  batchNumber: undefined,
  batchName: undefined,
  batchStatus: '01',
  saleScope: undefined,
  generateQty: undefined,
  cardForm: '1',
  faceValue: undefined,
  effectiveTime: undefined,
  expireTime: undefined,
  remark: undefined
};
const data = reactive<PageData<RechargeBatchForm, RechargeBatchQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    batchNumber: undefined,
    batchName: undefined,
    batchStatus: undefined,
    saleScope: undefined,
    cardForm: undefined,
    updateBy: undefined,
    params: {
      effectiveTime: undefined,
      expireTime: undefined,
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    batchStatus: [{ required: true, message: '批次状态不能为空', trigger: 'change' }],
    saleScope: [{ required: true, message: '销售范围不能为空', trigger: 'change' }],
    generateQty: [{ required: true, message: '制码数量不能为空', trigger: 'blur' }],
    cardForm: [{ required: true, message: '卡面形态不能为空', trigger: 'change' }],
    faceValue: [{ required: true, message: '面值不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
import { ca } from 'element-plus/es/locale/index.mjs';
const { loadUserList, userOptions } = useSysUserSelect();

/** 查询制码批次列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeEffectiveTime.value, 'EffectiveTime');
  proxy?.addDateRange(queryParams.value, dateRangeExpireTime.value, 'ExpireTime');
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listRechargeBatch(queryParams.value);
  rechargeBatchList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  rechargeBatchFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeEffectiveTime.value = ['', ''];
  dateRangeExpireTime.value = ['', ''];
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: RechargeBatchVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  effectiveDates.value = ['', ''];
  dialog.visible = true;
  dialog.title = '添加制码批次';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: RechargeBatchVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getRechargeBatch(_id);
  Object.assign(form.value, res.data);
  if (form.value.effectiveTime && form.value.expireTime) {
    effectiveDates.value = [
      dayjs(form.value.effectiveTime).format('YYYY-MM-DD HH:mm:ss'),
      dayjs(form.value.expireTime).format('YYYY-MM-DD HH:mm:ss')
    ];
  }
  dialog.visible = true;
  dialog.title = '修改制码批次';
};

/** 提交按钮 */
const submitForm = () => {
  rechargeBatchFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateRechargeBatch(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addRechargeBatch(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: RechargeBatchVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除制码批次编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delRechargeBatch(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'recharge/rechargeBatch/export',
    {
      ...queryParams.value
    },
    `rechargeBatch_${new Date().getTime()}.xlsx`
  );
};

/** 生效日期选择 */
const handleEffectiveDatesChange = () => {
  if (effectiveDates.value && effectiveDates.value[0]) {
    form.value.effectiveTime = effectiveDates.value[0].toString();
    form.value.expireTime = effectiveDates.value[1].toString();
  }
};

/** 提交制卡 */
const handleSubmit = async (row: RechargeBatchVO) => {
  try {
    await proxy.$modal.confirm(
      '确认要提交制卡吗？提交后系统将按需求生成面值为' + centToYuan(row.faceValue) + '元的充值码' + row.generateQty + '张,请耐心等待制卡完成。'
    );
    const res = await submitRechargeBatch(row.id);
    if (res.code === 200) {
      proxy.$modal.msgSuccess('提交成功，已经进入制卡中～');
      getList();
    }
  } catch (error) {
    console.error('提交制卡失败', error);
  }
};

/** 制作实体卡 */
const handleMakePhysicalCard = async (row) => {
  try {
    await proxy.$modal.confirm('确认要制作实体卡吗？点击确定之后，批次状态将流转到「制卡中」，你可以将卡密导出给制卡商');
    const res = await batchToMakePhysicalCard(row.id);
    if (res.code === 200) {
      proxy.$modal.msgSuccess('制作实体卡请求成功，批次已进入制卡中～');
      getList();
    }
  } catch (error) {
    console.error('制作实体卡请求失败', error);
  }
};

/** 完成实体卡制卡 */
const handleFinishMakePhysicalCard = async (row) => {
  try {
    await proxy.$modal.confirm('确认要完成实体卡制卡吗？');
    const res = await finishMakePhysicalCard(row.id);
    if (res.code === 200) {
      proxy.$modal.msgSuccess('完成实体卡制卡请求成功~');
      getList();
    }
  } catch (error) {
    console.error('完成实体卡制卡请求失败！', error);
  }
};

/** 导出卡密 */
const queryCard = ref<RechargeCardQuery>();
const handleExportCardSecret = (row: RechargeBatchVO) => {
  const batchNumber = row.batchNumber;
  queryCard.value = {
    batchNumber: batchNumber,
    pageNum: 1,
    pageSize: 10
  };
  proxy?.download(
    'recharge/rechargeCard/exportFull',
    {
      ...queryCard.value
    },
    `百果充值码_${row.batchNumber}_${new Date().getTime()}.xlsx`
  );
};

/** 查询批次关联的卡号 */
const queryCardPage = (batchNumber: string) => {
  const routeUrl = `/mall/recharge/rechargeCard?batchNumber=${batchNumber}`;
  window.open(routeUrl, '_blank');
};

/** 作废充值码 */
const handleVoidByBatchNumber = (row: RechargeBatchVO) => {
  if (!['21', '41', '51'].includes(row.batchStatus)) {
    proxy?.$modal.msgError('只有制卡中或制卡完成状态的批次才能作废');
    return;
  }

  voidDialog.batchNumber = row.batchNumber;
  voidDialog.batchName = row.batchName;
  voidDialog.form.remark = '';
  voidDialog.visible = true;
};

/** 确认作废 */
const confirmVoid = async () => {
  if (!voidDialog.form.remark) {
    proxy?.$modal.msgError('请输入作废原因');
    return;
  }

  try {
    voidDialog.loading = true;
    const res = await voidByBatchNumber({ batchNumber: voidDialog.batchNumber, remark: voidDialog.form.remark });
    voidDialog.loading = false;

    if (res.code === 200) {
      proxy?.$modal.msgSuccess('作废成功，该批次的所有充值码已无法使用');
      voidDialog.visible = false;
      getList();
    }
  } catch (error) {
    voidDialog.loading = false;
    console.error('作废充值码失败', error);
    proxy?.$modal.msgError('作废充值码失败');
  }
};

onMounted(() => {
  getList();
  loadUserList();
});
</script>

<style scoped lang="scss">
.void-dialog-content {
  padding: 0 10px;

  p {
    margin-bottom: 15px;
    font-size: 14px;
  }

  .batch-info {
    margin-bottom: 20px;
    padding: 10px 15px;
    background-color: #f8f8f8;
    border-radius: 4px;

    .batch-item {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        color: #f56c6c;
        font-weight: bold;
        margin-right: 5px;
      }

      .value {
        font-weight: 500;
      }
    }
  }
}

.dialog-body-text {
  margin-bottom: 20px;

  p {
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 1.5;
  }

  .text-danger {
    color: #f56c6c;
  }

  .font-bold {
    font-weight: bold;
  }
}
</style>
