<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="客户店铺" prop="customerShopId">
              <el-input v-model="queryParams.customerShopId" placeholder="请输入客户店铺" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="用户手机号" prop="userPhone">
              <el-input v-model="queryParams.userPhone" placeholder="请输入用户手机号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="结算依据" prop="settlementBasis">
              <el-select v-model="queryParams.settlementBasis" placeholder="请选择结算依据" clearable>
                <el-option v-for="dict in mall_settlement_basis" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="账户余额" prop="accountBalance">
              <el-input v-model="queryParams.accountBalance" placeholder="请输入账户余额" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否白名单" prop="isWhitelist">
              <el-select v-model="queryParams.isWhitelist" placeholder="请选择是否白名单" clearable>
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新者" prop="updateBy">
              <el-input v-model="queryParams.updateBy" placeholder="请输入更新者" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <!-- <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['recharge:pointsAccount:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['recharge:pointsAccount:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['recharge:pointsAccount:remove']"
              >删除</el-button
            >
          </el-col> -->
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['recharge:pointsAccount:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="pointsAccountList" @selection-change="handleSelectionChange" border>
        <el-table-column type="index" width="55" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="客户店铺" width="150" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.customerShop.shopName }}
          </template>
        </el-table-column>
        <el-table-column label="用户手机号" prop="userPhone" width="120" />
        <el-table-column label="结算依据" prop="settlementBasis">
          <template #default="scope">
            <dict-tag :options="mall_settlement_basis" :value="scope.row.settlementBasis" />
          </template>
        </el-table-column>
        <el-table-column label="账户余额" prop="accountBalance" />
        <el-table-column label="是否白名单" prop="isWhitelist" width="120">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isWhitelist" />
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" />
        <el-table-column label="更新者" prop="updateBy" />
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['recharge:pointsAccount:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['recharge:pointsAccount:remove']"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改积分账户对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body @close="cancel" draggable>
      <el-form ref="pointsAccountFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="客户店铺" prop="customerShopId">
          <el-input v-model="form.customerShopId" placeholder="请输入客户店铺" />
        </el-form-item>
        <el-form-item label="用户手机号" prop="userPhone">
          <el-input v-model="form.userPhone" placeholder="请输入用户手机号" />
        </el-form-item>
        <el-form-item label="结算依据" prop="settlementBasis">
          <el-radio-group v-model="form.settlementBasis">
            <el-radio v-for="dict in mall_settlement_basis" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="账户余额" prop="accountBalance">
          <el-input v-model="form.accountBalance" placeholder="请输入账户余额" />
        </el-form-item>
        <el-form-item label="是否白名单" prop="isWhitelist">
          <el-radio-group v-model="form.isWhitelist">
            <el-radio v-for="dict in sys_yes_no" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PointsAccount" lang="ts">
import { listPointsAccount, getPointsAccount, delPointsAccount, addPointsAccount, updatePointsAccount } from '@/api/recharge/pointsAccount';
import { PointsAccountVO, PointsAccountQuery, PointsAccountForm } from '@/api/recharge/pointsAccount/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_settlement_basis, sys_yes_no } = toRefs<any>(proxy?.useDict('mall_settlement_basis', 'sys_yes_no'));

const pointsAccountList = ref<PointsAccountVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑

const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const pointsAccountFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: PointsAccountForm = {
  id: undefined,
  customerShopId: undefined,
  userPhone: undefined,
  settlementBasis: undefined,
  accountBalance: undefined,
  isWhitelist: undefined,
  remark: undefined
};
const data = reactive<PageData<PointsAccountForm, PointsAccountQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    customerShopId: undefined,
    userPhone: undefined,
    settlementBasis: undefined,
    accountBalance: undefined,
    isWhitelist: undefined,
    updateBy: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    customerShopId: [{ required: true, message: '客户店铺不能为空', trigger: 'blur' }],
    userPhone: [{ required: true, message: '用户手机号不能为空', trigger: 'blur' }],
    settlementBasis: [{ required: true, message: '结算依据不能为空', trigger: 'change' }],
    accountBalance: [{ required: true, message: '账户余额不能为空', trigger: 'blur' }],
    isWhitelist: [{ required: true, message: '是否白名单不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询积分账户列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listPointsAccount(queryParams.value);
  pointsAccountList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  pointsAccountFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: PointsAccountVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加积分账户';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: PointsAccountVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getPointsAccount(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改积分账户';
};

/** 提交按钮 */
const submitForm = () => {
  pointsAccountFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updatePointsAccount(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addPointsAccount(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: PointsAccountVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除积分账户编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delPointsAccount(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'recharge/pointsAccount/export',
    {
      ...queryParams.value
    },
    `pointsAccount_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
