<template>
  <div>
    <el-select
      v-model="selectedGoodsId"
      :placeholder="placeholder"
      filterable
      remote
      reserve-keyword
      :loading="goodsLoading"
      :remote-method="remoteGoodsListMethod"
      @change="handleGoodsChange"
      @keyup.enter="handleGoodsChange"
      required
      :disabled="disabled"
      clearable
      :customerShopId="customerShopId"
    >
      <el-option v-for="item in goodsOptions" :key="item.value" :label="item.mainLable" :value="item.value">
        <el-tooltip content="商品名称" placement="top">
          <span>{{ item.mainLable }}</span>
        </el-tooltip>
        <el-tooltip content="商品类型" placement="top">
          <span style="float: right; color: #999">{{ item.secondLable }}</span>
        </el-tooltip>
      </el-option>
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { getGoods } from '@/api/mall/goods';
import { listCandidateGoods } from '@/api/recharge/shopGoods';
import { useRemoteListMoreMethod } from '@/hooks/useBusiness/useSelectMore/remote';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 组件属性
const props = defineProps<{
  modelValue?: string | number; // v-model 绑定值
  disabled?: boolean; // 添加 disabled 属性
  placeholder?: string; // 搜索框提示
  customerShopId?: string | number; // 客户店铺id
}>();

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string | number];
  'change': [value: string | number];
}>();

// 组件内部状态
const selectedGoodsId = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const {
  list: goodsOptions,
  loading: goodsLoading,
  remoteMethod: remoteGoodsListMethod
} = useRemoteListMoreMethod(proxy, {
  FetchUrl: listCandidateGoods,
  value: 'id',
  mainLable: 'name',
  secondLable: 'goodsTypeLabel',
  desc: 'saleUnitLabel',
  queryStr: 'name',
  query: { pageNum: 1, pageSize: 10, name: '', customerShopId: props.customerShopId }
});

// 处理商品选择变化
const handleGoodsChange = (value: string | number) => {
  emit('change', value);
};

// 初始化默认值
const initDefaultValue = async (newValue) => {
  let currentItem = null;
  try {
    // 只在有 modelValue 时获取商品信息
    if (newValue) {
      const res = await getGoods(newValue);
      if (res.data) {
        currentItem = {
          value: res.data.id,
          mainLable: res.data.name,
          secondLable: res.data.goodsType,
          desc: res.data.saleUnit
        };
      }
    } else {
      currentItem = null;
    }
    return currentItem;
  } catch (error) {
    console.error('获取商品信息失败:', error);
  }
  return currentItem;
};

watch(
  () => props.modelValue,
  async (newValue) => {
    const currentItem = await initDefaultValue(newValue);
    await remoteGoodsListMethod('', '', '');
    if (currentItem !== null) {
      const isExist = goodsOptions.value.find((item) => item.value === currentItem.value);
      if (!isExist) {
        goodsOptions.value = [currentItem, ...goodsOptions.value];
      }
    }
  },
  { immediate: true }
);

defineExpose({
  remoteGoodsListMethod
});
</script>
