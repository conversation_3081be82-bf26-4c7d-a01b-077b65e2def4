// 客户店铺选择组件的支撑Hook
// 可以传入客户id，则只查询该客户的店铺
// 适合小数据量的查询场景

import { ref, computed } from 'vue';

import { listCustomerShop } from '@/api/recharge/customerShop';
import { CustomerShopVO, CustomerShopQuery } from '@/api/recharge/customerShop/types';

/**
 * 客户店铺选择Hook
 * @param props 参数选项，immediate表示是否立即加载数据
 * @returns 相关状态和方法
 */
export const useCustomerShopSelect = (props = { immediate: false }) => {
  // 数据状态
  const customerShopOptions = ref<CustomerShopVO[]>([]);
  const customerShopLoading = ref(false);
  const searchKeyword = ref('');

  /**
   * 过滤店铺选项
   * 根据搜索关键词过滤店铺名称
   */
  const filteredOptions = computed(() => {
    if (!searchKeyword.value) return customerShopOptions.value;

    return customerShopOptions.value.filter((item) => item.shopName?.toLowerCase().includes(searchKeyword.value.toLowerCase()));
  });

  /**
   * 加载店铺列表
   * @param params 查询参数，可以是客户ID或查询参数对象
   */
  const loadCustomerShopList = async (params?: string | number | CustomerShopQuery) => {
    try {
      // 显示加载状态
      customerShopLoading.value = true;

      // 构建查询参数
      let queryParams: CustomerShopQuery = { pageNum: 1, pageSize: 20 };

      // 根据传入参数类型设置查询条件
      if (typeof params === 'string' || typeof params === 'number') {
        // 如果传入的是字符串或数字，则视为customerId
        queryParams.customerId = params;
      } else if (params && typeof params === 'object') {
        // 如果传入的是对象，则合并查询条件
        queryParams = { ...queryParams, ...params };
      }

      // 调用API获取数据
      const res = await listCustomerShop(queryParams);
      customerShopOptions.value = res.rows;
    } catch (error) {
      console.error('加载店铺列表失败:', error);
      customerShopOptions.value = [];
    } finally {
      // 无论成功失败都结束加载状态
      customerShopLoading.value = false;
    }
  };

  // 如果需要立即加载数据
  if (props.immediate) {
    loadCustomerShopList();
  }

  return { customerShopOptions, customerShopLoading, filteredOptions, loadCustomerShopList };
};
