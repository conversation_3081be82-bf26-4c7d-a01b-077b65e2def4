<template>
  <el-dialog v-model="dialog.visible" :title="dialog.title" width="90%" append-to-body destroy-on-close @close="handleClose" draggable>
    <!-- 搜索框 -->
    <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
      <el-form-item label="商品名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入商品名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="销售状态" prop="saleStatus">
        <el-select v-model="queryParams.saleStatus" placeholder="请选择销售状态" clearable @change="handleQuery">
          <el-option v-for="dict in mall_sale_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="商品标签" prop="tags">
        <dict-select v-model="queryParams.tags" dict-key="mall_goods_tag" placeholder="请选择商品标签" clearable @change="handleQuery" />
      </el-form-item>
      <!-- <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item> -->
    </el-form>
    <!-- 列表 -->
    <el-table ref="candidateGoodsTableRef" v-loading="loading" :data="goodsList" border @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="主图" prop="name" width="80" align="center">
        <template #default="scope">
          <image-preview :src="scope.row.imagesUrl" :width="50" :height="50" />
        </template>
      </el-table-column>
      <el-table-column label="商品名称" prop="name">
        <template #default="scope">
          <el-button type="text" @click="openGoodsDetailInEdit(scope.row)">
            {{ scope.row.name }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="销售单位" prop="saleUnit" width="80" align="center">
        <template #default="scope">
          <dict-tag :options="mall_goods_unit" :value="scope.row.saleUnit" />
        </template>
      </el-table-column>
      <!-- quoteStrategy: 01-成本价加点，02-零售价打折 -->
      <el-table-column label="零售价(元)" width="100" align="right" v-if="props.customerShop.quoteStrategy === '02'">
        <template #header>
          <span>零售价(元)</span>
          <el-tooltip content="商品管理中设置" placement="top">
            <el-icon>
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
        </template>
        <template #default="scope">
          <span style="color: #f56c6c">{{ centToYuan(scope.row.currentSalePrice) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="成本价(元)" width="100" align="right" v-if="props.customerShop.quoteStrategy === '01'">
        <template #header>
          <span>成本价(元)</span>
          <el-tooltip content="商品管理中设置" placement="top">
            <el-icon>
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
        </template>
        <template #default="scope">
          <span style="color: #f56c6c">{{ centToYuan(scope.row.currentCostPrice) }}</span>
        </template>
      </el-table-column>
      <!-- settlementBasis: 1-按售卡结算，2-按提交结算 -->
      <el-table-column label="结算价(元)" width="100" v-if="props.customerShop.settlementBasis === '2'">
        <template #header>
          <span>结算价(元)</span>
          <el-tooltip
            :content="
              props.customerShop.quoteStrategy === '01'
                ? `结算价 = 成本价 * (1 + 成本价加点「${props.customerShop.settlePriceMakeup}%」)`
                : `结算价 = 零售价 * 零售价打折「${props.customerShop.settlePriceDiscount}%」`
            "
            placement="top"
          >
            <el-icon>
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
        </template>
        <template #default="scope">
          <span style="color: #67c23a">{{ centToYuan(Number(calculateSettlementPrice(scope.row))) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="销售价(元)" width="100">
        <template #header>
          <span>销售价(元)</span>
          <el-tooltip
            :content="
              props.customerShop.quoteStrategy === '01'
                ? `销售价 = 结算价 * (1 + 销售价加点「${props.customerShop.sellingPriceMakeup}%」)`
                : `销售价 = 零售价 * 零售价打折「${props.customerShop.sellingPriceDiscount}%」`
            "
            placement="top"
          >
            <el-icon>
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
        </template>
        <template #default="scope">
          <span style="color: #67c23a">{{ centToYuan(Number(calculateSellingPrice(scope.row))) }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    <!-- 弹窗提交操作 -->
    <template #footer>
      <el-tooltip content="刷新列表数据" placement="top">
        <el-button @click="handleQuery" icon="Refresh" />
      </el-tooltip>
      <el-button :loading="buttonLoading" type="primary" @click="submitForm" :disabled="submitGoodsListForm.length === 0">确定</el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, getCurrentInstance, toRefs } from 'vue';
import { CandidateGoodsQuery, ShopGoodsForm } from '@/api/recharge/shopGoods/types';
import { listCandidateGoods, batchAddShopGoods } from '@/api/recharge/shopGoods';
import { CustomerShopVO } from '@/api/recharge/customerShop/types';
import { GoodsSimpleVO } from '@/api/mall/goods/types';
import { GoodsVO } from '@/api/mall/goods/types';

import { centToYuan } from '@/utils/moneyUtils';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_sale_status, sys_yes_no } = toRefs<any>(proxy?.useDict('mall_sale_status', 'sys_yes_no'));
const { mall_goods_tag, mall_goods_unit } = toRefs<any>(proxy?.useDict('mall_goods_tag', 'mall_goods_unit'));
const dialog = ref({
  visible: false,
  title: '批量添加店铺商品'
});

const loading = ref(false);
const total = ref(0);
const goodsList = ref<GoodsVO[]>([]);
const buttonLoading = ref(false);

const candidateGoodsTableRef = ref(null);
// 定义商品列表接收数据的接口
const submitGoodsListForm = ref<ShopGoodsForm[]>([]);

const props = defineProps<{
  customerShop: CustomerShopVO;
}>();

const queryFormRef = ref<ElFormInstance>();
const queryParams = ref<CandidateGoodsQuery>({
  pageNum: 1,
  pageSize: 10,
  name: undefined,
  saleStatus: '0',
  tags: undefined,
  customerShopId: props.customerShop.id
});

const emit = defineEmits(['refresh']);

const handleClose = () => {
  submitGoodsListForm.value = [];
  dialog.value.visible = false;
  emit('refresh');
};

const openDialog = () => {
  dialog.value.visible = true;
  dialog.value.title = '给「' + props.customerShop.shopName + '」批量添加商品';
  getList();
};

const handleQuery = () => {
  // 实现查询逻辑
  console.log('handleQuery', queryParams.value);
  getList();
};

const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 提交表单 */
const submitForm = async () => {
  // console.log('submitForm', submitGoodsListForm.value);
  buttonLoading.value = true;
  try {
    const res = await batchAddShopGoods(submitGoodsListForm.value);
    if (res && res.code === 200) {
      proxy?.$modal.msgSuccess('操作成功');
      handleClose();
    } else {
      proxy?.$modal.msgError(res?.msg || '操作失败');
    }
  } catch (e) {
    proxy?.$modal.msgError('网络异常或服务器无响应');
  } finally {
    buttonLoading.value = false;
  }
};

/** 查询商品列表 */
const getList = async () => {
  queryParams.value.params = {};
  const res = await listCandidateGoods(queryParams.value);
  goodsList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/**
 * 处理复选框变化
 * 结算依据：1-按售卡结算，2-按提货结算
 * 报价策略：01-成本价加点，02-零售价打折
 * 结算价 = 成本价 * (1 + 成本价加点) 或 零售价 * 零售价打折
 * 销售价 = 结算价 * (1 + 销售价加点) 或 零售价 * 零售价打折
 */
const handleSelectionChange = (selection: any[]) => {
  submitGoodsListForm.value = selection.map((row) => ({
    customerShopId: props.customerShop.id,
    goodsId: row.id,
    settlePrice: Number(calculateSettlementPrice(row)),
    sellingPrice: Number(calculateSellingPrice(row)),
    isShow: 'N'
  }));
  console.log('submitGoodsListForm', submitGoodsListForm.value);
  // console.log('props.customerShop', props.customerShop);
};

/** 打开商品详情 */
const openGoodsDetailInEdit = (goods: GoodsSimpleVO) => {
  console.log('goods', goods.id);
  const routeUrl = `/mall/goods/goods?id=${goods.id}&openEdit=true`;
  window.open(routeUrl, '_blank');
};

/** 计算结算价 */
const calculateSettlementPrice = (row: GoodsSimpleVO) => {
  if (props.customerShop.quoteStrategy === '01') {
    const settlementPrice = row.currentCostPrice * (1 + props.customerShop.settlePriceMakeup / 100);
    return settlementPrice.toFixed(0);
  } else {
    const settlementPrice = (row.currentSalePrice * props.customerShop.settlePriceDiscount) / 100;
    return settlementPrice.toFixed(0);
  }
};

/** 计算销售价 */
const calculateSellingPrice = (row: GoodsSimpleVO) => {
  if (props.customerShop.quoteStrategy === '01') {
    const sellingPrice = row.currentCostPrice * (1 + props.customerShop.settlePriceMakeup / 100) * (1 + props.customerShop.sellingPriceMakeup / 100);
    return sellingPrice.toFixed(0);
  } else {
    const sellingPrice = (row.currentSalePrice * props.customerShop.sellingPriceDiscount) / 100;
    return sellingPrice.toFixed(0);
  }
};

onMounted(() => {
  getList();
});

defineExpose({
  openDialog
});
</script>
