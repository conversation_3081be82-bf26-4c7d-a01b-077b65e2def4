<template>
  <div>
    <!-- 新增和搜索 -->
    <div class="mb-2" style="display: flex; justify-content: space-between; align-items: center" gap="10px">
      <el-row :gutter="10">
        <el-col :span="1.5">
          <el-button type="primary" @click="handleAdd">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['recharge:pointsStream:export']">导出</el-button>
        </el-col>
        <el-col :span="6">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="" style="width: 308px">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <el-tooltip content="刷新" placement="top">
        <el-link type="primary" icon="Refresh" @click="getList" />
      </el-tooltip>
    </div>

    <!-- 列表 -->
    <div>
      <el-table v-loading="loading" :data="pointsStreamList" @selection-change="handleSelectionChange" border>
        <el-table-column type="index" width="60" />
        <el-table-column label="ID" prop="id" v-if="true" width="180" />
        <!-- <el-table-column label="积分账户" prop="pointsAccountId" /> -->
        <el-table-column label="变化时间" prop="updateTime" width="160">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="流水类型" prop="streamType" width="100">
          <template #default="scope">
            <dict-tag :options="mall_points_stream_type" :value="scope.row.streamType" />
          </template>
        </el-table-column>
        <el-table-column label="流水原因" prop="streamReason" width="100">
          <template #default="scope">
            <dict-tag :options="mall_points_stream_reason" :value="scope.row.streamReason" />
          </template>
        </el-table-column>
        <el-table-column label="流水金额(元)" prop="streamAmount" width="100" align="right">
          <template #default="scope">
            <span :style="{ color: scope.row.streamAmount < 0 ? 'red' : 'inherit' }">{{ centToYuan(scope.row.streamAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="变化前余额(元)" prop="balanceBefore" width="120" align="right">
          <template #default="scope">
            {{ centToYuan(scope.row.balanceBefore) }}
          </template>
        </el-table-column>
        <el-table-column label="变化后余额(元)" prop="balanceAfter" width="120" align="right">
          <template #default="scope">
            {{ centToYuan(scope.row.balanceAfter) }}
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="200" show-overflow-tooltip />
        <!-- <el-table-column label="更新者" prop="updateBy" /> -->
        <!-- <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="100">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['recharge:pointsStream:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['recharge:pointsStream:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column> -->
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>
    <!-- 添加或修改积分流水对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body @close="cancel" draggable>
      <el-form ref="pointsStreamFormRef" :model="form" :rules="rules" label-width="100px" label-position="top">
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="流水原因" prop="streamReason">
              <dict-select v-model="form.streamReason" placeholder="请选择流水原因" dict-key="mall_points_stream_reason" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="流水类型" prop="streamType">
              <el-select v-model="form.streamType" placeholder="请选择流水类型" clearable>
                <el-option v-for="dict in mall_points_stream_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="流水金额（单位元）" prop="streamAmount">
              <el-input-number
                placeholder="请输入流水金额"
                :min="0"
                :max="99999.99"
                :step="0.01"
                :disabled="form.streamType == null"
                :precision="2"
                @change="(val) => (form.streamAmount = val ? yuanToCent(Number(val)) : undefined)"
                :model-value="form.streamAmount ? Number(centToYuan(form.streamAmount)) : undefined"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="变化前余额" prop="balanceBefore">
              <el-input :model-value="centToYuan(form.balanceBefore)" placeholder="请输入变化前余额" disabled>
                <template #append>元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="变化后余额" prop="balanceAfter">
              <el-input
                placeholder="系统自动计算"
                disabled
                @blur="(val) => (form.balanceAfter = val ? yuanToCent(Number(val)) : undefined)"
                :model-value="form.balanceAfter ? Number(centToYuan(form.balanceAfter)) : undefined"
              >
                <template #append>元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="充值人手机" prop="rechargeOpPhone">
              <el-input v-model="form.rechargeOpPhone" placeholder="请输入充值人手机" maxlength="11" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" placeholder="请输入备注" type="textarea" :rows="3" maxlength="200" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { listPointsStream, getPointsStream, delPointsStream, addPointsStream, updatePointsStream } from '@/api/recharge/pointsStream';
import { PointsStreamVO, PointsStreamQuery, PointsStreamForm } from '@/api/recharge/pointsStream/types';
import { PointsAccountVO } from '@/api/recharge/pointsAccount/types';

import { centToYuan, yuanToCent } from '@/utils/moneyUtils';

const props = defineProps<{
  pointsAccount: PointsAccountVO;
  customerShopId: string | number;
}>();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_points_stream_type, mall_points_stream_reason } = toRefs<any>(proxy?.useDict('mall_points_stream_type', 'mall_points_stream_reason'));

const pointsStreamList = ref<PointsStreamVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑

const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const pointsStreamFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: PointsStreamForm = {
  id: undefined,
  pointsAccountId: props.pointsAccount.id,
  customerShopId: props.customerShopId,
  rechargeOpPhone: undefined,
  streamType: undefined,
  streamReason: undefined,
  streamAmount: undefined,
  balanceBefore: props.pointsAccount.accountBalance,
  balanceAfter: undefined,
  remark: undefined
};
const data = reactive<PageData<PointsStreamForm, PointsStreamQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    pointsAccountId: props.pointsAccount.id,
    customerShopId: props.customerShopId,
    rechargeOpPhone: undefined,
    streamType: undefined,
    streamReason: undefined,
    streamAmount: undefined,
    balanceBefore: undefined,
    balanceAfter: undefined,
    updateBy: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    rechargeOpPhone: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的11位手机号', trigger: 'blur' }],
    pointsAccountId: [{ required: true, message: '积分账户不能为空', trigger: 'blur' }],
    streamType: [
      { required: true, message: '流水类型不能为空', trigger: 'change' },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (form.value.streamType === '2' && Number(value) > Number(form.value.balanceBefore)) {
            callback(new Error('扣减金额不能大于变化前余额'));
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ],
    streamReason: [{ required: true, message: '流水原因不能为空', trigger: 'change' }],
    streamAmount: [
      { required: true, message: '流水金额不能为空', trigger: 'change' },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (form.value.streamType === '2' && Number(value) > Number(form.value.balanceBefore)) {
            callback(new Error('扣减金额不能大于变化前余额'));
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询积分流水列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listPointsStream(queryParams.value);
  pointsStreamList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  pointsStreamFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: PointsStreamVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '给 「' + props.pointsAccount.userPhone + '」创建积分流水记录';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: PointsStreamVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getPointsStream(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改积分流水 「' + props.pointsAccount.userPhone + '」';
};

/** 提交按钮 */
const submitForm = () => {
  pointsStreamFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      // 处理金额转换
      const submitForm = { ...form.value };
      if (submitForm.streamType === '2') {
        // 扣减时转为负数
        submitForm.streamAmount = -Number(submitForm.streamAmount);
      }
      // 金额已经是以分为单位，不需要再次转换
      if (submitForm.id) {
        await updatePointsStream(submitForm).finally(() => (buttonLoading.value = false));
      } else {
        await addPointsStream(submitForm).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: PointsStreamVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除积分流水编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delPointsStream(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'recharge/pointsStream/export',
    {
      ...queryParams.value
    },
    `pointsStream_${new Date().getTime()}.xlsx`
  );
};

// 监听流水类型和流水金额的变化
watch(
  () => [form.value.streamType, form.value.streamAmount],
  ([newType, newAmount]) => {
    if (newType && newAmount) {
      const amount = Number(newAmount);
      const before = Number(form.value.balanceBefore);
      if (newType === '1') {
        // 增加
        form.value.balanceAfter = Number((before + amount).toFixed(2));
      } else if (newType === '2') {
        // 扣减
        form.value.balanceAfter = Number((before - amount).toFixed(2));
      }
    }
  },
  { immediate: true }
);

onMounted(() => {
  getList();
});
</script>
