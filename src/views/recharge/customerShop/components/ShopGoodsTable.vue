<template>
  <!-- 新增和搜索 -->
  <div class="mb-2" style="display: flex; justify-content: space-between; align-items: center" gap="10px">
    <el-row :gutter="10">
      <!-- <el-col :span="1.5">
        <el-button type="primary" @click="handleAddOneByOne">单增</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="primary" @click="handleAddBatch">批增</el-button>
      </el-col>
      <el-col :span="15">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container" style="display: flex; gap: 8px">
          <el-form-item label="" prop="goodsId" style="margin-right: 0">
            <goods-select v-model="queryParams.goodsId" placeholder="搜索商品" @change="handleQuery" />
          </el-form-item>
          <el-form-item label="" prop="isShow" style="margin-right: 0">
            <el-select v-model="queryParams.isShow" placeholder="请选择是否展示商品" clearable @change="handleQuery">
              <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>

    <el-tooltip content="刷新" placement="top">
      <el-link type="primary" icon="Refresh" @click="getList" />
    </el-tooltip>
  </div>
  <!-- 列表 -->
  <div>
    <el-table v-loading="loading" :data="shopGoodsList" @selection-change="handleSelectionChange" border>
      <el-table-column type="index" label="#" width="50" />
      <el-table-column label="id" prop="id" v-if="false" />
      <el-table-column label="图片" width="80" align="center">
        <template #default="scope">
          <image-preview :src="scope.row.goods.imagesUrl" :width="50" :height="50" />
        </template>
      </el-table-column>
      <el-table-column label="商品名称" min-width="300" show-overflow-tooltip>
        <template #default="scope">
          <el-tooltip content="商品名称" placement="top">
            <el-link type="primary" @click="openGoodsDetailInEdit(scope.row.goods)">
              {{ scope.row.goods.name }}
            </el-link>
          </el-tooltip>
          <div style="font-size: 12px; color: #999">
            <el-tooltip content="前端二级分类" placement="top">
              <span>{{ scope.row.goods.frontCategoryLabel || '<未设置前端分类>' }}</span>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="销售状态" prop="saleStatus" width="80">
        <template #default="scope">
          <el-tooltip content="统一在商品信息中修改,商品名称上有快捷操作入口" placement="top">
            <dict-tag :options="mall_sale_status" :value="scope.row.goods.saleStatus" />
          </el-tooltip>
        </template>
      </el-table-column> -->
      <el-table-column label="是否展示" prop="isShow" width="80">
        <template #default="scope">
          <!-- <dict-tag :options="sys_yes_no" :value="scope.row.isShow" /> -->
          <el-switch v-model="scope.row.isShow" active-value="Y" inactive-value="N" @change="handleIsShowChange(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="结算价(元)" prop="settlePrice" width="120" v-if="isShowSettlePrice">
        <template #header>
          <span>结算价(元)</span>
          <el-tooltip content="面向B端的价格，生成商品订单时的结算价从这里取值" placement="top">
            <el-icon :size="16" class="el-icon--right"><QuestionFilled /></el-icon>
          </el-tooltip>
        </template>
        <template #default="scope">
          <div class="price-cell">
            <el-popover
              v-model:visible="scope.row.settlePopoverVisible"
              placement="top"
              :width="200"
              trigger="click"
              popper-class="price-popover"
              @before-enter="
                () => {
                  scope.row.tempSettlePrice = scope.row.settlePrice ? Number(centToYuan(scope.row.settlePrice)) : undefined;
                }
              "
            >
              <template #default>
                <div style="margin-bottom: 10px">
                  <el-input
                    v-model="scope.row.tempSettlePrice"
                    placeholder="请输入结算价，支持两位小数"
                    type="number"
                    style="width: 100%"
                    @change="(val) => (scope.row.tempSettlePrice = val)"
                    clearable
                    @clear="handleSettlePriceClear(scope.row)"
                  />
                </div>
                <div style="text-align: right">
                  <el-button size="small" @click="scope.row.settlePopoverVisible = false">取消</el-button>
                  <el-button size="small" type="primary" @click="handleSettlePriceConfirm(scope.row)">确认</el-button>
                </div>
              </template>
              <template #reference>
                <div class="price-value">
                  <span>{{ scope.row.settlePrice ? centToYuan(scope.row.settlePrice) : '<未设置>' }}</span>
                  <span style="font-size: 12px; color: #999" v-if="scope.row.settlePrice"> 元/{{ scope.row.goods.saleUnitLabel }}</span>
                  <el-icon class="edit-icon"><Edit /></el-icon>
                </div>
              </template>
            </el-popover>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="销售价(元) " prop="sellingPrice" width="120">
        <template #header>
          <span>销售价(元)</span>
          <el-tooltip content="面向C端的价格，C端下单商品订单时的价格" placement="top">
            <el-icon :size="16" class="el-icon--right"><QuestionFilled /></el-icon>
          </el-tooltip>
        </template>
        <template #default="scope">
          <div class="price-cell">
            <el-popover
              v-model:visible="scope.row.popoverVisible"
              placement="top"
              :width="200"
              trigger="click"
              popper-class="price-popover"
              @before-enter="
                () => {
                  scope.row.tempPrice = Number(centToYuan(scope.row.sellingPrice));
                }
              "
            >
              <template #default>
                <div style="margin-bottom: 10px">
                  <el-input
                    v-model="scope.row.tempPrice"
                    placeholder="请输入销售价，支持两位小数"
                    type="number"
                    style="width: 100%"
                    @change="(val) => (scope.row.tempPrice = val)"
                    clearable
                    @clear="handleSellingPriceClear(scope.row)"
                  />
                </div>
                <div style="text-align: right">
                  <el-button size="small" @click="scope.row.popoverVisible = false">取消</el-button>
                  <el-button size="small" type="primary" @click="handlePriceConfirm(scope.row)">确认</el-button>
                </div>
              </template>
              <template #reference>
                <div class="price-value">
                  <span>{{ centToYuan(scope.row.sellingPrice) }}</span>
                  <span style="font-size: 12px; color: #999"> 元/{{ scope.row.goods.saleUnitLabel }}</span>
                  <el-icon class="edit-icon"><Edit /></el-icon>
                </div>
              </template>
            </el-popover>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="零售价（元）" prop="currentSalePrice" width="170" v-if="props.customerShop.quoteStrategy === '02'">
        <template #default="scope">
          <span>{{ centToYuan(scope.row.goods.currentSalePrice) }}</span>
          <div>
            <span v-if="scope.row.settlePrice && scope.row.goods.currentSalePrice" style="color: #67c23a">
              <el-tooltip content="结算价/零售价" placement="top" v-if="isShowSettlePrice">
                {{ ((scope.row.settlePrice / scope.row.goods.currentSalePrice) * 100).toFixed(2) }}%
              </el-tooltip>
            </span>
            <span v-else>
              <el-tooltip content="未设结算价" placement="top" v-if="isShowSettlePrice">
                <span style="color: #67c23a; font-size: 12px">未设</span>
              </el-tooltip>
            </span>
            <span v-if="isShowSettlePrice"> / </span>
            <span v-if="scope.row.sellingPrice && scope.row.goods.currentSalePrice" style="color: #f56c6c">
              <el-tooltip content="销售价/零售价" placement="top">
                {{ ((scope.row.sellingPrice / scope.row.goods.currentSalePrice) * 100).toFixed(2) }}%
              </el-tooltip>
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="成本价（元）" prop="currentCostPrice" width="170" v-if="props.customerShop.quoteStrategy === '01'">
        <template #default="scope">
          <span>{{ centToYuan(scope.row.goods.currentCostPrice) }}</span>
          <div>
            <span v-if="scope.row.settlePrice && scope.row.goods.currentCostPrice" style="color: #67c23a">
              <el-tooltip content="结算价/成本价-1" placement="top" v-if="isShowSettlePrice">
                {{ ((scope.row.settlePrice / scope.row.goods.currentCostPrice - 1) * 100).toFixed(2) }}%
              </el-tooltip>
            </span>
            <span v-else>
              <el-tooltip content="未设结算价" placement="top" v-if="isShowSettlePrice">
                <span style="color: #67c23a; font-size: 12px">未设</span>
              </el-tooltip>
            </span>
            <span v-if="isShowSettlePrice"> / </span>
            <span v-if="scope.row.sellingPrice && scope.row.goods.currentCostPrice" style="color: #f56c6c">
              <el-tooltip content="销售价/成本价-1" placement="top">
                {{ ((scope.row.sellingPrice / scope.row.goods.currentCostPrice - 1) * 100).toFixed(2) }}%
              </el-tooltip>
            </span>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="备注" prop="remark" show-overflow-tooltip min-width="150" /> -->
      <el-table-column label="更新者" prop="updateNickName" width="120" show-overflow-tooltip />
      <el-table-column label="更新时间" prop="updateTime" width="110">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" class-name="small-padding fixed-width" fixed="right" width="100">
        <template #default="scope">
          <!-- <el-tooltip content="修改" placement="top">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['recharge:shopGoods:edit']"></el-button>
          </el-tooltip> -->
          <el-tooltip content="删除" placement="top">
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['recharge:shopGoods:remove']"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改客户店铺明细对话框 -->
    <!-- <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body @close="cancel" draggable destroy-on-close>
      <el-form ref="shopGoodsFormRef" :model="form" :rules="rules" label-width="120px" label-position="top">
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="商品" prop="goodsId">
              <candidate-goods-select
                ref="candidateGoodsSelectRef"
                v-model="form.goodsId"
                placeholder="搜索商品"
                style="width: 100%"
                :disabled="dialogEditStatus"
                @change="handleGoodsChange"
                :customerShopId="customerShopId"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="是否展示" prop="isShow">
              <el-select v-model="form.isShow" placeholder="请选择是否展示" clearable @change="handleGoodsChange">
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结算价" prop="settlePrice">
              <template #label>
                <span>结算价(元）</span>
                <el-tooltip content="跟B结算的价格,提货模式下必填" placement="top">
                  <el-icon :size="16" class="el-icon--right"><InfoFilled /></el-icon>
                </el-tooltip>
              </template>
              <el-input-number
                placeholder="结算价"
                :min="0.01"
                :max="9999.99"
                :step="0.01"
                @change="(val) => (form.settlePrice = val ? yuanToCent(val) : undefined)"
                :model-value="form.settlePrice ? Number(centToYuan(form.settlePrice)) : undefined"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="销售价，C下单价" prop="sellingPrice">
              <template #label>
                <span>销售价(元）</span>
                <el-tooltip content="C下单的支付价格" placement="top">
                  <el-icon :size="16" class="el-icon--right"><InfoFilled /></el-icon>
                </el-tooltip>
              </template>
              <el-input-number
                placeholder="销售价"
                :min="0"
                :max="9999.99"
                :step="1"
                :precision="2"
                @change="(val) => (form.sellingPrice = val ? yuanToCent(val) : undefined)"
                :model-value="form.sellingPrice ? Number(centToYuan(form.sellingPrice)) : undefined"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" placeholder="请输入备注" type="textarea" :rows="3" :maxlength="200" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog> -->
  </div>

  <!-- 批量添加候选商品 -->
  <candidate-goods-add-dialog ref="candidateGoodsAddDialogRef" :customerShop="customerShop" @refresh="getList" />
</template>

<script setup lang="ts">
import { listShopGoods, getShopGoods, delShopGoods, addShopGoods, updateShopGoods, checkUnique } from '@/api/recharge/shopGoods';
import { ShopGoodsVO, ShopGoodsQuery, ShopGoodsForm } from '@/api/recharge/shopGoods/types';
import { CustomerShopVO } from '@/api/recharge/customerShop/types';
import { GoodsSimpleVO } from '@/api/mall/goods/types';
import { centToYuan, yuanToCent } from '@/utils/moneyUtils';
import { parseTime } from '@/utils/ruoyi';

import CandidateGoodsSelect from '@/views/recharge/customerShop/assist/CandidateGoodsSelect.vue';

import CandidateGoodsAddDialog from './CandidateGoodsAddDialog.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_sale_status, sys_yes_no } = toRefs<any>(proxy?.useDict('mall_sale_status', 'sys_yes_no'));

const props = defineProps<{
  customerShopId: string | number;
  customerShop: CustomerShopVO;
}>();

const candidateGoodsSelectRef = ref(null);

// 继承ShopGoodsVO
interface ShopGoodsVOWithTemp extends ShopGoodsVO {
  tempSettlePrice?: number;
  tempPrice?: number;
  popoverVisible?: boolean;
  settlePopoverVisible?: boolean;
}

const shopGoodsList = ref<ShopGoodsVOWithTemp[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑
const isShowSettlePrice = ref(props.customerShop.settlementBasis === '2'); // props.customerShop.settlementBasis === '2'为真

const candidateGoodsAddDialogRef = ref(null);

const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const shopGoodsFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ShopGoodsForm = {
  id: undefined,
  customerShopId: props.customerShopId,
  goodsId: undefined,
  settlePrice: undefined,
  sellingPrice: undefined,
  isShow: 'Y',
  remark: undefined
};
const data = reactive<PageData<ShopGoodsForm, ShopGoodsQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 5,
    customerShopId: props.customerShopId,
    goodsId: undefined,
    settlePrice: undefined,
    sellingPrice: undefined,
    updateBy: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    customerShopId: [{ required: true, message: '客户店铺不能为空', trigger: 'blur' }],
    goodsId: [
      { required: true, message: '商品不能为空', trigger: 'change' },
      {
        validator: (rule, value, callback) => {
          if (value) {
            queryUnique.id = form.value.id;
            queryUnique.goodsId = form.value.goodsId;
            queryUnique.customerShopId = form.value.customerShopId;
            checkUnique(queryUnique)
              .then((res) => {
                if (res.data) {
                  callback(new Error('商品已存在店铺中'));
                } else {
                  callback();
                }
              })
              .catch(() => {
                callback(new Error('唯一性校验失败'));
              });
          } else {
            callback();
          }
        },
        trigger: 'change'
      }
    ],
    settlePrice: [
      {
        // required: true,
        message: '结算价，在提货结算模式下不能为空',
        trigger: 'blur',
        validator: (rule, value, callback) => {
          if (props.customerShop.settlementBasis === '2' && !value) {
            callback(new Error('结算价，在提货结算模式下不能为空'));
          } else {
            callback();
          }
        }
      }
    ],
    sellingPrice: [
      {
        required: true,
        message: '销售价，C下单价不能为空',
        trigger: 'blur'
      }
    ],
    isShow: [{ required: true, message: '是否展示不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 校验唯一性查询体 */
const queryUnique: ShopGoodsForm = {
  id: undefined,
  customerShopId: undefined,
  goodsId: undefined
};

/** 查询客户店铺明细列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listShopGoods(queryParams.value);
  shopGoodsList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  shopGoodsFormRef.value?.resetFields();
  dialogEditStatus.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ShopGoodsVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAddOneByOne = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加商品报价明细';
  candidateGoodsSelectRef.value?.remoteGoodsListMethod('', '', '');
  dialogEditStatus.value = false;
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ShopGoodsVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getShopGoods(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialogEditStatus.value = true;
  dialog.title = '修改商品报价明细';
};

/** 提交按钮 */
const submitForm = () => {
  shopGoodsFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateShopGoods(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addShopGoods(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ShopGoodsVO) => {
  await proxy?.$modal
    .confirm('是否确认删除名称为"' + row?.goods.name + '"的店铺商品？删除后你可以重新添加回来。')
    .finally(() => (loading.value = false));
  await delShopGoods(row?.id);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 商品选择变化 */
const handleGoodsChange = (value: string | number) => {
  console.log(value);
  console.log('customerShop', props.customerShop);
};

/** 打开商品详情 */
const openGoodsDetailInEdit = (goods: GoodsSimpleVO) => {
  console.log('goods', goods.id);
  const routeUrl = `/mall/goods/goods?id=${goods.id}&openEdit=true`;
  window.open(routeUrl, '_blank');
};

/** 批量添加候选商品 */
const handleAddBatch = () => {
  candidateGoodsAddDialogRef.value?.openDialog();
};

/** 确认修改价格 */
const handlePriceConfirm = async (row: ShopGoodsVOWithTemp) => {
  try {
    const newPrice = yuanToCent(row.tempPrice);
    await updateShopGoods({
      id: row.id,
      customerShopId: row.customerShopId,
      goodsId: row.goodsId,
      sellingPrice: newPrice,
      settlePrice: row.settlePrice,
      isShow: row.isShow
    });
    row.popoverVisible = false;
    proxy?.$modal.msgSuccess('修改成功');
    await getList();
  } catch (error) {
    proxy?.$modal.msgError('修改失败');
  }
};

/** 确认修改结算价 */
const handleSettlePriceConfirm = async (row: ShopGoodsVOWithTemp) => {
  try {
    const newPrice = row.tempSettlePrice ? yuanToCent(row.tempSettlePrice) : undefined;
    await updateShopGoods({
      id: row.id,
      customerShopId: row.customerShopId,
      goodsId: row.goodsId,
      sellingPrice: row.sellingPrice,
      settlePrice: newPrice,
      isShow: row.isShow
    });
    row.settlePopoverVisible = false;
    proxy?.$modal.msgSuccess('修改成功');
    await getList();
  } catch (error) {
    proxy?.$modal.msgError('修改失败');
  }
};

/** 是否展示切换 */
const handleIsShowChange = async (row: ShopGoodsVOWithTemp) => {
  // console.log('row', row);
  try {
    await updateShopGoods(row);
    proxy?.$modal.msgSuccess('操作成功');
  } catch (err) {
    proxy?.$modal.msgError('操作失败');
  }
  await getList();
};

/** 结算价清空 */
const handleSettlePriceClear = (row: ShopGoodsVOWithTemp) => {
  // 01: 成本价加点；02: 成本价加价；
  if (props.customerShop.quoteStrategy === '01') {
    row.settlePrice = Number((row.goods.currentCostPrice * (1 + props.customerShop.settlePriceMakeup / 100)).toFixed(2));
  } else if (props.customerShop.quoteStrategy === '02') {
    row.settlePrice = Number(((row.goods.currentSalePrice * props.customerShop.settlePriceDiscount) / 100).toFixed(2));
  }
  row.tempSettlePrice = row.settlePrice ? Number(centToYuan(row.settlePrice)) : undefined;
};

/** 销售价清空 */
const handleSellingPriceClear = (row: ShopGoodsVOWithTemp) => {
  // 01: 成本价加点；02: 成本价加价；
  if (props.customerShop.quoteStrategy === '01') {
    row.sellingPrice = Number(
      (row.goods.currentCostPrice * (1 + props.customerShop.sellingPriceMakeup / 100) * (1 + props.customerShop.settlePriceMakeup / 100)).toFixed(2)
    );
  } else if (props.customerShop.quoteStrategy === '02') {
    row.sellingPrice = Number(((row.goods.currentSalePrice * props.customerShop.sellingPriceDiscount) / 100).toFixed(2));
  }
  row.tempPrice = row.sellingPrice ? Number(centToYuan(row.sellingPrice)) : undefined;
};

onMounted(() => {
  getList();
});
</script>

<style scoped>
.price-cell {
  width: 100%;
  height: 100%;
  position: relative;
}

.price-value {
  position: relative;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.price-value:hover .edit-icon {
  display: inline-flex;
}

.edit-icon {
  display: none;
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  color: #409eff;
  font-size: 16px;
}
</style>
