<template>
  <el-dialog :title="dialog.title" v-model="dialog.visible" width="80%" append-to-body destroy-on-close @close="handleClose" draggable>
    <point-stream-table :pointsAccount="pointsAccount" :customerShopId="customerShopId" />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import PointStreamTable from './PointStreamTable.vue';
import { PointsAccountVO } from '@/api/recharge/pointsAccount/types';

const pointsAccount = ref<PointsAccountVO>();
const customerShopId = ref<string | number | undefined>();

const dialog = ref({
  visible: false,
  title: ''
});

const emit = defineEmits(['refresh']);

const handleClose = () => {
  dialog.value.visible = false;
  emit('refresh');
};

const openDialog = async (row: PointsAccountVO) => {
  dialog.value.visible = true;
  dialog.value.title = '积分账户「 ' + row.userPhone + ' 」的流水';
  pointsAccount.value = row;
  customerShopId.value = row.customerShopId;
};

defineExpose({
  openDialog
});
</script>
