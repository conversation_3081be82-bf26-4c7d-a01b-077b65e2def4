<template>
  <el-dialog :title="dialog.title" v-model="dialog.visible" width="80%" append-to-body destroy-on-close @close="handleClose" draggable>
    <shop-goods-table :customerShopId="customerShopId" :customerShop="customerShop" />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ShopGoodsTable from './ShopGoodsTable.vue';
import { CustomerShopVO } from '@/api/recharge/customerShop/types';

const customerShopId = ref<string | number | undefined>();
const customerShop = ref<CustomerShopVO>();

const dialog = ref({
  visible: false,
  title: ''
});

const emit = defineEmits(['refresh']);

const handleClose = () => {
  dialog.value.visible = false;
  emit('refresh');
};

const openDialog = async (row: CustomerShopVO) => {
  dialog.value.visible = true;
  dialog.value.title = '给客户「 ' + row.customerName + ' 」的店铺「 ' + row.shopName + ' 」添加商品';
  customerShopId.value = row.id;
  customerShop.value = row;
};

defineExpose({
  openDialog
});
</script>
