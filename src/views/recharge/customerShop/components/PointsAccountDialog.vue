<template>
  <el-dialog :title="dialog.title" v-model="dialog.visible" width="80%" append-to-body destroy-on-close @close="handleClose" draggable>
    <points-account-table :customerShopId="customerShopId" :customerShop="customerShop" />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import PointsAccountTable from './PointsAccountTable.vue';
import { CustomerShopVO } from '@/api/recharge/customerShop/types';

const customerShopId = ref<string | number | undefined>();
const customerShop = ref<CustomerShopVO>();
const dialog = ref({
  visible: false,
  title: '白名单'
});

const emit = defineEmits(['refresh']);

const handleClose = () => {
  dialog.value.visible = false;
  emit('refresh');
};

const openDialog = async (row: CustomerShopVO) => {
  dialog.value.visible = true;
  dialog.value.title = '店铺「 ' + row.shopName + ' 」的C端用户(白名单+积分账户)';
  customerShopId.value = row.id;
  customerShop.value = row;
};

defineExpose({
  openDialog
});
</script>
