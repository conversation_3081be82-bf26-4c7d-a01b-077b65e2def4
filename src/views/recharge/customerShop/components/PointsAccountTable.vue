<template>
  <div>
    <!-- 新增和搜索 -->
    <div class="mb-2" style="display: flex; justify-content: space-between; align-items: center" gap="10px">
      <el-row :gutter="10">
        <el-col :span="1.5">
          <el-button type="primary" @click="handleAdd">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container">
            <el-form-item label="" prop="userPhone">
              <el-input v-model="queryParams.userPhone" placeholder="搜索手机号" maxlength="11" show-word-limit clearable @blur="handleQuery" />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <el-tooltip content="刷新" placement="top">
        <el-link type="primary" icon="Refresh" @click="getList" />
      </el-tooltip>
    </div>

    <!-- 列表 -->
    <div>
      <el-table v-loading="loading" :data="pointsAccountList" @selection-change="handleSelectionChange" border>
        <el-table-column type="index" width="55" label="序号" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="用户手机号" prop="userPhone" width="130" />
        <el-table-column label="结算依据" prop="settlementBasis">
          <template #default="scope">
            <dict-tag :options="mall_settlement_basis" :value="scope.row.settlementBasis" />
          </template>
        </el-table-column>
        <el-table-column label="账户余额" prop="accountBalance" width="130">
          <template #default="scope">
            <el-link type="primary" @click="openPointStreamDialog(scope.row)">
              <el-tooltip content="查看积分账户流水" placement="top">
                {{ centToYuan(scope.row.accountBalance) }}
              </el-tooltip>
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="是否白名单" prop="isWhitelist" width="100">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.isWhitelist" />
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="250" show-overflow-tooltip />
        <el-table-column label="更新者" prop="updateNickName" width="130" show-overflow-tooltip />
        <el-table-column label="更新时间" prop="updateTime" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{m}-{d} {h}:{i}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" width="100" fixed="right">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['recharge:pointsAccount:edit']"></el-button>
            </el-tooltip>
            <!-- <el-tooltip content="删除" placement="top">
              <el-button
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['recharge:pointsAccount:remove']"
              ></el-button>
            </el-tooltip> -->
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </div>

    <!-- 添加或修改积分账户对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body @close="cancel" draggable>
      <el-form ref="pointsAccountFormRef" :model="form" :rules="rules" label-width="120px" label-position="top">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="用户手机号" prop="userPhone">
              <el-input v-model="form.userPhone" placeholder="请输入用户手机号" maxlength="11" show-word-limit :disabled="dialogEditStatus" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否白名单" prop="isWhitelist">
              <el-radio-group v-model="form.isWhitelist">
                <el-radio v-for="dict in sys_yes_no" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结算依据" prop="settlementBasis">
              <el-radio-group v-model="form.settlementBasis" disabled>
                <el-radio v-for="dict in mall_settlement_basis" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账户余额" prop="accountBalance">
              <el-input-number
                placeholder="请输入账户余额"
                :min="0"
                :max="9999.99"
                :step="0.01"
                @change="(val) => (form.accountBalance = val ? yuanToCent(Number(val)) : undefined)"
                :model-value="form.accountBalance ? Number(centToYuan(form.accountBalance)) : undefined"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" placeholder="请输入备注" type="textarea" :rows="3" :maxlength="200" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 积分账户流水 -->
    <point-stream-dialog ref="pointStreamDialogRef" :customerShopId="customerShopId" @refresh="getList" />
  </div>
</template>

<script setup lang="ts">
import { CustomerShopVO } from '@/api/recharge/customerShop/types';
import {
  listPointsAccount,
  getPointsAccount,
  delPointsAccount,
  addPointsAccount,
  updatePointsAccount,
  checkUnique
} from '@/api/recharge/pointsAccount';
import { PointsAccountVO, PointsAccountQuery, PointsAccountForm } from '@/api/recharge/pointsAccount/types';
import PointStreamDialog from './PointsStreamDialog.vue';

import { centToYuan, yuanToCent } from '@/utils/moneyUtils';

const props = defineProps<{
  customerShopId: string | number;
  customerShop: CustomerShopVO;
}>();

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_settlement_basis, sys_yes_no } = toRefs<any>(proxy?.useDict('mall_settlement_basis', 'sys_yes_no'));

const pointsAccountList = ref<PointsAccountVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑
const pointStreamDialogRef = ref(null);

const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const pointsAccountFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: PointsAccountForm = {
  id: undefined,
  customerShopId: props.customerShopId,
  userPhone: undefined,
  settlementBasis: props.customerShop.settlementBasis,
  accountBalance: 0,
  isWhitelist: 'Y',
  remark: undefined
};
const data = reactive<PageData<PointsAccountForm, PointsAccountQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    customerShopId: props.customerShopId,
    userPhone: undefined,
    settlementBasis: undefined,
    accountBalance: undefined,
    isWhitelist: undefined,
    updateBy: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    customerShopId: [{ required: true, message: '客户店铺不能为空', trigger: 'blur' }],
    userPhone: [
      { required: true, message: '用户手机号不能为空', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的11位手机号', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value) {
            queryUnique.id = form.value.id;
            queryUnique.userPhone = form.value.userPhone;
            queryUnique.customerShopId = form.value.customerShopId;
            checkUnique(queryUnique)
              .then((res) => {
                if (res.data) {
                  callback(new Error('用户手机号已存在'));
                } else {
                  callback();
                }
              })
              .catch(() => {
                callback(new Error('唯一性校验失败'));
              });
          } else {
            callback();
          }
        },
        trigger: 'change'
      }
    ],
    settlementBasis: [{ required: true, message: '结算依据不能为空', trigger: 'change' }],
    isWhitelist: [{ required: true, message: '是否白名单不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 校验唯一性查询体 */
const queryUnique: PointsAccountForm = {
  id: undefined,
  customerShopId: undefined,
  userPhone: undefined
};

/** 查询积分账户列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listPointsAccount(queryParams.value);
  pointsAccountList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  pointsAccountFormRef.value?.resetFields();
  dialogEditStatus.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: PointsAccountVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加积分账户';
  dialogEditStatus.value = false;
};

/** 修改按钮操作 */
const handleUpdate = async (row?: PointsAccountVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getPointsAccount(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改积分账户';
  dialogEditStatus.value = true;
};

/** 提交按钮 */
const submitForm = () => {
  pointsAccountFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updatePointsAccount(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addPointsAccount(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: PointsAccountVO) => {
  await proxy?.$modal.confirm('是否确认删除积分账户编号为"' + row?.userPhone + '"的C端用户吗？').finally(() => (loading.value = false));
  await delPointsAccount(row?.id);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

//** 打开积分账户流水弹窗 */
const openPointStreamDialog = (row: PointsAccountVO) => {
  pointStreamDialogRef.value.openDialog(row);
  dialog.visible = false;
};

onMounted(() => {
  getList();
});
</script>
