<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form-container" label-width="100px">
            <el-form-item label="客户" prop="customerId">
              <customer-select v-model="queryParams.customerId" placeholder="请选择客户档案" @change="handleQuery" />
            </el-form-item>
            <el-form-item label="店铺名称" prop="shopName">
              <el-input v-model="queryParams.shopName" placeholder="请输入名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态" prop="shopStatus">
              <el-select v-model="queryParams.shopStatus" placeholder="请选择状态" clearable @change="handleQuery">
                <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="结算依据" prop="settlementBasis">
              <el-select v-model="queryParams.settlementBasis" placeholder="请选择结算依据" clearable @change="handleQuery">
                <el-option v-for="dict in mall_settlement_basis" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="报价策略" prop="quoteStrategy">
              <el-select v-model="queryParams.quoteStrategy" placeholder="请选择报价策略" clearable @change="handleQuery">
                <el-option v-for="dict in mall_quote_strategy" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="小程序" prop="wxAppid" v-if="showMoreCondition">
              <el-select v-model="queryParams.wxAppid" placeholder="请选择小程序" clearable @change="handleQuery">
                <el-option v-for="dict in mall_wx_client" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新者" prop="updateBy" v-if="showMoreCondition">
              <el-select v-model="queryParams.updateBy" placeholder="请输入更新者" clearable @change="handleQuery" filterable>
                <el-option v-for="item in userOptions" :key="item.userId" :label="item.nickName" :value="item.userId" />
              </el-select>
            </el-form-item>
            <el-form-item label="更新时间" style="width: 308px" v-if="showMoreCondition">
              <el-date-picker
                v-model="dateRangeUpdateTime"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button link @click="showMoreCondition = !showMoreCondition">
                {{ showMoreCondition ? '收起' : '展开' }}
                <el-icon class="el-icon--right">
                  <arrow-up v-if="showMoreCondition" />
                  <arrow-down v-else />
                </el-icon>
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['recharge:customerShop:add']">新增</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['recharge:customerShop:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['recharge:customerShop:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['recharge:customerShop:export']">导出</el-button>
          </el-col> -->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="customerShopList" @selection-change="handleSelectionChange" border>
        <el-table-column type="index" width="55" label="序号" />
        <el-table-column label="id" prop="id" v-if="false" />
        <el-table-column label="店铺名称" prop="shopName" min-width="150" />
        <el-table-column label="客户" prop="customerId" min-width="200" show-overflow-tooltip>
          <template #default="scope">
            <el-link type="primary" @click="openCustomerDetailInView(scope.row.customerId)">
              <el-tooltip content="打开客户档案" placement="top">
                {{ scope.row.customerName }}
              </el-tooltip>
            </el-link>
          </template>
        </el-table-column>

        <el-table-column label="兑换入口" prop="qrCodeUrl" min-width="80" align="center">
          <template #default="scope">
            <image-preview :src="scope.row.qrCodeUrl" :width="50" :height="50" />
          </template>
        </el-table-column>

        <el-table-column label="客户状态" prop="customerStatus" min-width="100">
          <template #default="scope">
            <dict-tag :options="dict_customer_status" :value="scope.row.customerStatus" />
          </template>
        </el-table-column>

        <!-- <el-table-column label="小程序" prop="wxAppid" min-width="100" show-overflow-tooltip>
          <template #default="scope">
            <dict-tag :options="mall_wx_client" :value="scope.row.wxAppid" />
          </template>
        </el-table-column> -->
        <el-table-column label="状态" prop="shopStatus" min-width="100">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.shopStatus" />
          </template>
        </el-table-column>
        <el-table-column label="店铺商品" prop="goodsCount" min-width="100">
          <template #default="scope">
            <el-link type="primary" @click="openShopGoodsDialog(scope.row)">
              <el-tooltip content="维护店铺商品" placement="top">
                {{ scope.row.goodsCount }}
              </el-tooltip>
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="白名单用户" prop="pointAccountCount" min-width="100">
          <template #default="scope">
            <el-link type="primary" @click="openPointsAccountDialog(scope.row)">
              <el-tooltip content="维护白名单用户" placement="top">
                {{ scope.row.pointAccountCount }}
              </el-tooltip>
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="免运费门槛(元)" prop="freeShippingThreshold" min-width="120">
          <template #default="scope">
            {{ centToYuan(scope.row.freeShippingThreshold) }}
          </template>
        </el-table-column>
        <el-table-column label="运费(元)" prop="postFee" min-width="100">
          <template #default="scope">
            {{ centToYuan(scope.row.postFee) }}
          </template>
        </el-table-column>
        <el-table-column label="结算依据" prop="settlementBasis" min-width="100">
          <template #default="scope">
            <dict-tag :options="mall_settlement_basis" :value="scope.row.settlementBasis" />
          </template>
        </el-table-column>
        <el-table-column label="报价策略" prop="quoteStrategy" min-width="100">
          <template #default="scope">
            <dict-tag :options="mall_quote_strategy" :value="scope.row.quoteStrategy" />
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="250" show-overflow-tooltip />
        <el-table-column label="更新者" prop="updateNickName" min-width="150" />
        <el-table-column label="更新时间" prop="updateTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" width="150" fixed="right">
          <template #default="scope">
            <el-tooltip content="生成小程序码" placement="top" v-if="!scope.row.qrCode">
              <el-button
                link
                type="primary"
                icon="Refresh"
                @click="handleGenerateQrCode(scope.row)"
                v-hasPermi="['recharge:customerShop:generateQrCode']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['recharge:customerShop:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['recharge:customerShop:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改客户店铺对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="60%" append-to-body @close="cancel" draggable>
      <el-form ref="customerShopFormRef" :model="form" :rules="rules" label-width="120px" label-position="top">
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="客户" prop="customerId">
              <template #label>
                <span>
                  客户档案
                  <el-tooltip content="打开客户档案" placement="top" v-if="form.customerId">
                    <el-icon @click="openCustomerDetailInView(form.customerId)">
                      <Link />
                    </el-icon>
                  </el-tooltip>
                  <el-tooltip content="新增客户档案" placement="top" v-else>
                    <el-icon @click="openCustomerAddDialog()">
                      <Plus />
                    </el-icon>
                  </el-tooltip>
                </span>
              </template>
              <customer-select v-model="form.customerId" :disabled="dialogEditStatus" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="小程序" prop="wxAppid">
              <dict-select v-model="form.wxAppid" dict-key="mall_wx_client" placeholder="请选择小程序" :disabled="dialogEditStatus" />
            </el-form-item>
          </el-col> -->
          <el-col :span="8">
            <el-form-item label="店铺名称" prop="shopName">
              <el-input v-model="form.shopName" placeholder="请输入名称" :maxlength="20" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态" prop="shopStatus">
              <el-radio-group v-model="form.shopStatus">
                <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="免运费门槛" prop="freeShippingThreshold">
              <template #label>
                <span>
                  免运门槛(元)
                  <el-tooltip content="订单中的商品金额大于等于该值则免运费" placement="top">
                    <el-icon>
                      <InfoFilled />
                    </el-icon>
                  </el-tooltip>
                </span>
              </template>
              <el-input-number
                placeholder="请输入免运费门槛"
                :min="0"
                :max="9999.99"
                :step="0.01"
                @change="(val) => (form.freeShippingThreshold = val ? yuanToCent(val) : undefined)"
                :model-value="Number(centToYuan(form.freeShippingThreshold))"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="运费" prop="postFee">
              <el-input-number
                placeholder="请输入运费"
                :min="0"
                :max="9999.99"
                :step="0.01"
                @change="(val) => (form.postFee = val ? yuanToCent(val) : undefined)"
                :model-value="Number(centToYuan(form.postFee))"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结算依据" prop="settlementBasis">
              <dict-select
                v-model="form.settlementBasis"
                dict-key="mall_settlement_basis"
                placeholder="请选择结算依据"
                :disabled="dialogEditStatus"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="报价策略" prop="quoteStrategy">
              <dict-select v-model="form.quoteStrategy" dict-key="mall_quote_strategy" placeholder="请选择报价策略" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.quoteStrategy === '01' && form.settlementBasis === '2'">
            <el-form-item label="结算价加点" prop="settlePriceMakeup">
              <el-input-number
                v-model="form.settlePriceMakeup"
                placeholder="请输入结算价加点"
                :min="0"
                :max="999.99"
                :step="1"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.quoteStrategy === '01'">
            <el-form-item label="销售价加点" prop="sellingPriceMakeup">
              <el-input-number
                v-model="form.sellingPriceMakeup"
                placeholder="请输入销售价加点"
                :min="0"
                :max="999.99"
                :step="1"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.quoteStrategy === '02' && form.settlementBasis === '2'">
            <el-form-item label="结算价折扣" prop="settlePriceDiscount">
              <el-input-number
                v-model="form.settlePriceDiscount"
                placeholder="请输入结算价折扣"
                :min="0"
                :max="999.99"
                :step="0.01"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.quoteStrategy === '02'">
            <el-form-item label="销售价折扣" prop="sellingPriceDiscount">
              <el-input-number
                v-model="form.sellingPriceDiscount"
                placeholder="请输入销售价折扣"
                :min="0"
                :max="999.99"
                :step="0.01"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" placeholder="请输入备注" :maxlength="200" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="海报图片" prop="poster">
              <image-upload v-model="form.poster" :limit="1" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 店铺商品弹窗 -->
    <shop-goods-dialog ref="shopGoodsDialogRef" @refresh="getList" />

    <!-- 积分账户弹窗 -->
    <points-account-dialog ref="pointsAccountDialogRef" @refresh="getList" />
  </div>
</template>

<script setup name="CustomerShop" lang="ts">
import {
  listCustomerShop,
  generateQrCode,
  getCustomerShop,
  delCustomerShop,
  addCustomerShop,
  updateCustomerShop,
  checkShopNameUnique
} from '@/api/recharge/customerShop';
import { CustomerShopVO, CustomerShopQuery, CustomerShopForm } from '@/api/recharge/customerShop/types';
import ShopGoodsDialog from './components/ShopGoodsDialog.vue';
import PointsAccountDialog from './components/PointsAccountDialog.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mall_settlement_basis, mall_wx_client, sys_normal_disable, dict_customer_status, mall_quote_strategy } = toRefs<any>(
  proxy?.useDict('mall_settlement_basis', 'mall_wx_client', 'sys_normal_disable', 'dict_customer_status', 'mall_quote_strategy')
);

import { centToYuan, yuanToCent } from '@/utils/moneyUtils';

const customerShopList = ref<CustomerShopVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showMoreCondition = ref(false);
const dialogEditStatus = ref(false); // false: 新增，true: 编辑
const shopGoodsDialogRef = ref(null);
const pointsAccountDialogRef = ref(null);

const dateRangeUpdateTime = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const customerShopFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const route = useRoute();
const router = useRouter();

const initFormData: CustomerShopForm = {
  id: undefined,
  shopName: undefined,
  customerId: undefined,
  wxAppid: 'wx7a1513cb54364652',
  shopStatus: '0',
  freeShippingThreshold: 10000,
  postFee: 1000,
  settlementBasis: '1',
  settlePriceMakeup: 0,
  sellingPriceMakeup: 0,
  settlePriceDiscount: 100,
  sellingPriceDiscount: 100,
  poster: undefined,
  quoteStrategy: '01',
  remark: undefined
};
const data = reactive<PageData<CustomerShopForm, CustomerShopQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    shopName: undefined,
    customerId: undefined,
    wxAppid: undefined,
    shopStatus: undefined,
    freeShippingThreshold: undefined,
    postFee: undefined,
    settlementBasis: undefined,
    quoteStrategy: undefined,
    updateBy: undefined,
    params: {
      updateTime: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    shopName: [
      { required: true, message: '名称不能为空', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value) {
            queryUnique.shopName = form.value.shopName;
            queryUnique.id = form.value.id;
            checkShopNameUnique(queryUnique)
              .then((res) => {
                if (res.data) {
                  callback(new Error('名称已存在'));
                } else {
                  callback();
                }
              })
              .catch(() => {
                callback(new Error('验证名称失败'));
              });
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ],
    customerId: [{ required: true, message: '客户不能为空', trigger: 'blur' }],
    wxAppid: [{ required: true, message: '小程序不能为空', trigger: 'change' }],
    poster: [{ required: true, message: '海报图片不能为空', trigger: 'blur' }],
    quoteStrategy: [{ required: true, message: '报价策略不能为空', trigger: 'change' }],
    shopStatus: [{ required: true, message: '状态不能为空', trigger: 'change' }],
    freeShippingThreshold: [{ required: true, message: '免运费门槛不能为空', trigger: 'blur' }],
    postFee: [{ required: true, message: '运费不能为空', trigger: 'blur' }],

    /**
     * 校验规则：
     * 1. 如果settlementBasis=1，quoteStrategy=01，则sellingPriceMakeup必填
     * 2. 如果settlementBasis=2，quoteStrategy=01，则sellingPriceMakeup和settlePriceMakeup均必填
     * 3. 如果settlementBasis=1，quoteStrategy=02，则settlePriceDiscount必填
     * 4. 如果settlementBasis=2，quoteStrategy=02，则settlePriceDiscount和sellingPriceDiscount均必填
     */
    settlementBasis: [{ required: true, message: '结算依据不能为空', trigger: 'change' }],
    settlePriceMakeup: [
      {
        validator: (rule, value, callback) => {
          if (form.value.settlementBasis === '2' && form.value.quoteStrategy === '01' && !value) {
            callback(new Error('结算价加点不能为空'));
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ],
    sellingPriceMakeup: [
      {
        validator: (rule, value, callback) => {
          if (form.value.quoteStrategy === '01' && (value === undefined || value === null || value === '')) {
            callback(new Error('销售价加点不能为空'));
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ],
    settlePriceDiscount: [
      {
        validator: (rule, value, callback) => {
          if (form.value.quoteStrategy === '02' && !value) {
            callback(new Error('结算价折扣不能为空'));
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ],
    sellingPriceDiscount: [
      {
        validator: (rule, value, callback) => {
          if (form.value.settlementBasis === '2' && form.value.quoteStrategy === '02' && !value) {
            callback(new Error('销售价折扣不能为空'));
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

import { useSysUserSelect } from '@/hooks/useBusiness/sysUserSelect';
const { loadUserList, userOptions } = useSysUserSelect();

/** 校验名称的唯一性查询体 */
const queryUnique: CustomerShopForm = {
  id: undefined,
  shopName: undefined
};

/** 查询客户店铺列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeUpdateTime.value, 'UpdateTime');
  const res = await listCustomerShop(queryParams.value);
  customerShopList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
  dialogEditStatus.value = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  customerShopFormRef.value?.resetFields();
  dialogEditStatus.value = false;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeUpdateTime.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: CustomerShopVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加客户店铺';
  dialogEditStatus.value = false;
};

/** 生成小程序码 */
const handleGenerateQrCode = async (row?: CustomerShopVO) => {
  loading.value = true;
  const res = await generateQrCode(row.id);
  proxy?.$modal.msgSuccess('操作成功');
  await getList();
  loading.value = false;
};

/** 修改按钮操作 */
const handleUpdate = async (row?: CustomerShopVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getCustomerShop(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改客户店铺';
  dialogEditStatus.value = true;
};

/** 提交按钮 */
const submitForm = () => {
  customerShopFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateCustomerShop(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addCustomerShop(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: CustomerShopVO) => {
  await proxy?.$modal.confirm('是否确认删除客户店铺为"' + row?.shopName + '"的项？').finally(() => (loading.value = false));
  await delCustomerShop(row?.id);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'recharge/customerShop/export',
    {
      ...queryParams.value
    },
    `customerShop_${new Date().getTime()}.xlsx`
  );
};

// 打开客户详情(查看页面)
const openCustomerDetailInView = (id: string | number) => {
  const routeUrl = `/crm/bizMg/customer/?id=${id}&openView=true`;
  window.open(routeUrl, '_blank');
};

// 打开客户弹窗
const openCustomerAddDialog = () => {
  const routeUrl = `/crm/bizMg/customer/?openAdd=true`;
  window.open(routeUrl, '_blank');
};

/** 打开店铺商品弹窗 */
const openShopGoodsDialog = (row: CustomerShopVO) => {
  shopGoodsDialogRef.value.openDialog(row);
  dialog.visible = false;
};

/** 打开积分账户弹窗 */
const openPointsAccountDialog = (row: CustomerShopVO) => {
  pointsAccountDialogRef.value.openDialog(row);
  dialog.visible = false;
};

/** 监听路由参数 */
const handleOpenDialog = () => {
  // 1. 打开新增弹窗
  const customerId = route.query.customerId;
  const openAdd = route.query.openAdd;
  if (openAdd === 'true') {
    nextTick(() => {
      handleAdd();
      form.value.customerId = customerId as string;
      router.replace({ query: {} });
    });
  }
};

onMounted(() => {
  getList();
  loadUserList();
  handleOpenDialog();
});
</script>
