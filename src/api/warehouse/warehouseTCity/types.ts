export interface WarehouseTCityVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 组织id
   */
  createOrgId: string | number;

  /**
   * 组织名称
   */
  createOrgName: string;

  /**
   * 大仓
   */
  warehouseId: string | number;

  /**
   * 大仓名称
   */
  warehouseName: string;

  /**
   * 省份,冗余阻断
   */
  provinceCode: string;

  /**
   * 省份名称
   */
  provinceName: string;

  /**
   * 城市
   */
  cityCode: string;

  /**
   * 城市名称
   */
  cityName: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新人
   */
  updateBy: number;

  /**
   * 更新人昵称
   */
  updateNickName: string;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface WarehouseTCityForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 大仓
   */
  warehouseId?: string | number;

  /**
   * 省份,冗余阻断
   */
  provinceCode?: string;

  /**
   * 城市
   */
  cityCode?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface WarehouseTCityQuery extends PageQuery {
  /**
   * 大仓
   */
  warehouseId?: string | number;

  /**
   * 省份,冗余阻断
   */
  provinceCode?: string;

  /**
   * 城市
   */
  cityCode?: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
