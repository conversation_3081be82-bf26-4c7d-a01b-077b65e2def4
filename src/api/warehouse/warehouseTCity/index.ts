import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { WarehouseTCityVO, WarehouseTCityForm, WarehouseTCityQuery } from '@/api/warehouse/warehouseTCity/types';

/**
 * 查询大仓发货城市列表
 * @param query
 * @returns {*}
 */

export const listWarehouseTCity = (query?: WarehouseTCityQuery): AxiosPromise<WarehouseTCityVO[]> => {
  return request({
    url: '/warehouse/warehouseTCity/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询大仓发货城市详细
 * @param id
 */
export const getWarehouseTCity = (id: string | number): AxiosPromise<WarehouseTCityVO> => {
  return request({
    url: '/warehouse/warehouseTCity/' + id,
    method: 'get'
  });
};

/**
 * 新增大仓发货城市
 * @param data
 */
export const addWarehouseTCity = (data: WarehouseTCityForm) => {
  return request({
    url: '/warehouse/warehouseTCity',
    method: 'post',
    data: data
  });
};

/**
 * 修改大仓发货城市
 * @param data
 */
export const updateWarehouseTCity = (data: WarehouseTCityForm) => {
  return request({
    url: '/warehouse/warehouseTCity',
    method: 'put',
    data: data
  });
};

/**
 * 删除大仓发货城市
 * @param id
 */
export const delWarehouseTCity = (id: string | number | Array<string | number>) => {
  return request({
    url: '/warehouse/warehouseTCity/' + id,
    method: 'delete'
  });
};
