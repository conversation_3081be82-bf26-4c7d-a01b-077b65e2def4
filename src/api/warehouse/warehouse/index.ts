import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { WarehouseVO, WarehouseForm, WarehouseQuery } from '@/api/warehouse/warehouse/types';

/**
 * 查询大仓列表
 * @param query
 * @returns {*}
 */

export const listWarehouse = (query?: WarehouseQuery): AxiosPromise<WarehouseVO[]> => {
  return request({
    url: '/warehouse/warehouse/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询大仓详细
 * @param id
 */
export const getWarehouse = (id: string | number): AxiosPromise<WarehouseVO> => {
  return request({
    url: '/warehouse/warehouse/' + id,
    method: 'get'
  });
};

/**
 * 新增大仓
 * @param data
 */
export const addWarehouse = (data: WarehouseForm) => {
  return request({
    url: '/warehouse/warehouse',
    method: 'post',
    data: data
  });
};

/**
 * 修改大仓
 * @param data
 */
export const updateWarehouse = (data: WarehouseForm) => {
  return request({
    url: '/warehouse/warehouse',
    method: 'put',
    data: data
  });
};

/**
 * 删除大仓
 * @param id
 */
export const delWarehouse = (id: string | number | Array<string | number>) => {
  return request({
    url: '/warehouse/warehouse/' + id,
    method: 'delete'
  });
};
