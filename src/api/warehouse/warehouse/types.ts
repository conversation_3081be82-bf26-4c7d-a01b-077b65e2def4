export interface WarehouseVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 名称
   */
  name: string;

  /**
   *发货城市数量
   */
  cityCount: number;

  /**
   * 地址
   */
  address: string;

  /**
   * 内部ERP编码
   */
  insideErpCode: string;

  /**
   * 外部ERP编码
   */
  outsideErpCode: string;

  /**
   * 状态，关联字典「sys_normal_disable」
   */
  status: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新人
   */
  updateBy: number;

  /**
   * 更新人昵称
   */
  updateNickName: string;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface WarehouseForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 名称
   */
  name?: string;

  /**
   * 地址
   */
  address?: string;

  /**
   * 内部ERP编码
   */
  insideErpCode?: string;

  /**
   * 外部ERP编码
   */
  outsideErpCode?: string;
  /**
   * 状态，关联字典「sys_normal_disable」
   */
  status?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface WarehouseQuery extends PageQuery {
  /**
   * 名称
   */
  name?: string;

  /**
   * 状态，关联字典「sys_normal_disable」
   */
  status?: string;

  /**
   * 内部ERP编码
   */
  insideErpCode?: string;

  /**
   * 外部ERP编码
   */
  outsideErpCode?: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
