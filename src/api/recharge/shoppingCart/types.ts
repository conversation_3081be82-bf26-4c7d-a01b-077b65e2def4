export interface ShoppingCartVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 客户店铺
   */
  customerShopId: string | number;

  /**
   * 用户手机号
   */
  userPhone: string;

  /**
   * 商品
   */
  goodsId: string | number;

  /**
   * 数量
   */
  qty: number;

  /**
   * 更新者
   */
  updateBy: number;

  /**
   * 更新者昵称
   */
  updateNickName: string;
  /**
   * 更新时间
   */
  updateTime: string;
}

export interface ShoppingCartForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 客户店铺
   */
  customerShopId?: string | number;

  /**
   * 用户手机号
   */
  userPhone?: string;

  /**
   * 商品
   */
  goodsId?: string | number;

  /**
   * 数量
   */
  qty?: number;
}

export interface ShoppingCartQuery extends PageQuery {
  /**
   * 客户店铺
   */
  customerShopId?: string | number;

  /**
   * 用户手机号
   */
  userPhone?: string;

  /**
   * 商品
   */
  goodsId?: string | number;

  /**
   * 数量
   */
  qty?: number;

  /**
   * 更新者
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
