import { CustomerShopVO } from '@/api/recharge/customerShop/types';

export interface PointsAccountVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 客户店铺
   */
  customerShopId: string | number;

  /**
   * 客户店铺
   */
  customerShop: CustomerShopVO;

  /**
   * 用户手机号
   */
  userPhone: string;

  /**
   * 结算依据
   */
  settlementBasis: string;

  /**
   * 账户余额
   */
  accountBalance: number;

  /**
   * 是否白名单
   */
  isWhitelist: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新者
   */
  updateBy: number;

  /**
   * 更新者昵称
   */
  updateNickname: string;
  /**
   * 更新时间
   */
  updateTime: string;
}

export interface PointsAccountForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 客户店铺
   */
  customerShopId?: string | number;

  /**
   * 用户手机号
   */
  userPhone?: string;

  /**
   * 结算依据
   */
  settlementBasis?: string;

  /**
   * 账户余额
   */
  accountBalance?: number;

  /**
   * 是否白名单
   */
  isWhitelist?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface PointsAccountQuery extends PageQuery {
  /**
   * 客户店铺
   */
  customerShopId?: string | number;

  /**
   * 用户手机号
   */
  userPhone?: string;

  /**
   * 结算依据
   */
  settlementBasis?: string;

  /**
   * 账户余额
   */
  accountBalance?: number;

  /**
   * 是否白名单
   */
  isWhitelist?: string;

  /**
   * 更新者
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
