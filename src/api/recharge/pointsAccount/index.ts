import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PointsAccountVO, PointsAccountForm, PointsAccountQuery } from '@/api/recharge/pointsAccount/types';

/**
 * 查询积分账户列表
 * @param query
 * @returns {*}
 */

export const listPointsAccount = (query?: PointsAccountQuery): AxiosPromise<PointsAccountVO[]> => {
  return request({
    url: '/recharge/pointsAccount/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询积分账户详细
 * @param id
 */
export const getPointsAccount = (id: string | number): AxiosPromise<PointsAccountVO> => {
  return request({
    url: '/recharge/pointsAccount/' + id,
    method: 'get'
  });
};

/**
 * 新增积分账户
 * @param data
 */
export const addPointsAccount = (data: PointsAccountForm) => {
  return request({
    url: '/recharge/pointsAccount',
    method: 'post',
    data: data
  });
};

/**
 * 修改积分账户
 * @param data
 */
export const updatePointsAccount = (data: PointsAccountForm) => {
  return request({
    url: '/recharge/pointsAccount',
    method: 'put',
    data: data
  });
};

/**
 * 删除积分账户
 * @param id
 */
export const delPointsAccount = (id: string | number | Array<string | number>) => {
  return request({
    url: '/recharge/pointsAccount/' + id,
    method: 'delete'
  });
};

/**
 * 校验唯一性
 */
export const checkUnique = (data: PointsAccountForm): AxiosPromise<boolean> => {
  return request({
    url: '/recharge/pointsAccount/checkUnique',
    method: 'get',
    params: data
  });
};
