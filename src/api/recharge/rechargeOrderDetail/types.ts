export interface RechargeOrderDetailVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 销售订单id
   */
  orderId: string | number;

  /**
   * 批次号
   */
  batchNumber: string;

  /**
   * 面值（分）
   */
  faceValue: number;

  /**
   * 需求数量
   */
  qty: number;

  /**
   * 出卡数量
   */
  authQty: number;

  /**
   * 单价（元）
   */
  unitPrice: number;

  /**
   * 需求小计（元）
   */
  demandTotal: number;

  /**
   * 出卡小计（元）
   */
  authTotal: number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新者
   */
  updateBy: number;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface RechargeOrderDetailForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 销售订单id
   */
  orderId?: string | number;

  /**
   * 批次号
   */
  batchNumber?: string;

  /**
   * 需求数量
   */
  qty?: number;

  /**
   * 出卡数量
   */
  authQty?: number;

  /**
   * 单价（元）
   */
  unitPrice?: number;

  /**
   *  需求小计（元）
   */
  demandTotal?: number;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 折扣
   */
  discount?: number;

  /**
   * 面值（分）
   */
  faceValueYuan?: number;

  /**
   * 单价（元）
   */
  unitPriceYuan?: number;

  /**
   * 需求小计（元）
   */
  demandTotalYuan?: number;

  /**
   * 流水号-开始
   */
  cardNoStart?: string;

  /**
   * 流水号-结束
   */
  cardNoEnd?: string;

  /**
   * 此次出卡数量
   */
  cardBeOutQty?: number;
}

export interface RechargeOrderDetailQuery extends PageQuery {
  /**
   * 销售订单id
   */
  orderId?: string | number;

  /**
   * 批次号
   */
  batchNumber?: string;

  /**
   * 更新者
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
