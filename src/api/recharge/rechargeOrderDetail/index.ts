import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { RechargeOrderDetailVO, RechargeOrderDetailForm, RechargeOrderDetailQuery } from '@/api/recharge/rechargeOrderDetail/types';

/**
 * 查询售码订单明细列表
 * @param query
 * @returns {*}
 */

export const listRechargeOrderDetail = (query?: RechargeOrderDetailQuery): AxiosPromise<RechargeOrderDetailVO[]> => {
  return request({
    url: '/recharge/rechargeOrderDetail/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询售码订单明细详细
 * @param id
 */
export const getRechargeOrderDetail = (id: string | number): AxiosPromise<RechargeOrderDetailVO> => {
  return request({
    url: '/recharge/rechargeOrderDetail/' + id,
    method: 'get'
  });
};

/**
 * 新增售码订单明细
 * @param data
 */
export const addRechargeOrderDetail = (data: RechargeOrderDetailForm) => {
  return request({
    url: '/recharge/rechargeOrderDetail',
    method: 'post',
    data: data
  });
};

/**
 * 修改售码订单明细
 * @param data
 */
export const updateRechargeOrderDetail = (data: RechargeOrderDetailForm) => {
  return request({
    url: '/recharge/rechargeOrderDetail',
    method: 'put',
    data: data
  });
};

/**
 * 删除售码订单明细
 * @param id
 */
export const delRechargeOrderDetail = (id: string | number | Array<string | number>) => {
  return request({
    url: '/recharge/rechargeOrderDetail/' + id,
    method: 'delete'
  });
};

/**
 * 校验订单中是否已存在该批次号
 */
export const checkBatchNumberUnique = (data: RechargeOrderDetailForm): AxiosPromise<boolean> => {
  return request({
    url: '/recharge/rechargeOrderDetail/checkBatchUnique',
    method: 'get',
    params: data
  });
};

/**
 * 销售订单出卡
 */
export const cardOutSubmit = (data: RechargeOrderDetailForm) => {
  return request({
    url: '/recharge/rechargeOrderDetail/cardOut',
    method: 'post',
    data: data
  });
};

/**
 * 销售订单撤销出卡
 */
export const cardOutCancel = (data: RechargeOrderDetailForm) => {
  return request({
    url: '/recharge/rechargeOrderDetail/cancelCardOut',
    method: 'post',
    data: data
  });
};
