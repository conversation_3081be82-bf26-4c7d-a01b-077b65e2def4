export interface RechargeOrderVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 订单号
   */
  orderNo: string;

  /**
   * 订单类型
   */
  orderType: string;

  /**
   * 订单用途
   */
  purpose: string;

  /**
   * 承做人
   */
  ownerId: string | number;

  /**
   * 承做人昵称
   */
  ownerNickName: string;

  /**
   * 承做部门
   */
  ownerDeptId: string | number;

  /**
   * 承做部门名称
   */
  ownerDeptName: string;

  /**
   * 客户店铺
   */
  customerShopId: string | number;

  /**
   * 客户
   */
  customerId: string | number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 总面值
   */
  totalValues: number;

  /**
   * 总明细金额
   */
  totalAmount: number;

  /**
   * 取整优惠金额
   */
  discountAmount: number;

  /**
   * 订单金额
   */
  orderAmount: number;

  /**
   * 折扣率
   */
  discountPercentage: number;

  /**
   * 订单状态
   */
  orderStatus: string;

  /**
   * 订单状态修改时间
   */
  statusChangeTime: string;

  /**
   * 是否预收款
   */
  isAdvanceReceipt: string;

  /**
   * 预收金额
   */
  receiptAmount: number;

  /**
   * 收款日期
   */
  receiptDate: string;

  /**
   * 收款备注
   */
  receiptNote: string;

  /**
   * 收款附件
   */
  receiptFiles: string;

  /**
   * 开卡时间
   */
  openCardTime: string;

  /**
   * 开卡凭证
   */
  certificateFiles: string;

  /**
   * 开卡备注
   */
  openCardNote: string;

  /**
   * 更新者
   */
  updateBy: number;

  /**
   * 更新者昵称
   */
  updateNickName: string;

  /**
   * 更新时间
   */
  updateTime: string;

  /**
   * 总面值（元）
   */
  totalValuesYuan: number;

  /**
   * 总明细（元）
   */
  totalAmountYuan: number;

  /**
   * 取整优惠金额（元）
   */
  discountAmountYuan: number;

  /**
   * 订单金额（元）
   */
  orderAmountYuan: number;

  /**
   * 订单明细数量
   */
  detailCount: number;

  /**
   * 客户店铺名称
   */
  customerShopName: string;

  /**
   * 客户名称
   */
  customerName: string;
}

export interface RechargeOrderForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 订单号
   */
  orderNo?: string;

  /**
   * 订单类型
   */
  orderType?: string;

  /**
   * 订单用途
   */
  purpose?: string;

  /**
   * 承做人
   */
  ownerId?: string | number;

  /**
   * 承做部门
   */
  ownerDeptId?: string | number;

  /**
   * 客户店铺
   */
  customerShopId?: string | number;

  /**
   * 客户
   */
  customerId?: string | number;

  /**
   * 发票抬头
   */
  invoiceTitleId?: string | number;

  /**
   * 兑换有效期
   */
  rechargeValidityDates?: string[];

  /**
   * 兑换有效期开始时间
   */
  rechargeStartDate?: string;

  /**
   * 兑换有效期结束时间
   */
  rechargeEndDate?: string;

  /**
   * 客户要求
   */
  customerDemand?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 总面值
   */
  totalValues?: number;

  /**
   * 总明细金额
   */
  totalAmount?: number;

  /**
   * 取整优惠金额
   */
  discountAmount?: number;

  /**
   * 订单金额
   */
  orderAmount?: number;

  /**
   * 折扣率
   */
  discountPercentage?: number;

  /**
   * 订单状态
   */
  orderStatus?: string;

  /**
   * 订单状态修改时间
   */
  statusChangeTime?: string;

  /**
   * 是否预收款
   */
  isAdvanceReceipt?: string;

  /**
   * 预收金额
   */
  receiptAmount?: number;

  /**
   * 收款日期
   */
  receiptDate?: string;

  /**
   * 收款备注
   */
  receiptNote?: string;

  /**
   * 收款附件
   */
  receiptFiles?: string;

  /**
   * 开卡时间
   */
  openCardTime?: string;

  /**
   * 开卡凭证
   */
  certificateFiles?: string;

  /**
   * 开卡备注
   */
  openCardNote?: string;

  /**
   * 总面值（元）
   */
  totalValuesYuan?: number;

  /**
   * 总明细（元）
   */
  totalAmountYuan?: number;

  /**
   * 取整优惠金额（元）
   */
  discountAmountYuan?: number;

  /**
   * 订单金额（元）
   */
  orderAmountYuan?: number;
}

export interface RechargeOrderQuery extends PageQuery {
  /**
   * 订单号
   */
  orderNo?: string;

  /**
   * 订单类型
   */
  orderType?: string;

  /**
   * 订单用途
   */
  purpose?: string;

  /**
   * 承做人
   */
  ownerId?: string | number;

  /**
   * 承做部门
   */
  ownerDeptId?: string | number;

  /**
   * 客户店铺
   */
  customerShopId?: string | number;

  /**
   * 客户
   */
  customerId?: string | number;

  /**
   * 总面值
   */
  totalValues?: number;

  /**
   * 订单状态
   */
  orderStatus?: string;

  /**
   * 订单状态修改时间
   */
  statusChangeTime?: string;

  /**
   * 是否预收款
   */
  isAdvanceReceipt?: string;

  /**
   * 预收金额
   */
  receiptAmount?: number;

  /**
   * 收款日期
   */
  receiptDate?: string;

  /**
   * 开卡时间
   */
  openCardTime?: string;

  /**
   * 更新者
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
