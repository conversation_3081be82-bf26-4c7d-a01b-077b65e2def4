import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { RechargeOrderVO, RechargeOrderForm, RechargeOrderQuery } from '@/api/recharge/rechargeOrder/types';

/**
 * 查询售码订单列表
 * @param query
 * @returns {*}
 */

export const listRechargeOrder = (query?: RechargeOrderQuery): AxiosPromise<RechargeOrderVO[]> => {
  return request({
    url: '/recharge/rechargeOrder/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询售码订单详细
 * @param id
 */
export const getRechargeOrder = (id: string | number): AxiosPromise<RechargeOrderVO> => {
  return request({
    url: '/recharge/rechargeOrder/' + id,
    method: 'get'
  });
};

/**
 * 新增售码订单
 * @param data
 */
export const addRechargeOrder = (data: RechargeOrderForm) => {
  return request({
    url: '/recharge/rechargeOrder',
    method: 'post',
    data: data
  });
};

/**
 * 修改售码订单
 * @param data
 */
export const updateRechargeOrder = (data: RechargeOrderForm) => {
  return request({
    url: '/recharge/rechargeOrder',
    method: 'put',
    data: data
  });
};

/**
 * 删除售码订单
 * @param id
 */
export const delRechargeOrder = (id: string | number | Array<string | number>) => {
  return request({
    url: '/recharge/rechargeOrder/' + id,
    method: 'delete'
  });
};

/**
 * 提交订单，等待审核
 */
export const submitRechargeOrder = (id: string | number) => {
  return request({
    url: '/recharge/rechargeOrder/submit/' + id,
    method: 'put'
  });
};

/**
 * 审核售码订单
 * @param data
 */
export const auditRechargeOrder = (data: RechargeOrderForm) => {
  return request({
    url: '/recharge/rechargeOrder/audit',
    method: 'put',
    data: data
  });
};

/**
 *  完成出卡
 * @param id
 */
export const cardOutFinish = (id: string | number) => {
  return request({
    url: '/recharge/rechargeOrder/cardOut/' + id,
    method: 'put'
  });
};

/**
 * 订单开卡，订单将流转到终态『已完成』
 * @param data
 */
export const openCard = (data: RechargeOrderForm) => {
  return request({
    url: '/recharge/rechargeOrder/openCard',
    method: 'put',
    data: data
  });
};

/**
 * 作废订单
 * @param data
 */
export const voidRechargeOrder = (data: { id: string; remark: string }) => {
  return request({
    url: '/recharge/rechargeOrder/void',
    method: 'put',
    data: data
  });
};
