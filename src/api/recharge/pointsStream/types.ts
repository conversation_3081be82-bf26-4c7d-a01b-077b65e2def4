export interface PointsStreamVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 积分账户
   */
  pointsAccountId: string | number;

  /**
   * 客户店铺id
   */
  customerShopId: string | number;
  /**
   * 充值人手机
   */
  rechargeOpPhone: string;

  /**
   * 流水类型
   */
  streamType: string;

  /**
   * 流水原因
   */
  streamReason: string;

  /**
   * 流水金额
   */
  streamAmount: number;

  /**
   * 流水前余额
   */
  balanceBefore: number;

  /**
   * 流水后余额
   */
  balanceAfter: number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新者
   */
  updateBy: number;

  /**
   * 更新者昵称
   */
  updateNickName: string;
  /**
   * 更新时间
   */
  updateTime: string;
}

export interface PointsStreamForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 积分账户
   */
  pointsAccountId?: string | number;

  /**
   * 客户店铺id
   */
  customerShopId?: string | number;

  /**
   * 充值人手机
   */
  rechargeOpPhone?: string;

  /**
   * 流水类型
   */
  streamType?: string;

  /**
   * 流水原因
   */
  streamReason?: string;

  /**
   * 流水金额
   */
  streamAmount?: number;

  /**
   * 流水前余额
   */
  balanceBefore?: number;

  /**
   * 流水后余额
   */
  balanceAfter?: number;

  /**
   * 备注
   */
  remark?: string;
}

export interface PointsStreamQuery extends PageQuery {
  /**
   * 积分账户
   */
  pointsAccountId?: string | number;

  /**
   * 客户店铺id
   */
  customerShopId?: string | number;

  /**
   * 充值人手机
   */
  rechargeOpPhone?: string;

  /**
   * 流水类型
   */
  streamType?: string;

  /**
   * 流水原因
   */
  streamReason?: string;

  /**
   * 流水金额
   */
  streamAmount?: number;

  /**
   * 流水前余额
   */
  balanceBefore?: number;

  /**
   * 流水后余额
   */
  balanceAfter?: number;

  /**
   * 更新者
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
