import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PointsStreamVO, PointsStreamForm, PointsStreamQuery } from '@/api/recharge/pointsStream/types';

/**
 * 查询积分流水列表
 * @param query
 * @returns {*}
 */

export const listPointsStream = (query?: PointsStreamQuery): AxiosPromise<PointsStreamVO[]> => {
  return request({
    url: '/recharge/pointsStream/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询积分流水详细
 * @param id
 */
export const getPointsStream = (id: string | number): AxiosPromise<PointsStreamVO> => {
  return request({
    url: '/recharge/pointsStream/' + id,
    method: 'get'
  });
};

/**
 * 新增积分流水
 * @param data
 */
export const addPointsStream = (data: PointsStreamForm) => {
  return request({
    url: '/recharge/pointsStream',
    method: 'post',
    data: data
  });
};

/**
 * 修改积分流水
 * @param data
 */
export const updatePointsStream = (data: PointsStreamForm) => {
  return request({
    url: '/recharge/pointsStream',
    method: 'put',
    data: data
  });
};

/**
 * 删除积分流水
 * @param id
 */
export const delPointsStream = (id: string | number | Array<string | number>) => {
  return request({
    url: '/recharge/pointsStream/' + id,
    method: 'delete'
  });
};
