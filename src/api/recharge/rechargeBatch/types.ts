export interface RechargeBatchVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 批次号
   */
  batchNumber: string;

  /**
   * 批次名称
   */
  batchName: string;

  /**
   * 批次状态
   */
  batchStatus: string;

  /**
   * 销售范围
   */
  saleScope: string;

  /**
   * 制码数量
   */
  generateQty: number;

  /**
   * 可售数量
   */
  availableSaleQty: number;

  /**
   * 销售中数量
   */
  onSaleQty: number;

  /**
   * 卡面形态
   */
  cardForm: string;

  /**
   * 面值
   */
  faceValue: number;

  /**
   * 生效时间
   */
  effectiveTime: string;

  /**
   * 失效时间
   */
  expireTime: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新者
   */
  updateBy: number;

  /**
   * 更新者昵称
   */
  updateNickName: string;
  /**
   * 更新时间
   */
  updateTime: string;
}

export interface RechargeBatchForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 批次号
   */
  batchNumber?: string;

  /**
   * 批次名称
   */
  batchName?: string;

  /**
   * 批次状态
   */
  batchStatus?: string;

  /**
   * 销售范围
   */
  saleScope?: string;

  /**
   * 制码数量
   */
  generateQty?: number;

  /**
   * 卡面形态
   */
  cardForm?: string;

  /**
   * 面值
   */
  faceValue?: number;

  /**
   * 生效时间
   */
  effectiveTime?: string;

  /**
   * 失效时间
   */
  expireTime?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface RechargeBatchQuery extends PageQuery {
  /**
   * 批次号
   */
  batchNumber?: string;

  /**
   * 批次名称
   */
  batchName?: string;

  /**
   * 批次状态
   */
  batchStatus?: string;

  /**
   * 销售范围
   */
  saleScope?: string;

  /**
   * 卡面形态
   */
  cardForm?: string;

  /**
   * 生效时间
   */
  effectiveTime?: string;

  /**
   * 失效时间
   */
  expireTime?: string;

  /**
   * 更新者
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
