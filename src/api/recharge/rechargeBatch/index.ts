import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { RechargeBatchVO, RechargeBatchForm, RechargeBatchQuery } from '@/api/recharge/rechargeBatch/types';

/**
 * 查询制码批次列表
 * @param query
 * @returns {*}
 */

export const listRechargeBatch = (query?: RechargeBatchQuery): AxiosPromise<RechargeBatchVO[]> => {
  return request({
    url: '/recharge/rechargeBatch/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询制码批次列表，只展示可销售数量大于0的批次
 */
export const listRechargeBatchExcludeZero = (query?: RechargeBatchQuery): AxiosPromise<RechargeBatchVO[]> => {
  return request({
    url: '/recharge/rechargeBatch/listExcludeZero',
    method: 'get',
    params: query
  });
};

/**
 * 查询制码批次详细
 * @param id
 */
export const getRechargeBatch = (id: string | number): AxiosPromise<RechargeBatchVO> => {
  return request({
    url: '/recharge/rechargeBatch/' + id,
    method: 'get'
  });
};

/**
 * 新增制码批次
 * @param data
 */
export const addRechargeBatch = (data: RechargeBatchForm) => {
  return request({
    url: '/recharge/rechargeBatch',
    method: 'post',
    data: data
  });
};

/**
 * 修改制码批次
 * @param data
 */
export const updateRechargeBatch = (data: RechargeBatchForm) => {
  return request({
    url: '/recharge/rechargeBatch',
    method: 'put',
    data: data
  });
};

/**
 * 删除制码批次
 * @param id
 */
export const delRechargeBatch = (id: string | number | Array<string | number>) => {
  return request({
    url: '/recharge/rechargeBatch/' + id,
    method: 'delete'
  });
};

/**
 * 提交制卡批次，准备制卡
 * @param id
 */
export const submitRechargeBatch = (id: string | number) => {
  return request({
    url: '/recharge/rechargeBatch/submit/' + id,
    method: 'put'
  });
};

/**
 * 将实体卡批次流转到制卡中，激活导出卡密的按钮
 */
export const batchToMakePhysicalCard = (id: string | number) => {
  return request({
    url: '/recharge/rechargeBatch/makeCard/' + id,
    method: 'put'
  });
};

/**
 * 完成实体卡制卡，卡可以销售
 */
export const finishMakePhysicalCard = (id: string | number) => {
  return request({
    url: '/recharge/rechargeBatch/makeCardFinish/' + id,
    method: 'put'
  });
};

/**
 * 查询batchNumber查询批次详情
 * @param batchNumber
 */
export const getRechargeBatchByBatchNumber = (batchNumber: string) => {
  return request({
    url: '/recharge/rechargeBatch/queryByBatchNumber/' + batchNumber,
    method: 'get'
  });
};

// 根据批次号作废充值码
export const voidByBatchNumber = (data: { batchNumber: string }) => {
  return request({
    url: '/recharge/rechargeCard/voidByBatchNumber',
    method: 'put',
    data: data
  });
};
