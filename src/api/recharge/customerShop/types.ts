export interface CustomerShopVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 名称
   */
  shopName: string;

  /**
   * 客户
   */
  customerId: string | number;

  /**
   * 客户名称
   */
  customerName: string;

  /**
   * 客户状态
   */
  customerStatus: string;

  /**
   * 小程序
   */
  wxAppid: string | number;

  /**
   * 状态
   */
  shopStatus: string;

  /**
   * 免运费门槛
   */
  freeShippingThreshold: number;

  /**
   * 运费
   */
  postFee: number;

  /**
   * 结算依据
   */
  settlementBasis: string;

  /**
   * 结算依据Label
   */
  settlementBasisLabel: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新者
   */
  updateBy: number;

  /**
   * 更新人昵称
   */
  updateNickName: string;
  /**
   * 更新时间
   */
  updateTime: string;

  /**
   * 店铺商品数量
   */
  goodsCount?: number;

  /**
   * 积分账户数量
   */
  pointAccountCount?: number;

  /**
   * 报价策略
   */
  quoteStrategy?: string;

  /**
   * 报价策略Label
   */
  quoteStrategyLabel?: string;

  /**
   * 结算价加点
   */
  settlePriceMakeup?: number;

  /**
   * 销售价加点
   */
  sellingPriceMakeup?: number;

  /**
   * 结算价折扣
   */
  settlePriceDiscount?: number;

  /**
   * 销售价折扣
   */
  sellingPriceDiscount?: number;

  /**
   * 海报图片
   */
  posterUrl?: string;
}

export interface CustomerShopForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 名称
   */
  shopName?: string;

  /**
   * 客户
   */
  customerId?: string | number;

  /**
   * 小程序
   */
  wxAppid?: string | number;

  /**
   * 状态
   */
  shopStatus?: string;

  /**
   * 免运费门槛
   */
  freeShippingThreshold?: number;

  /**
   * 运费
   */
  postFee?: number;

  /**
   * 结算依据
   */
  settlementBasis?: string;

  /**
   * 报价策略
   */
  quoteStrategy?: string;

  /**
   * 结算价加点
   */
  settlePriceMakeup?: number;

  /**
   * 销售价加点
   */
  sellingPriceMakeup?: number;

  /**
   * 结算价折扣
   */
  settlePriceDiscount?: number;

  /**
   * 销售价折扣
   */
  sellingPriceDiscount?: number;

  /**
   * 海报图片
   */
  poster?: string;

  /**
   * 海报图片Url
   */
  posterUrl?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface CustomerShopQuery extends PageQuery {
  /**
   * id
   */
  id?: string | number;

  /**
   * 名称
   */
  shopName?: string;

  /**
   * 客户
   */
  customerId?: string | number;

  /**
   * 小程序
   */
  wxAppid?: string | number;

  /**
   * 状态
   */
  shopStatus?: string;

  /**
   * 免运费门槛
   */
  freeShippingThreshold?: number;

  /**
   * 运费
   */
  postFee?: number;

  /**
   * 结算依据
   */
  settlementBasis?: string;

  /**
   * 报价策略
   */
  quoteStrategy?: string;

  /**
   * 更新者
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
