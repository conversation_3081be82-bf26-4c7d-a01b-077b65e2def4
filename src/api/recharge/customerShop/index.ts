import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CustomerShopVO, CustomerShopForm, CustomerShopQuery } from '@/api/recharge/customerShop/types';

/**
 * 查询客户店铺列表
 * @param query
 * @returns {*}
 */

export const listCustomerShop = (query?: CustomerShopQuery): AxiosPromise<CustomerShopVO[]> => {
  return request({
    url: '/recharge/customerShop/list',
    method: 'get',
    params: query
  });
};

/**
 * 生成小程序码
 */
export const generateQrCode = (id: string | number): AxiosPromise<CustomerShopVO[]> => {
  return request({
    url: '/recharge/customerShop/generateQrCode/' + id,
    method: 'get'
  });
}

/**
 * 查询客户店铺详细
 * @param id
 */
export const getCustomerShop = (id: string | number): AxiosPromise<CustomerShopVO> => {
  return request({
    url: '/recharge/customerShop/' + id,
    method: 'get'
  });
};

/**
 * 新增客户店铺
 * @param data
 */
export const addCustomerShop = (data: CustomerShopForm) => {
  return request({
    url: '/recharge/customerShop',
    method: 'post',
    data: data
  });
};

/**
 * 修改客户店铺
 * @param data
 */
export const updateCustomerShop = (data: CustomerShopForm) => {
  return request({
    url: '/recharge/customerShop',
    method: 'put',
    data: data
  });
};

/**
 * 删除客户店铺
 * @param id
 */
export const delCustomerShop = (id: string | number | Array<string | number>) => {
  return request({
    url: '/recharge/customerShop/' + id,
    method: 'delete'
  });
};

/**
 * 校验名称的唯一性
 */
export const checkShopNameUnique = (data: CustomerShopForm): AxiosPromise<boolean> => {
  return request({
    url: '/recharge/customerShop/checkNameUnique',
    method: 'get',
    params: data
  });
};
