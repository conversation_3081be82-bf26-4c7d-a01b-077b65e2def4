import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { RechargeCardVO, RechargeCardForm, RechargeCardQuery } from './types';

/**
 * 查询充值码列表
 * @param query
 * @returns {*}
 */
export function listRechargeCard(query?: RechargeCardQuery): AxiosPromise<TableResponseData<RechargeCardVO[]>> {
  return request({
    url: '/recharge/rechargeCard/list',
    method: 'get',
    params: query
  });
}

/**
 * 查询充值码详细
 * @param id
 * @returns {*}
 */
export function getRechargeCard(id: string | number): AxiosPromise<DataResponseData<RechargeCardVO>> {
  return request({
    url: '/recharge/rechargeCard/' + id,
    method: 'get'
  });
}

/**
 * 新增充值码
 * @param data
 * @returns {*}
 */
export function addRechargeCard(data: RechargeCardForm) {
  return request({
    url: '/recharge/rechargeCard',
    method: 'post',
    data: data
  });
}

/**
 * 修改充值码
 * @param data
 * @returns {*}
 */
export function updateRechargeCard(data: RechargeCardForm) {
  return request({
    url: '/recharge/rechargeCard',
    method: 'put',
    data: data
  });
}

/**
 * 删除充值码
 * @param id
 * @returns {*}
 */
export function delRechargeCard(id: string | number | Array<string | number>) {
  return request({
    url: '/recharge/rechargeCard/' + id,
    method: 'delete'
  });
}

/**
 * 按流水号查询可售卡号数量
 * @param query
 * @returns {*}
 */
export const getCardNoCount = (query?: RechargeCardQuery): AxiosPromise<number> => {
  return request({
    url: '/recharge/rechargeCard/availableCardQty',
    method: 'get',
    params: query
  });
};

/**
 * 挂失充值码
 * @param ids 充值码ID数组或单个ID
 * @param remark 备注信息
 */
export function lossRechargeCard(ids: string | number | Array<string | number>, remark: string) {
  return request({
    url: '/recharge/rechargeCard/loss',
    method: 'put',
    data: { ids, remark }
  });
}

/**
 * 解挂充值码
 * @param ids 充值码ID数组或单个ID
 * @param remark 备注信息
 */
export function unLossRechargeCard(ids: string | number | Array<string | number>, remark: string) {
  return request({
    url: '/recharge/rechargeCard/unLossCard',
    method: 'put',
    data: { ids, remark }
  });
}

/**
 * 作废充值码
 * @param ids 充值码ID数组或单个ID
 * @param remark 备注信息
 */
export function voidRechargeCard(ids: string | number | Array<string | number>, remark: string) {
  return request({
    url: '/recharge/rechargeCard/void',
    method: 'put',
    data: { ids, remark }
  });
}

// 调整充值码有效期
export const adjustRechargeCardExpireTime = (data: { ids: (string | number)[]; expireTime: string; remark: string }) => {
  return request({
    url: '/recharge/rechargeCard/adjust',
    method: 'put',
    data: data
  });
};
