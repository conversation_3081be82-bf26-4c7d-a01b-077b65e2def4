import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ShopGoodsVO, ShopGoodsForm, ShopGoodsQuery, CandidateGoodsQuery } from '@/api/recharge/shopGoods/types';
import { GoodsVO } from '@/api/mall/goods/types';

/**
 * 查询客户店铺明细列表
 * @param query
 * @returns {*}
 */

export const listShopGoods = (query?: ShopGoodsQuery): AxiosPromise<ShopGoodsVO[]> => {
  return request({
    url: '/recharge/shopGoods/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询客户店铺明细详细
 * @param id
 */
export const getShopGoods = (id: string | number): AxiosPromise<ShopGoodsVO> => {
  return request({
    url: '/recharge/shopGoods/' + id,
    method: 'get'
  });
};

/**
 * 新增客户店铺明细
 * @param data
 */
export const addShopGoods = (data: ShopGoodsForm) => {
  return request({
    url: '/recharge/shopGoods',
    method: 'post',
    data: data
  });
};

/**
 * 修改客户店铺明细
 * @param data
 */
export const updateShopGoods = (data: ShopGoodsForm) => {
  return request({
    url: '/recharge/shopGoods',
    method: 'put',
    data: data
  });
};

/**
 * 删除客户店铺明细
 * @param id
 */
export const delShopGoods = (id: string | number | Array<string | number>) => {
  return request({
    url: '/recharge/shopGoods/' + id,
    method: 'delete'
  });
};

/**
 * 校验唯一性
 */
export const checkUnique = (data: ShopGoodsForm): AxiosPromise<boolean> => {
  return request({
    url: '/recharge/shopGoods/checkUnique',
    method: 'get',
    params: data
  });
};

/**
 * 查询候选商品列表
 */
export const listCandidateGoods = (query?: CandidateGoodsQuery): AxiosPromise<GoodsVO[]> => {
  return request({
    url: '/recharge/shopGoods/candidateList',
    method: 'get',
    params: query
  });
};

/**
 * 批量添加店铺商品
 */
export const batchAddShopGoods = (data: ShopGoodsForm[]): AxiosPromise<boolean> => {
  return request({
    url: '/recharge/shopGoods/batchInsert',
    method: 'post',
    data: data
  });
};
