import { GoodsSimpleVO } from '@/api/mall/goods/types';
import { GoodsQuery } from '@/api/mall/goods/types';

export interface ShopGoodsVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 客户店铺
   */
  customerShopId: string | number;

  /**
   * 商品
   */
  goodsId: string | number;

  /**
   * 商品简要信息
   */
  goods: GoodsSimpleVO;
  /**
   * 结算价，跟B结算
   */
  settlePrice: number;

  /**
   * 销售价，C下单价
   */
  sellingPrice: number;

  /**
   * 是否展示
   */
  isShow: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新者
   */
  updateBy: number;

  /**
   * 更新人昵称
   */
  updateNickName: string;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface ShopGoodsForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 客户店铺
   */
  customerShopId?: string | number;

  /**
   * 商品
   */
  goodsId?: string | number;

  /**
   * 结算价，跟B结算
   */
  settlePrice?: number;

  /**
   * 销售价，C下单价
   */
  sellingPrice?: number;

  /**
   * 是否展示
   */
  isShow?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface ShopGoodsQuery extends PageQuery {
  /**
   * 客户店铺
   */
  customerShopId?: string | number;

  /**
   * 商品
   */
  goodsId?: string | number;

  /**
   * 结算价，跟B结算
   */
  settlePrice?: number;

  /**
   * 销售价，C下单价
   */
  sellingPrice?: number;

  /**
   * 是否展示
   */
  isShow?: string;

  /**
   * 更新者
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

/**
 * 候选商品查询
 */
export interface CandidateGoodsQuery extends GoodsQuery {
  /**
   * 客户店铺
   */
  customerShopId?: string | number;
}
