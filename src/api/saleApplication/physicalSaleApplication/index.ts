import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import {
  PhysicalSaleApplicationVO,
  PhysicalSaleApplicationForm,
  PhysicalSaleApplicationQuery
} from '@/api/saleApplication/physicalSaleApplication/types';

/**
 * 查询销售申请（实物）列表
 * @param query
 * @returns {*}
 */

export const listPhysicalSaleApplication = (query?: PhysicalSaleApplicationQuery): AxiosPromise<PhysicalSaleApplicationVO[]> => {
  return request({
    url: '/saleApplication/physicalSaleApplication/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询销售申请（实物）详细
 * @param id
 */
export const getPhysicalSaleApplication = (id: string | number): AxiosPromise<PhysicalSaleApplicationVO> => {
  return request({
    url: '/saleApplication/physicalSaleApplication/' + id,
    method: 'get'
  });
};

/**
 * 新增销售申请（实物）
 * @param data
 */
export const addPhysicalSaleApplication = (data: PhysicalSaleApplicationForm) => {
  return request({
    url: '/saleApplication/physicalSaleApplication',
    method: 'post',
    data: data
  });
};

/**
 * 修改销售申请（实物）
 * @param data
 */
export const updatePhysicalSaleApplication = (data: PhysicalSaleApplicationForm) => {
  return request({
    url: '/saleApplication/physicalSaleApplication',
    method: 'put',
    data: data
  });
};

/**
 * 删除销售申请（实物）
 * @param id
 */
export const delPhysicalSaleApplication = (id: string | number | Array<string | number>) => {
  return request({
    url: '/saleApplication/physicalSaleApplication/' + id,
    method: 'delete'
  });
};

/**
 * 提交销售申请（实物）审核
 * @param id
 */
export const submitPhysicalSaleApplication = (id: string | number) => {
  return request({
    url: '/saleApplication/physicalSaleApplication/submit/' + id,
    method: 'post'
  });
};

/**
 * 作废销售申请（实物）
 * @param id 申请ID
 * @param remark 作废原因
 */
export const revokePhysicalSaleApplication = (id: string | number, remark: string) => {
  return request({
    url: '/saleApplication/physicalSaleApplication/revoke',
    method: 'post',
    data: {
      id,
      remark
    }
  });
};

/**
 * 审核销售申请（实物）
 * @param id 申请ID
 * @param auditStatus 审核状态 02-审核不通过 21-审核通过(待发货)
 * @param remark 审核备注
 */
export const auditPhysicalSaleApplication = (id: string | number, auditStatus: string, remark: string) => {
  return request({
    url: '/saleApplication/physicalSaleApplication/audit',
    method: 'post',
    data: {
      id,
      auditStatus,
      remark
    }
  });
};

/**
 * 发货（非一件代发）
 * @param id 申请ID
 * @param remark 发货备注
 */
export const shipPhysicalSaleApplication = (id: string | number, remark: string) => {
  return request({
    url: '/saleApplication/physicalSaleApplication/ship',
    method: 'post',
    data: {
      id,
      remark
    }
  });
};

/**
 * 完成销售申请（实物）
 * @param id 申请ID
 * @param remark 备注
 * @param files 附件
 */
export const finishPhysicalSaleApplication = (id: string | number, remark: string, files: string) => {
  return request({
    url: '/saleApplication/physicalSaleApplication/finish',
    method: 'post',
    data: {
      id,
      remark,
      files
    }
  });
};
