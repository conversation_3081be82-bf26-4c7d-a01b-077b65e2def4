export interface PhysicalSaleApplicationVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 销售申请单号
   */
  applicationNo: string;

  /**
   * 申请用途
   */
  purpose: string;

  /**
   * 承做人
   */
  ownerId: string | number;

  /**
   * 承做部门
   */
  ownerDeptId: string | number;

  /**
   * 客户
   */
  customerId: string | number;

  /**
   * 发票抬头
   */
  invoiceTitleId: string | number;

  /**
   * 下订类型
   */
  bookType: string;

  /**
   * 配送方式
   */
  shippingMethod: string;

  /**
   * 发货仓库
   */
  shippingWarehouse: number;

  /**
   * 期望发货日期
   */
  shippingDate: string;

  /**
   * 发货要求
   */
  shippingRequirements: string;

  /**
   * 商品列表
   */
  goodsList: Array<GoodsOrderItemVo>;

  /**
   * 总值
   */
  totalValues: number;

  /**
   * 总明细金额
   */
  totalAmount: number;

  /**
   * 取整优惠金额
   */
  discountAmount: number;

  /**
   * 订单金额
   */
  orderAmount: number;

  /**
   * 折扣率
   */
  discountPercentage: number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 申请状态
   */
  applicationStatus: string;

  /**
   * 状态修改时间
   */
  statusChangeTime: string;

  /**
   * 是否预收款
   */
  isAdvanceReceipt: string;

  /**
   * 预收金额
   */
  receiptAmount: number;

  /**
   * 收款日期
   */
  receiptDate: string;

  /**
   * 收款备注
   */
  receiptNote: string;

  /**
   * 收货时间
   */
  openCardTime: string;

  /**
   * 收货备注
   */
  openCardNote: string;

  /**
   * 更新者
   */
  updateBy: number;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface PhysicalSaleApplicationForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 销售申请单号
   */
  applicationNo?: string;

  /**
   * 申请用途
   */
  purpose?: string;

  /**
   * 承做人
   */
  ownerId?: string | number;

  /**
   * 承做部门
   */
  ownerDeptId?: string | number;

  /**
   * 客户
   */
  customerId?: string | number;

  /**
   * 发票抬头
   */
  invoiceTitleId?: string | number;

  /**
   * 下订类型
   */
  bookType?: string;

  /**
   * 配送方式
   */
  shippingMethod?: string;

  /**
   * 发货仓库
   */
  shippingWarehouse?: number;

  /**
   * 期望发货日期
   */
  shippingDate?: string;

  /**
   * 发货要求
   */
  shippingRequirements?: string;

  /**
   * 商品列表
   */
  goodsList?: Array<GoodsOrderItemBo>;

  /**
   * 总值
   */
  totalValues?: number;

  /**
   * 总明细金额
   */
  totalAmount?: number;

  /**
   * 取整优惠金额
   */
  discountAmount?: number;

  /**
   * 订单金额
   */
  orderAmount?: number;

  /**
   * 折扣率
   */
  discountPercentage?: number;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 申请状态
   */
  applicationStatus?: string;

  /**
   * 状态修改时间
   */
  statusChangeTime?: string;

  /**
   * 是否预收款
   */
  isAdvanceReceipt?: string;

  /**
   * 预收金额
   */
  receiptAmount?: number;

  /**
   * 收款日期
   */
  receiptDate?: string;

  /**
   * 收款备注
   */
  finishNote?: string;

  /**
   * 收款附件
   */
  receiptFiles?: string;

  /**
   * 收货时间
   */
  finishTime?: string;

  /**
   * 收货凭证
   */
  certificateFiles?: string;

  /**
   * 收货备注
   */
  openCardNote?: string;
}

export interface PhysicalSaleApplicationQuery extends PageQuery {
  /**
   * 销售申请单号
   */
  applicationNo?: string;

  /**
   * 申请用途
   */
  purpose?: string;

  /**
   * 承做人
   */
  ownerId?: string | number;

  /**
   * 承做部门
   */
  ownerDeptId?: string | number;

  /**
   * 客户
   */
  customerId?: string | number;

  /**
   * 发票抬头
   */
  invoiceTitleId?: string | number;

  /**
   * 下订类型
   */
  bookType?: string;

  /**
   * 配送方式
   */
  shippingMethod?: string;

  /**
   * 发货仓库
   */
  shippingWarehouse?: number;

  /**
   * 期望发货日期
   */
  shippingDate?: string;

  /**
   * 申请状态
   */
  applicationStatus?: string;

  /**
   * 状态修改时间
   */
  statusChangeTime?: string;

  /**
   * 是否预收款
   */
  isAdvanceReceipt?: string;

  /**
   * 收款日期
   */
  receiptDate?: string;

  /**
   * 收货时间
   */
  openCardTime?: string;

  /**
   * 更新者
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;

  /*
   * 备注
   */
  receiptNote: string;
}

/**
 * 销售申请单明细Bo
 */
export interface GoodsOrderItemBo {
  /**
   * 商品id
   */
  goodsId: string | number;

  /**
   * 商品数量
   */
  qty: number;

  /**
   * 零售价
   */
  salePrice: number;

  /**
   * 折扣
   */
  discount: number;

  /**
   * 单价
   */
  unitPrice: number;

  /**
   * 明细总价
   */
  detailTotal: number;

  /**
   * 备注
   */
  remark: string;
}

/**
 * 销售申请单明细Vo
 */
export interface GoodsOrderItemVo extends GoodsOrderItemBo {
  /**
   * 商品名称
   */
  goodsName: string;

  /**
   * 商品图片
   */
  goodsImage: string;

  /**
   * 销售单位
   */
  saleUnit?: string;

  /**
   * 销售单位Label
   */
  saleUnitLabel?: string;

  /**
   * 当前零售价
   */
  currentSalePrice?: number;

  /**
   * 当前零售价
   */
  shippingWarehouse?: string;
}
