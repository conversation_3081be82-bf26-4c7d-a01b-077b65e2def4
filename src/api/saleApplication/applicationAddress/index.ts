import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ApplicationAddressVO, ApplicationAddressForm, ApplicationAddressQuery } from '@/api/saleApplication/applicationAddress/types';

/**
 * 查询销售申请发货地址列表
 * @param query
 * @returns {*}
 */

export const listApplicationAddress = (query?: ApplicationAddressQuery): AxiosPromise<ApplicationAddressVO[]> => {
  return request({
    url: '/saleApplication/applicationAddress/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询销售申请发货地址详细
 * @param id
 */
export const getApplicationAddress = (id: string | number): AxiosPromise<ApplicationAddressVO> => {
  return request({
    url: '/saleApplication/applicationAddress/' + id,
    method: 'get'
  });
};

/**
 * 新增销售申请发货地址
 * @param data
 */
export const addApplicationAddress = (data: ApplicationAddressForm) => {
  return request({
    url: '/saleApplication/applicationAddress',
    method: 'post',
    data: data
  });
};

/**
 * 修改销售申请发货地址
 * @param data
 */
export const updateApplicationAddress = (data: ApplicationAddressForm) => {
  return request({
    url: '/saleApplication/applicationAddress',
    method: 'put',
    data: data
  });
};

/**
 * 删除销售申请发货地址
 * @param id
 */
export const delApplicationAddress = (id: string | number | Array<string | number>) => {
  return request({
    url: '/saleApplication/applicationAddress/' + id,
    method: 'delete'
  });
};
