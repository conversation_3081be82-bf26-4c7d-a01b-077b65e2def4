export interface ApplicationAddressVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 水果销售申请单id
   */
  applicationId: string | number;

  /**
   * 份数
   */
  portion: number;

  /**
   * 收件人姓名
   */
  recipientName: string;

  /**
   * 收件人电话
   */
  recipientPhone: string;

  /**
   * 收件人省份
   */
  recipientProvince: string;

  /**
   * 收件人城市
   */
  recipientCity: string;

  /**
   * 收件人区县
   */
  recipientArea: string;

  /**
   * 收件人地址
   */
  recipientAddress: string;

  /**
   * 收货备注
   */
  recipientNote: string;

  /**
   * 商品订单id
   */
  goodsOrderId: string | number;

  /**
   * B端备注
   */
  remark: string;

  /**
   * 更新者
   */
  updateBy: number;

  /**
   * 更新时间
   */
  updateTime: string;

}

export interface ApplicationAddressForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 水果销售申请单id
   */
  applicationId?: string | number;

  /**
   * 份数
   */
  portion?: number;

  /**
   * 收件人姓名
   */
  recipientName?: string;

  /**
   * 收件人电话
   */
  recipientPhone?: string;

  /**
   * 收件人省份
   */
  recipientProvince?: string;

  /**
   * 收件人城市
   */
  recipientCity?: string;

  /**
   * 收件人区县
   */
  recipientArea?: string;

  /**
   * 收件人地址
   */
  recipientAddress?: string;

  /**
   * 收货备注
   */
  recipientNote?: string;

  /**
   * 商品订单id
   */
  goodsOrderId?: string | number;

  /**
   * B端备注
   */
  remark?: string;

}

export interface ApplicationAddressQuery extends PageQuery {

  /**
   * 水果销售申请单id
   */
  applicationId?: string | number;

  /**
   * 份数
   */
  portion?: number;

  /**
   * 收件人姓名
   */
  recipientName?: string;

  /**
   * 收件人电话
   */
  recipientPhone?: string;

  /**
   * 收件人省份
   */
  recipientProvince?: string;

  /**
   * 收件人城市
   */
  recipientCity?: string;

  /**
   * 收件人区县
   */
  recipientArea?: string;

  /**
   * 收件人地址
   */
  recipientAddress?: string;

  /**
   * 收货备注
   */
  recipientNote?: string;

  /**
   * 商品订单id
   */
  goodsOrderId?: string | number;

  /**
   * 更新者
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



