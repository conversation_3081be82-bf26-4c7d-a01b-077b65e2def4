export interface TaxInvoiceTitleVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 客户
   */
  customerId: string | number;

  /**
   * 客户名称
   */
  customerName: string;

  /**
   * 接收电话
   */
  phone: string;

  /**
   * 接收邮箱
   */
  email: string;

  /**
   * 公司名称
   */
  companyName: string;

  /**
   * 税号
   */
  taxNo: string;

  /**
   * 单位地址
   */
  address: string;

  /**
   * 开户银行
   */
  bank: string;

  /**
   * 银行账号
   */
  bankAccount: string;

  /**
   * 状态
   */
  status: string;

  /**
   * 排序
   */
  sort: number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新人
   */
  updateBy: number;

  /**
   * 更新人昵称
   */
  updateNickName: string;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface TaxInvoiceTitleForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 客户
   */
  customerId?: string | number;

  /**
   * 接收电话
   */
  phone?: string;

  /**
   * 接收邮箱
   */
  email?: string;

  /**
   * 公司名称
   */
  companyName?: string;

  /**
   * 税号
   */
  taxNo?: string;

  /**
   * 单位地址
   */
  address?: string;

  /**
   * 单位电话
   */
  telephone?: string;

  /**
   * 开户银行
   */
  bank?: string;

  /**
   * 银行账号
   */
  bankAccount?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 排序
   */
  sort?: number;

  /**
   * 备注
   */
  remark?: string;
}

export interface TaxInvoiceTitleQuery extends PageQuery {
  /**
   * 客户
   */
  customerId?: string | number;

  /**
   * 接收电话
   */
  phone?: string;

  /**
   * 接收邮箱
   */
  email?: string;

  /**
   * 公司名称
   */
  companyName?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 排序
   */
  sort?: number;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
