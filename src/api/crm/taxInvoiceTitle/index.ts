import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { TaxInvoiceTitleVO, TaxInvoiceTitleForm, TaxInvoiceTitleQuery } from '@/api/crm/taxInvoiceTitle/types';

/**
 * 查询税务发票抬头列表
 * @param query
 * @returns {*}
 */

export const listTaxInvoiceTitle = (query?: TaxInvoiceTitleQuery): AxiosPromise<TaxInvoiceTitleVO[]> => {
  return request({
    url: '/crm/taxInvoiceTitle/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询税务发票抬头详细
 * @param id
 */
export const getTaxInvoiceTitle = (id: string | number): AxiosPromise<TaxInvoiceTitleVO> => {
  return request({
    url: '/crm/taxInvoiceTitle/' + id,
    method: 'get'
  });
};

/**
 * 新增税务发票抬头
 * @param data
 */
export const addTaxInvoiceTitle = (data: TaxInvoiceTitleForm) => {
  return request({
    url: '/crm/taxInvoiceTitle',
    method: 'post',
    data: data
  });
};

/**
 * 修改税务发票抬头
 * @param data
 */
export const updateTaxInvoiceTitle = (data: TaxInvoiceTitleForm) => {
  return request({
    url: '/crm/taxInvoiceTitle',
    method: 'put',
    data: data
  });
};

/**
 * 删除税务发票抬头
 * @param id
 */
export const delTaxInvoiceTitle = (id: string | number | Array<string | number>) => {
  return request({
    url: '/crm/taxInvoiceTitle/' + id,
    method: 'delete'
  });
};

/**
 * 获取某客户的抬头数量
 */
export const countTaxInvoiceTitle = (customerId: string | number): AxiosPromise<number> => {
  return request({
    url: '/crm/taxInvoiceTitle/TaxInvoiceTitleCount/' + customerId,
    method: 'get'
  });
};
