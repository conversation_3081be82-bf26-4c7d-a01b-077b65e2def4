import { CustomerVO } from '@/api/crm/customer/types';

export interface OpportunityVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 机会编号
   */
  code: string;

  /**
   * 机会名称
   */
  name: string;

  /**
   * 客户档案
   */
  customerId: string | number;

  /**
   * 客户名称
   */
  customerName: string;

  /**
   * 客户档案
   */
  customer: CustomerVO;

  /**
   * 商机来源，值来自字典「dict_opportunity_source」
   */
  resourceKey: string;

  /**
   * 需求目的，关联字典「dict_demand_purpose」
   */
  demandPurpose: string;

  /**
   * 意向产品，值来自字典「dict_crm_pro」
   */
  productKey: string;

  /**
   * 商机联系人
   */
  contactId: number;

  /**
   * 商机阶段，值来自字典「dict_opportunity_phase」
   */
  phase: string;

  /**
   * 需求描述
   */
  demandDesc: string;

  /**
   * 预计合同金额，单位元
   */
  expectAmount: number;

  /**
   * 预计成交日期
   */
  expectDate: string;

  /**
   * 销售策略，填写在线文档地址
   */
  saleStrategy: string;

  /**
   * 销售场景
   */
  saleScene: string;

  /**
   * 承揽人类型，值来自字典「dict_contractor_type」
   */
  contractorType: string;

  /**
   * 承揽人
   */
  contractor: string;

  /**
   * 承揽备注
   */
  contractRemark: string;

  /**
   * 业务机会范围，值来自「dict_opportunity_scope」
   */
  opportunityScope: string;

  /**
   * 机会类型，值来自「dict_opportunity_type」
   */
  opportunityType: string;

  /**
   * 输单原因，值来来自字典「dict_lose_reason」
   */
  loseReason: string;

  /**
   * 输单描述
   */
  loseDesc: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 赢单附件
   */
  files: string;
}

export interface OpportunityForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 机会编号
   */
  code?: string;

  /**
   * 机会名称
   */
  name?: string;

  /**
   * 客户档案
   */
  customerId?: string | number;

  /**
   * 商机来源，值来自字典「dict_opportunity_source」
   */
  resourceKey?: string;

  /**
   * 需求目的，关联字典「dict_demand_purpose」
   */
  demandPurpose?: string;

  /**
   * 意向产品，值来自字典「dict_crm_pro」
   */
  productKey?: string;

  /**
   * 商机联系人
   */
  contactId?: number;

  /**
   * 商机阶段，值来自字典「dict_opportunity_phase」
   */
  phase?: string;

  /**
   * 需求描述
   */
  demandDesc?: string;

  /**
   * 预计合同金额，单位元
   */
  expectAmount?: number;

  /**
   * 预计成交日期
   */
  expectDate?: string;

  /**
   * 销售策略，填写在线文档地址
   */
  saleStrategy?: string;

  /**
   * 销售场景
   */
  saleScene?: string;

  /**
   * 承揽人类型，值来自字典「dict_contractor_type」
   */
  contractorType?: string;

  /**
   * 承揽人
   */
  contractor?: string;

  /**
   * 承揽备注
   */
  contractRemark?: string;

  /**
   * 业务机会范围，值来自「dict_opportunity_scope」
   */
  opportunityScope?: string;

  /**
   * 机会类型，值来自「dict_opportunity_type」
   */
  opportunityType?: string;

  /**
   * 输单原因，值来来自字典「dict_lose_reason」
   */
  loseReason?: string;

  /**
   * 输单描述
   */
  loseDesc?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 赢单附件
   */
  files?: string;
}

export interface OpportunityQuery extends PageQuery {
  /**
   * 机会编号
   */
  code?: string;

  /**
   * 机会名称
   */
  name?: string;

  /**
   * 客户档案
   */
  customerId?: string | number;

  /**
   * 商机来源，值来自字典「dict_opportunity_source」
   */
  resourceKey?: string;

  /**
   * 需求目的，关联字典「dict_demand_purpose」
   */
  demandPurpose?: string;

  /**
   * 意向产品，值来自字典「dict_crm_pro」
   */
  productKey?: string;

  /**
   * 商机联系人
   */
  contactId?: number;

  /**
   * 商机阶段，值来自字典「dict_opportunity_phase」
   */
  phase?: string;

  /**
   * 需求描述
   */
  demandDesc?: string;

  /**
   * 预计合同金额，单位元
   */
  expectAmount?: number;

  /**
   * 预计成交日期
   */
  expectDate?: string;

  /**
   * 销售策略，填写在线文档地址
   */
  saleStrategy?: string;

  /**
   * 销售场景
   */
  saleScene?: string;

  /**
   * 承揽人类型，值来自字典「dict_contractor_type」
   */
  contractorType?: string;

  /**
   * 承揽人
   */
  contractor?: string;

  /**
   * 承揽备注
   */
  contractRemark?: string;

  /**
   * 业务机会范围，值来自「dict_opportunity_scope」
   */
  opportunityScope?: string;

  /**
   * 机会类型，值来自「dict_opportunity_type」
   */
  opportunityType?: string;

  /**
   * 输单原因，值来来自字典「dict_lose_reason」
   */
  loseReason?: string;

  /**
   * 输单描述
   */
  loseDesc?: string;

  /**
   * 赢单附件
   */
  files?: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
