import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { OpportunityVO, OpportunityForm, OpportunityQuery } from '@/api/crm/opportunity/types';

/**
 * 查询销售机会列表
 * @param query
 * @returns {*}
 */

export const listOpportunity = (query?: OpportunityQuery): AxiosPromise<OpportunityVO[]> => {
  return request({
    url: '/crm/opportunity/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询销售机会详细
 * @param id
 */
export const getOpportunity = (id: string | number): AxiosPromise<OpportunityVO> => {
  return request({
    url: '/crm/opportunity/' + id,
    method: 'get'
  });
};

/**
 * 新增销售机会
 * @param data
 */
export const addOpportunity = (data: OpportunityForm) => {
  return request({
    url: '/crm/opportunity',
    method: 'post',
    data: data
  });
};

/**
 * 修改销售机会
 * @param data
 */
export const updateOpportunity = (data: OpportunityForm) => {
  return request({
    url: '/crm/opportunity',
    method: 'put',
    data: data
  });
};

/**
 * 删除销售机会
 * @param id
 */
export const delOpportunity = (id: string | number | Array<string | number>) => {
  return request({
    url: '/crm/opportunity/' + id,
    method: 'delete'
  });
};
