import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CustomerVO, CustomerForm, CustomerQuery } from '@/api/crm/customer/types';

/**
 * 查询客户档案列表
 * @param query
 * @returns {*}
 */

export const listCustomer = (query?: CustomerQuery): AxiosPromise<CustomerVO[]> => {
  return request({
    url: '/crm/customer/list',
    method: 'get',
    params: query
  });
};

/**
 * 按更新时间查询前20条在公海的客户
 */
export const listHighSeaCustomer = (query?: CustomerQuery): AxiosPromise<CustomerVO[]> => {
  return request({
    url: '/crm/customer/listInHighSea',
    method: 'get',
    params: query
  });
};

/**
 * 查询客户档案详细
 * @param id
 */
export const getCustomer = (id: string | number): AxiosPromise<CustomerVO> => {
  return request({
    url: '/crm/customer/' + id,
    method: 'get'
  });
};

/**
 * 新增客户档案
 * @param data
 */
export const addCustomer = (data: CustomerForm) => {
  return request({
    url: '/crm/customer',
    method: 'post',
    data: data
  });
};

/**
 * 修改客户档案
 * @param data
 */
export const updateCustomer = (data: CustomerForm) => {
  return request({
    url: '/crm/customer',
    method: 'put',
    data: data
  });
};

/**
 * 删除客户档案
 * @param id
 */
export const delCustomer = (id: string | number | Array<string | number>) => {
  return request({
    url: '/crm/customer/' + id,
    method: 'delete'
  });
};

/**
 * 校验名称的唯一性
 */
export const checkCustomerNameUnique = (data: CustomerForm): AxiosPromise<boolean> => {
  return request({
    url: '/crm/customer/checkCustomerNameUnique',
    method: 'get',
    params: data
  });
};
