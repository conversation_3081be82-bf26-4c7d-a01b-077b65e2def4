export interface CustomerVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 客户名称
   */
  name: string;

  /**
   * 客户对外编码
   */
  openCode: string;

  /**
   * 客户状态
   */
  status: string;

  /**
   * 客户联系人数量
   */
  contactNum: number;
  /**
   * 客户编码
   */
  customerCode: string;

  /**
   * 客户群组，关联数据字典「dict_customer_group」
   */
  customerGroup: string;

  /**
   * 客户别称
   */
  aliasName: string;
  /**
   * 承办大区，关联部门表「sys_dept」
   */
  ownerRegion: number;

  /**
   * 承办大区名称
   */
  ownerRegionName: string;

  /**
   * 承办人
   */
  owner: number;

  /**
   * 承办人昵称
   */
  ownerNickName: string;

  /**
   * 承办人所在部门
   */
  ownerDept: number;

  /**
   * 承办部门名称
   */
  ownerDeptName: string;

  /**
   * 协办人
   */
  coOwner: string | number;

  /**
   * 客户级别
   */
  level: string;

  /**
   * 客户类型
   */
  type: string;

  /**
   * 客户来源
   */
  source: string;

  /**
   * 所在行业
   */
  industry: string;

  /**
   * 意向产品分类
   */
  productKey: string;

  /**
   * 需求目的
   */
  demandPurpose: string;

  /**
   * 客户企业规模
   */
  companyScale: string;

  /**
   * 销售场景
   */
  saleScene: string;

  /**
   * 下次跟进日期
   */
  followUpDate: string;

  /**
   * 公海
   */
  highSeaId: string | number;

  /**
   * 上一年成交额
   */
  lastYearDealAmount: number;

  /**
   * 是否已签合同
   */
  isSignedContract: string;

  /**
   * 省份
   */
  provinceId: string;

  /**
   * 省份名称
   */
  provinceName: string;

  /**
   * 城市
   */
  cityId: string;

  /**
   * 城市名称
   */
  cityName: string;

  /**
   * 区县
   */
  districtId: string;

  /**
   * 经度
   */
  lng: string;

  /**
   * 纬度
   */
  lat: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 合同附件等
   */
  files: string;

  /**
   * 更新人
   */
  updateBy: number;

  /**
   * 更新人昵称
   */
  updateByNickName: string;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface CustomerForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 客户名称
   */
  name?: string;

  /**
   * 客户对外编码
   */
  openCode?: string;

  /**
   * 客户状态
   */
  status?: string;

  /**
   * 客户编码
   */
  customerCode?: string;

  /**
   * 客户群组
   */
  customerGroup?: string;

  /**
   * 客户别称
   */
  aliasName?: string;

  /**
   * 承办大区
   */
  ownerRegion?: number;

  /**
   * 承办人
   */
  owner?: number;

  /**
   * 承办部门
   */
  ownerDept?: number;

  /**
   * 协办人
   */
  coOwner?: string | number;

  /**
   * 客户级别
   */
  level?: string;

  /**
   * 客户类型
   */
  type?: string;

  /**
   * 客户来源
   */
  source?: string;

  /**
   * 所在行业
   */
  industry?: string;

  /**
   * 意向产品分类
   */
  productKey?: string;

  /**
   * 需求目的
   */
  demandPurpose?: string;

  /**
   * 客户企业规模
   */
  companyScale?: string;

  /**
   * 销售场景
   */
  saleScene?: string;

  /**
   * 客户描述
   */
  customerDesc?: string;

  /**
   * 下次跟进日期
   */
  followUpDate?: string;

  /**
   * 公海
   */
  highSeaId?: string | number;

  /**
   * 公海名称
   */
  highSeaName?: string;

  /**
   * 上一年成交额
   */
  lastYearDealAmount?: number;

  /**
   * 是否已签合同
   */
  isSignedContract?: string;

  /**
   * 税号
   */
  tin?: string;

  /**
   * 公司名称
   */
  companyName?: string;

  /**
   * 法人代表
   */
  legalPerson?: string;

  /**
   * 经营范围
   */
  bizScope?: string;

  /**
   * 营业执照
   */
  bizLicense?: string;

  /**
   * 地址
   */
  address?: string;

  /**
   * 省份
   */
  provinceId?: string;

  /**
   * 城市
   */
  cityId?: string;

  /**
   * 区县
   */
  districtId?: string;

  /**
   * 经度
   */
  lng?: string;

  /**
   * 纬度
   */
  lat?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 合同附件等
   */
  files?: string;

  /** */
}

export interface CustomerQuery extends PageQuery {
  /**
   * id
   */
  id?: string | number;
  /**
   * 客户名称
   */
  name?: string;

  /**
   * 客户对外编码
   */
  openCode?: string;

  /**
   * 客户状态
   */
  status?: string;

  /**
   * 客户编码
   */
  customerCode?: string;

  /**
   * 客户群组
   */
  customerGroup?: string;

  /**
   * 销售场景
   */
  saleScene?: string;

  /**
   * 客户别称
   */
  aliasName?: string;

  /**
   * 承办大区
   */
  ownerRegion?: number;

  /**
   * 承办人
   */
  owner?: number;

  /**
   * 承办部门
   */
  ownerDept?: number;

  /**
   * 协办人
   */
  coOwner?: string | number;

  /**
   * 客户级别
   */
  level?: string;

  /**
   * 客户类型
   */
  type?: string;

  /**
   * 客户来源
   */
  source?: string;

  /**
   * 所在行业
   */
  industry?: string;

  /**
   * 意向产品分类
   */
  productKey?: string;

  /**
   * 需求目的
   */
  demandPurpose?: string;

  /**
   * 客户企业规模
   */
  companyScale?: string;

  /**
   * 下次跟进日期
   */
  followUpDate?: string;

  /**
   * 是否已签合同
   */
  isSignedContract?: string;

  /**
   * 公海
   */
  highSeaId?: string | number;

  /**
   * 地址
   */
  address?: string;

  /**
   * 省份
   */
  provinceId?: string;

  /**
   * 城市
   */
  cityId?: string;

  /**
   * 区县
   */
  districtId?: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
