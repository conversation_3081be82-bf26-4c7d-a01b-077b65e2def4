export interface ActivityVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 客户ID
   */
  customerId: string | number;

  /**
   * 客户名称
   */
  customerName: string;

  /**
   * 联系人ID
   */
  contactId: string | number;

  /**
   * 联系人姓名
   */
  contactName: string;

  /**
   * 跟进日期
   */
  activityAt: string;

  /**
   * 跟进人
   */
  activityBy: number;

  /**
   * 跟进人昵称
   */
  activityByNickName: string;

  /**
   * 跟进方式
   */
  activityWay: string;

  /**
   * 跟进方式名称
   */
  activityWayLabel: string;

  /**
   * 跟进类型
   */
  type: string;

  /**
   * 跟进类型名称
   */
  typeLabel: string;

  /**
   * 跟进记录
   */
  logDesc: string;

  /**
   * 跟进对象
   */
  activityTo: string;

  /**
   * 跟进对象名称
   */
  activityToLabel: string;

  /**
   * 跟进对象ID
   */
  toId: string | number;

  /**
   * 更新人
   */
  updateBy: number;

  /**
   * 更新时间
   */
  updateTime: string;

  /**
   * 备注
   */
  remark: string;
}

export interface ActivityForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 客户名称
   */
  customerId?: string | number;

  /**
   * 联系人
   */
  contactId?: string | number;

  /**
   * 跟进日期
   */
  activityAt?: string;

  /**
   * 跟进人
   */
  activityBy?: number;

  /**
   * 跟进方式
   */
  activityWay?: string;

  /**
   * 跟进类型
   */
  type?: string;

  /**
   * 跟进记录
   */
  logDesc?: string;

  /**
   * 跟进对象
   */
  activityTo?: string;

  /**
   * 跟进对象ID
   */
  toId?: string | number;

  /**
   * 附件
   */
  files?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface ActivityQuery extends PageQuery {
  /**
   * 客户名称
   */
  customerId?: string | number;

  /**
   * 联系人
   */
  contactId?: string | number;

  /**
   * 跟进日期
   */
  activityAt?: string;

  /**
   * 跟进人
   */
  activityBy?: number;

  /**
   * 跟进方式
   */
  activityWay?: string;

  /**
   * 跟进类型
   */
  type?: string;

  /**
   * 跟进对象
   */
  activityTo?: string;

  /**
   * 跟进对象ID
   */
  toId?: string | number;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
