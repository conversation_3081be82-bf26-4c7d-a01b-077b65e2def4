import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ActivityVO, ActivityForm, ActivityQuery } from '@/api/crm/activity/types';

/**
 * 查询跟进记录列表
 * @param query
 * @returns {*}
 */

export const listActivity = (query?: ActivityQuery): AxiosPromise<ActivityVO[]> => {
  return request({
    url: '/crm/activity/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询跟进记录详细
 * @param id
 */
export const getActivity = (id: string | number): AxiosPromise<ActivityVO> => {
  return request({
    url: '/crm/activity/' + id,
    method: 'get'
  });
};

/**
 * 新增跟进记录
 * @param data
 */
export const addActivity = (data: ActivityForm) => {
  return request({
    url: '/crm/activity',
    method: 'post',
    data: data
  });
};

/**
 * 修改跟进记录
 * @param data
 */
export const updateActivity = (data: ActivityForm) => {
  return request({
    url: '/crm/activity',
    method: 'put',
    data: data
  });
};

/**
 * 删除跟进记录
 * @param id
 */
export const delActivity = (id: string | number | Array<string | number>) => {
  return request({
    url: '/crm/activity/' + id,
    method: 'delete'
  });
};
