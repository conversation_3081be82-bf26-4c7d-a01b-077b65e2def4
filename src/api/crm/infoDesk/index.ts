import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CustomerByStatus, OpportunityByPhase, ClueByStatus } from './type';

/**
 * 查询客户档案状态统计
 * @returns
 */
export const getCustomerByStatus = (): AxiosPromise<CustomerByStatus[]> => {
  return request({
    url: '/crm/infoDesk/customer-status',
    method: 'get'
  });
};

/**
 * 查询销售机会阶段统计
 * @returns
 */
export const getOpportunityByPhase = (): AxiosPromise<OpportunityByPhase[]> => {
  return request({
    url: '/crm/infoDesk/opportunity-phase',
    method: 'get'
  });
};

/**
 * 查询客户线索的状态统计
 */
export const getClueByStatus = (): AxiosPromise<ClueByStatus[]> => {
  return request({
    url: '/crm/infoDesk/clue-status',
    method: 'get'
  });
};
