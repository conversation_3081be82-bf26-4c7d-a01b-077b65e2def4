/**
 * 客户档案状态统计
 */
export interface CustomerByStatus {
  /**
   * 状态key
   */
  status: string;

  /**
   * 状态名称
   */
  statusLabel: string;

  /**
   * 数量
   */
  count: number;
}

/**
 * 销售机会阶段统计
 */
export interface OpportunityByPhase {
  /**
   * 阶段key
   */
  phase: string;

  /**
   * 阶段Label
   */
  phaseLabel: string;

  /**
   * 数量
   */
  count: number;

  /**
   * 预期金额
   */
  totalExpectAmount: number;
}

/**
 * 客户线索状态统计
 */
export interface ClueByStatus {
  /**
   * 状态key
   */
  status: string;

  /**
   * 状态名称
   */
  statusLabel: string;

  /**
   * 数量
   */
  count: number;
}
