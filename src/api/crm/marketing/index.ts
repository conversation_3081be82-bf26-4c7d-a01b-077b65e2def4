import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MarketingVO, MarketingForm, MarketingQuery } from '@/api/crm/marketing/types';

/**
 * 查询市场活动列表
 * @param query
 * @returns {*}
 */

export const listMarketing = (query?: MarketingQuery): AxiosPromise<MarketingVO[]> => {
  return request({
    url: '/crm/marketing/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询市场活动详细
 * @param id
 */
export const getMarketing = (id: string | number): AxiosPromise<MarketingVO> => {
  return request({
    url: '/crm/marketing/' + id,
    method: 'get'
  });
};

/**
 * 新增市场活动
 * @param data
 */
export const addMarketing = (data: MarketingForm) => {
  return request({
    url: '/crm/marketing',
    method: 'post',
    data: data
  });
};

/**
 * 修改市场活动
 * @param data
 */
export const updateMarketing = (data: MarketingForm) => {
  return request({
    url: '/crm/marketing',
    method: 'put',
    data: data
  });
};

/**
 * 删除市场活动
 * @param id
 */
export const delMarketing = (id: string | number | Array<string | number>) => {
  return request({
    url: '/crm/marketing/' + id,
    method: 'delete'
  });
};
