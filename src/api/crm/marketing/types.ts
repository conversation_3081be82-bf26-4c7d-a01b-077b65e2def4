export interface MarketingVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 活动名称
   */
  name: string;

  /**
   * 活动描述
   */
  description: string;

  /**
   * 活动类型
   */
  type: string;

  /**
   * 活动状态
   */
  status: string;

  /**
   * 活动状态标签
   */
  statusLabel: string;

  /**
   * 开始时间
   */
  startDate: string;

  /**
   * 结束时间
   */
  endDate: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新人
   */
  updateBy: number;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface MarketingForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 活动名称
   */
  name?: string;

  /**
   * 活动描述
   */
  description?: string;

  /**
   * 活动类型
   */
  type?: string;

  /**
   * 活动状态
   */
  status?: string;

  /**
   * 开始时间
   */
  startDate?: string;

  /**
   * 结束时间
   */
  endDate?: string;

  /**
   * 附件
   */
  files?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface MarketingQuery extends PageQuery {
  /**
   * 活动名称
   */
  name?: string;

  /**
   * 活动描述
   */
  description?: string;

  /**
   * 活动类型
   */
  type?: string;

  /**
   * 活动状态
   */
  status?: string;

  /**
   * 开始时间
   */
  startDate?: string;

  /**
   * 结束时间
   */
  endDate?: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
