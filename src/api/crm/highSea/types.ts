export interface HighSeaVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 公海名称
   */
  seaName: string;

  /**
   * 公海状态
   */
  status: string;

  /**
   * 所属部门
   */
  deptId: string | number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新人
   */
  updateBy: number;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface HighSeaForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 公海名称
   */
  seaName?: string;

  /**
   * 公海状态
   */
  status?: string;

  /**
   * 所属部门
   */
  deptId?: string | number;

  /**
   * 备注
   */
  remark?: string;
}

export interface HighSeaQuery extends PageQuery {
  /**
   * 公海名称
   */
  seaName?: string;

  /**
   * 公海状态
   */
  status?: string;

  /**
   * 所属部门
   */
  deptId?: string | number;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
