import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { HighSeaVO, HighSeaForm, HighSeaQuery } from '@/api/crm/highSea/types';

/**
 * 查询公海列表
 * @param query
 * @returns {*}
 */

export const listHighSea = (query?: HighSeaQuery): AxiosPromise<HighSeaVO[]> => {
  return request({
    url: '/crm/highSea/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询公海详细
 * @param id
 */
export const getHighSea = (id: string | number): AxiosPromise<HighSeaVO> => {
  return request({
    url: '/crm/highSea/' + id,
    method: 'get'
  });
};

/**
 * 新增公海
 * @param data
 */
export const addHighSea = (data: HighSeaForm) => {
  return request({
    url: '/crm/highSea',
    method: 'post',
    data: data
  });
};

/**
 * 修改公海
 * @param data
 */
export const updateHighSea = (data: HighSeaForm) => {
  return request({
    url: '/crm/highSea',
    method: 'put',
    data: data
  });
};

/**
 * 删除公海
 * @param id
 */
export const delHighSea = (id: string | number | Array<string | number>) => {
  return request({
    url: '/crm/highSea/' + id,
    method: 'delete'
  });
};
