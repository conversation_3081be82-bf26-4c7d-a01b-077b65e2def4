import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ClueVO, ClueForm, ClueQuery } from '@/api/crm/clue/types';

/**
 * 查询客户线索列表
 * @param query
 * @returns {*}
 */

export const listClue = (query?: ClueQuery): AxiosPromise<ClueVO[]> => {
  return request({
    url: '/crm/clue/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询客户线索详细
 * @param id
 */
export const getClue = (id: string | number): AxiosPromise<ClueVO> => {
  return request({
    url: '/crm/clue/' + id,
    method: 'get'
  });
};

/**
 * 新增客户线索
 * @param data
 */
export const addClue = (data: ClueForm) => {
  return request({
    url: '/crm/clue',
    method: 'post',
    data: data
  });
};

/**
 * 修改客户线索
 * @param data
 */
export const updateClue = (data: ClueForm) => {
  return request({
    url: '/crm/clue',
    method: 'put',
    data: data
  });
};

/**
 * 删除客户线索
 * @param id
 */
export const delClue = (id: string | number | Array<string | number>) => {
  return request({
    url: '/crm/clue/' + id,
    method: 'delete'
  });
};
