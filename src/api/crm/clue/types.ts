export interface ClueVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 客户名称
   */
  customer: string;

  /**
   * 行业
   */
  industryKey: string;

  /**
   * 联系人姓名
   */
  contactName: string;

  /**
   * 联系人手机
   */
  contactPhone: string;

  /**
   * 联系人角色
   */
  contactRole: string;

  /**
   * 意向产品
   */
  productKey: string;

  /**
   * 需求目的
   */
  demandPurpose: string;

  /**
   * 需求描述
   */
  demandDesc: string;

  /**
   * 需求描述链接
   */
  demandUrl: string;

  /**
   * 商机来源
   */
  sourceKey: string;

  /**
   * 商机状态
   */
  status: string;

  /**
   * 市场活动
   */
  marktingId: string | number;

  /**
   * 业务员
   */
  owner: number;

  /**
   * 业务员昵称
   */
  ownerNickName: string;

  /**
   * 业务部门
   */
  ownerDept: number;

  /**
   * 业务部门名称
   */
  ownerDeptName: string;

  /**
   * 分配时间
   */
  assignTime: string;

  /**
   * 关闭原因
   */
  closeReason: string;

  /**
   * 关闭原因描述
   */
  closeDesc: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 省份
   */
  provinceId: string | number;

  /**
   * 省份名称
   */
  provinceName: string;

  /**
   * 城市
   */
  cityId: string | number;

  /**
   * 城市名称
   */
  cityName: string;

  /**
   * 区县
   */
  districtId: string | number;

  /**
   * 区县名称
   */
  districtName: string;

  /**
   * 客户档案
   */
  customerId: string | number;

  /**
   * 销售机会
   */
  opportunityId: string | number;

  /**
   * 更新人
   */
  updateBy: number;

  /**
   * 更新时间
   */
  updateTime: string;

  /**
   * 创建时间
   */
  createTime: string;
}

export interface ClueForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 客户名称
   */
  customer?: string;

  /**
   * 行业
   */
  industryKey?: string;

  /**
   * 联系人姓名
   */
  contactName?: string;

  /**
   * 联系人手机
   */
  contactPhone?: string;

  /**
   * 联系人角色
   */
  contactRole?: string;

  /**
   * 意向产品
   */
  productKey?: string;

  /**
   * 需求目的
   */
  demandPurpose?: string;

  /**
   * 需求描述
   */
  demandDesc?: string;

  /**
   * 需求描述链接
   */
  demandUrl?: string;

  /**
   * 商机来源
   */
  sourceKey?: string;

  /**
   * 商机状态
   */
  status?: string;

  /**
   * 市场活动
   */
  marktingId?: string | number;

  /**
   * 业务员
   */
  owner?: number;

  /**
   * 业务部门
   */
  ownerDept?: number;

  /**
   * 分配时间
   */
  assignTime?: string;

  /**
   * 关闭原因
   */
  closeReason?: string;

  /**
   * 关闭原因描述
   */
  closeDesc?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 地址
   */
  address?: string;

  /**
   * 省份
   */
  provinceId?: string | number;

  /**
   * 城市
   */
  cityId?: string | number;

  /**
   * 区县
   */
  districtId?: string | number;

  /**
   * 经度
   */
  lng?: string;

  /**
   * 纬度
   */
  lat?: string;

  /**
   * 客户档案
   */
  customerId?: string | number;

  /**
   * 销售机会
   */
  opportunityId?: string | number;
}

export interface ClueQuery extends PageQuery {
  /**
   * 客户名称
   */
  customer?: string;

  /**
   * 行业
   */
  industryKey?: string;

  /**
   * 联系人姓名
   */
  contactName?: string;

  /**
   * 联系人手机
   */
  contactPhone?: string;

  /**
   * 联系人角色
   */
  contactRole?: string;

  /**
   * 意向产品
   */
  productKey?: string;

  /**
   * 需求目的
   */
  demandPurpose?: string;

  /**
   * 需求描述
   */
  demandDesc?: string;

  /**
   * 商机来源
   */
  sourceKey?: string;

  /**
   * 商机状态
   */
  status?: string;

  /**
   * 市场活动
   */
  marktingId?: string | number;

  /**
   * 业务员
   */
  owner?: number;

  /**
   * 业务部门
   */
  ownerDept?: number;

  /**
   * 分配时间
   */
  assignTime?: string;

  /**
   * 关闭原因
   */
  closeReason?: string;

  /**
   * 省份
   */
  provinceId?: string;

  /**
   * 城市
   */
  cityId?: string;

  /**
   * 区县
   */
  districtId?: string;

  /**
   * 客户档案
   */
  customerId?: string | number;

  /**
   * 销售机会
   */
  opportunityId?: string | number;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
