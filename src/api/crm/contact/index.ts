import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ContactVO, ContactForm, ContactQuery } from '@/api/crm/contact/types';

/**
 * 查询联系人列表
 * @param query
 * @returns {*}
 */

export const listContact = (query?: ContactQuery): AxiosPromise<ContactVO[]> => {
  return request({
    url: '/crm/contact/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询联系人详细
 * @param id
 */
export const getContact = (id: string | number): AxiosPromise<ContactVO> => {
  return request({
    url: '/crm/contact/' + id,
    method: 'get'
  });
};

/**
 * 新增联系人
 * @param data
 */
export const addContact = (data: ContactForm) => {
  return request({
    url: '/crm/contact',
    method: 'post',
    data: data
  });
};

/**
 * 修改联系人
 * @param data
 */
export const updateContact = (data: ContactForm) => {
  return request({
    url: '/crm/contact',
    method: 'put',
    data: data
  });
};

/**
 * 删除联系人
 * @param id
 */
export const delContact = (id: string | number | Array<string | number>) => {
  return request({
    url: '/crm/contact/' + id,
    method: 'delete'
  });
};
