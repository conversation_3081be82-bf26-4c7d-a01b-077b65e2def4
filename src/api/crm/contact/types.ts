import { CustomerVO } from '@/api/crm/customer/types';

export interface ContactVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 客户名称
   */
  customerId: string | number;

  /**
   * 客户档案
   */
  customer: CustomerVO;

  /**
   * 姓名
   */
  name: string;

  /**
   * 联系人角色
   */
  role: string;

  /**
   * 联系电话
   */
  phone: string;

  /**
   * 微信号
   */
  wxId: string | number;

  /**
   * 邮箱
   */
  email: string;

  /**
   * 在职状态
   */
  status: string;

  /**
   * 性别
   */
  gender: string;

  /**
   * 职务
   */
  position: string;
  /**
   * 是否主要联系人
   */
  isPrimary: string;

  /**
   * 行为取向
   */
  behavioral: string;

  /**
   * 性格特征
   */
  personality: string;

  /**
   * 对我方立场
   */
  positionOnMe: string;

  /**
   * 交往程度
   */
  communicate: string;

  /**
   * 个人爱好
   */
  hobby: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新人
   */
  updateBy: number;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface ContactForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 客户名称
   */
  customerId?: string | number;

  /**
   * 姓名
   */
  name?: string;

  /**
   * 联系人角色
   */
  role?: string;

  /**
   * 联系电话
   */
  phone?: string;

  /**
   * 微信号
   */
  wxId?: string | number;

  /**
   * 邮箱
   */
  email?: string;

  /**
   * 在职状态
   */
  status?: string;

  /**
   * 性别
   */
  gender?: string;

  /**
   * 职务
   */
  position?: string;

  /**
   * 是否主要联系人
   */
  isPrimary?: string;

  /**
   * 行为取向
   */
  behavioral?: string;

  /**
   * 性格特征
   */
  personality?: string;

  /**
   * 对我方立场
   */
  positionOnMe?: string;

  /**
   * 交往程度
   */
  communicate?: string;

  /**
   * 个人爱好
   */
  hobby?: string;

  /**
   * 文件路径
   */
  file?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface ContactQuery extends PageQuery {
  /**
   * 客户名称
   */
  customerId?: string | number;

  /**
   * 姓名
   */
  name?: string;

  /**
   * 联系人角色
   */
  role?: string;

  /**
   * 联系电话
   */
  phone?: string;

  /**
   * 微信号
   */
  wxId?: string | number;

  /**
   * 邮箱
   */
  email?: string;

  /**
   * 在职状态
   */
  status?: string;

  /**
   * 是否主要联系人
   */
  isPrimary?: string;

  /**
   * 行为取向
   */
  behavioral?: string;

  /**
   * 性格特征
   */
  personality?: string;

  /**
   * 对我方立场
   */
  positionOnMe?: string;

  /**
   * 交往程度
   */
  communicate?: string;

  /**
   * 个人爱好
   */
  hobby?: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
