import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { OrgVO, OrgQuery, OrgForm } from './types';

/**
 * 获取组织列表
 * @param query
 * @returns
 */
export function listOrg(query?: OrgQuery): AxiosPromise<OrgVO[]> {
  return request({
    url: '/org/org/list',
    method: 'get',
    params: query
  });
}

/**
 * 根据Id查询Org的信息
 * @param id
 * @returns
 */
export const getOrgById = (id: number | string): AxiosPromise<OrgVO> => {
  return request({
    url: '/org/org/' + id,
    method: 'get'
  });
};

/**
 * 添加组织
 * @param data
 * @returns
 */
export const addOrg = (data: OrgForm) => {
  return request({
    url: '/org/org',
    method: 'post',
    data: data
  });
};

/**
 * 修改组织
 * @param data
 * @returns
 */
export const updateOrg = (data: OrgForm) => {
  return request({
    url: '/org/org',
    method: 'put',
    data: data
  });
};

/**
 * 删除组织
 * @param id
 * @returns
 */
export const deleteOrg = (id: number | string) => {
  return request({
    url: '/org/org/' + id,
    method: 'delete'
  });
};

/**
 * 获取已存在组织的数量
 * @returns
 */
export const existOrgCount = (): Promise<number> => {
  return request({
    url: '/org/org/existOrgCount',
    method: 'get'
  });
};
