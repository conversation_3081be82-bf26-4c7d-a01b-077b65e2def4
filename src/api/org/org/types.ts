export interface OrgVO {
  id: string | number;
  orgName: string;
  orgRoleKey: string;
  orgAdmin: string | number;
  orderNum: number;
  status: string;
  portalStatus: string;
  orgLogo: string;
  orgLogoUrl: string;
  remark: string;
  createTime: string;
  updateTime: string;
}

export interface OrgForm extends BaseEntity {
  id?: string | number;
  orgName?: string;
  orgRoleKey?: string;
  orgAdmin?: string | number;
  orderNum?: number;
  status?: string;
  portalStatus?: string;
  orgLogo?: string;
  remark?: string;
}

export interface OrgQuery extends PageQuery {
  orgName?: string;
  orgRoleKey?: string;
  status?: string;
  portalStatus?: string;
  updateTime?: string;

  /** 日期范围参数 */
  params?: any;
}
