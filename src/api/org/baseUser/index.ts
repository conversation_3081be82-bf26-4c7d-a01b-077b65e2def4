import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { BaseUserForm, BaseUserVO, BaseUserQuery, ResetPwdForm } from './types';

/**
 * 查询基础用户列表
 * @param query
 * @returns
 */
export const listBaseUser = (query?: BaseUserQuery): AxiosPromise<BaseUserVO[]> => {
  return request({
    url: '/org/baseUser/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询基础用户详细
 * @param id
 * @returns
 */
export const getBaseUser = (id: number | string): AxiosPromise<BaseUserVO> => {
  return request({
    url: '/org/baseUser/' + id,
    method: 'get'
  });
};

/**
 * 修改基础用户，主要是黑名单管理
 * @param data
 * @returns
 */
export const updateBaseUser = (data: BaseUserForm): AxiosPromise<BaseUserVO> => {
  return request({
    url: '/org/baseUser',
    method: 'put',
    data: data
  });
};

/**
 * 重置密码
 * @param data
 * @returns
 */
export const resetPwd = (data: ResetPwdForm) => {
  return request({
    url: '/org/baseUser/resetPwd',
    method: 'put',
    data: data
  });
};

export const getBaseUserOrAdd = (data: BaseUserForm): AxiosPromise<BaseUserVO> => {
  return request({
    url: '/org/baseUser/queryOrAdd',
    method: 'post',
    data: data
  });
};
