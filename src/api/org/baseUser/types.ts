export interface BaseUserVO {
  id: string | number;
  userName: string;
  loginPhone: string;
  avatarUrl: string;
  avatarUrlUrl: string;
  gender: string;
  isBlacklist: string;
  remark: string;
  updateTime: string;
}

export interface BaseUserForm extends BaseEntity {
  id?: string | number;
  userName?: string;
  loginPhone?: string;
  password?: string;
  avatarUrl?: string;
  gender?: string;
  isBlacklist?: string;
  remark?: string;
}

export interface BaseUserQuery extends PageQuery {
  userName?: string;
  loginPhone?: string;
  gender?: string;
  isBlacklist?: string;
  updateTime?: string;
  params?: any;
}

export interface ResetPwdForm extends BaseEntity {
  id?: string | number;
  loginPhone?: string;
  userName?: string;
  password: string;
}
