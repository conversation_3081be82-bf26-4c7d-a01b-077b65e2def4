export interface StateMachineVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 字典类型
   */
  dictType: string;

  /**
   * 事件名称
   */
  eventName: string;

  /**
   * 事件编码
   */
  eventCode: string;
  /**
   * 当前状态
   */
  currentStatus: string;

  /**
   * 下一状态
   */
  nextStatus: string;

  /**
   * 排序
   */
  sort: number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新人
   */
  updateBy: number;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface StateMachineForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 字典类型
   */
  dictType?: string;

  /**
   * 事件名称
   */
  eventName?: string;

  /**
   * 事件编码
   */
  eventCode?: string;

  /**
   * 当前状态
   */
  currentStatus?: string;

  /**
   * 下一状态
   */
  nextStatus?: string;

  /**
   * 排序
   */
  sort?: number;

  /**
   * 备注
   */
  remark?: string;
}

export interface StateMachineQuery extends PageQuery {
  /**
   * 字典类型
   */
  dictType?: string;

  /**
   * 当前状态
   */
  currentStatus?: string;

  /**
   * 事件名称
   */
  eventName?: string;

  /**
   * 事件编码
   */
  eventCode?: string;

  /**
   * 下一状态
   */
  nextStatus?: string;

  /**
   * 排序
   */
  sort?: number;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
