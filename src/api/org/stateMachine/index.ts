import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { StateMachineVO, StateMachineForm, StateMachineQuery } from '@/api/org/stateMachine/types';

/**
 * 查询状态机列表
 * @param query
 * @returns {*}
 */

export const listStateMachine = (query?: StateMachineQuery): AxiosPromise<StateMachineVO[]> => {
  return request({
    url: '/org/stateMachine/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询状态机详细
 * @param id
 */
export const getStateMachine = (id: string | number): AxiosPromise<StateMachineVO> => {
  return request({
    url: '/org/stateMachine/' + id,
    method: 'get'
  });
};

/**
 * 新增状态机
 * @param data
 */
export const addStateMachine = (data: StateMachineForm) => {
  return request({
    url: '/org/stateMachine',
    method: 'post',
    data: data
  });
};

/**
 * 修改状态机
 * @param data
 */
export const updateStateMachine = (data: StateMachineForm) => {
  return request({
    url: '/org/stateMachine',
    method: 'put',
    data: data
  });
};

/**
 * 删除状态机
 * @param id
 */
export const delStateMachine = (id: string | number | Array<string | number>) => {
  return request({
    url: '/org/stateMachine/' + id,
    method: 'delete'
  });
};

/**
 * 获取当前状态已存在的下一状态数量
 */
export const getStateMachineCount = (data: StateMachineQuery): Promise<number> => {
  return request({
    url: '/org/stateMachine/existNextStateCount',
    method: 'get',
    params: data
  });
};
