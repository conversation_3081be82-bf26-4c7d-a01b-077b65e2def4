import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { HelpDocVO, HelpDocForm, HelpDocQuery } from '@/api/org/helpDoc/types';

/**
 * 查询帮助文档列表
 * @param query
 * @returns {*}
 */

export const listHelpDoc = (query?: HelpDocQuery): AxiosPromise<HelpDocVO[]> => {
  return request({
    url: '/org/helpDoc/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询帮助文档详细
 * @param id
 */
export const getHelpDoc = (id: string | number): AxiosPromise<HelpDocVO> => {
  return request({
    url: '/org/helpDoc/' + id,
    method: 'get'
  });
};

/**
 * 新增帮助文档
 * @param data
 */
export const addHelpDoc = (data: HelpDocForm) => {
  return request({
    url: '/org/helpDoc',
    method: 'post',
    data: data
  });
};

/**
 * 修改帮助文档
 * @param data
 */
export const updateHelpDoc = (data: HelpDocForm) => {
  return request({
    url: '/org/helpDoc',
    method: 'put',
    data: data
  });
};

/**
 * 删除帮助文档
 * @param id
 */
export const delHelpDoc = (id: string | number | Array<string | number>) => {
  return request({
    url: '/org/helpDoc/' + id,
    method: 'delete'
  });
};

/**
 * 获取已存在帮助文档的数量
 * @returns
 */
export const existHelpDocCount = (): Promise<number> => {
  return request({
    url: '/org/helpDoc/existHelpDocCount',
    method: 'get'
  });
};
