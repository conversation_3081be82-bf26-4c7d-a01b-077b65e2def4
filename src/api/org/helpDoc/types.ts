export interface HelpDocVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 标题
   */
  title: string;

  /**
   * 链接
   */
  url: string;

  /**
   * 模块名称
   */
  moduleKey: string;

  /**
   * 排序
   */
  orderNum: number;

  /**
   * 状态
   */
  status: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface HelpDocForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 标题
   */
  title?: string;

  /**
   * 链接
   */
  url?: string;

  /**
   * 模块名称
   */
  moduleKey?: string;

  /**
   * 排序
   */
  orderNum?: number;

  /**
   * 状态
   */
  status?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface HelpDocQuery extends PageQuery {
  /**
   * 标题
   */
  title?: string;

  /**
   * 模块名称
   */
  moduleKey?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
