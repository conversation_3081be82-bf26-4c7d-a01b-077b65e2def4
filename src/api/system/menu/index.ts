import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MenuQuery, MenuVO, MenuForm, MenuTreeOption, RoleMenuTree, SameLevelMenuCountForm } from './types';

// 查询菜单列表
export const listMenu = (query?: MenuQuery): AxiosPromise<MenuVO[]> => {
  return request({
    url: '/system/menu/list',
    method: 'get',
    params: query
  });
};

// 查询菜单详细
export const getMenu = (menuId: string | number): AxiosPromise<MenuVO> => {
  return request({
    url: '/system/menu/' + menuId,
    method: 'get'
  });
};

// 查询菜单下拉树结构
export const treeselect = (query?: SameLevelMenuCountForm): AxiosPromise<MenuTreeOption[]> => {
  return request({
    url: '/system/menu/treeselect',
    method: 'get',
    params: query
  });
};

// 根据角色ID查询菜单下拉树结构
export const roleMenuTreeselect = (roleId: string | number, clientId: string | number): AxiosPromise<RoleMenuTree> => {
  return request({
    url: '/system/menu/roleMenuTreeselect/' + roleId + '/' + clientId,
    method: 'get'
  });
};

// 根据角色ID查询菜单下拉树结构
export const tenantPackageMenuTreeselect = (packageId: string | number): AxiosPromise<RoleMenuTree> => {
  return request({
    url: '/system/menu/tenantPackageMenuTreeselect/' + packageId,
    method: 'get'
  });
};

// 新增菜单
export const addMenu = (data: MenuForm) => {
  return request({
    url: '/system/menu',
    method: 'post',
    data: data
  });
};

// 修改菜单
export const updateMenu = (data: MenuForm) => {
  return request({
    url: '/system/menu',
    method: 'put',
    data: data
  });
};

// 删除菜单
export const delMenu = (menuId: string | number) => {
  return request({
    url: '/system/menu/' + menuId,
    method: 'delete'
  });
};

// 获取同级菜单的数量
export const getSameLevelMenuCount = (data: SameLevelMenuCountForm) => {
  return request({
    url: '/system/menu/SameLevelMenuCount',
    method: 'get',
    params: data
  });
};
