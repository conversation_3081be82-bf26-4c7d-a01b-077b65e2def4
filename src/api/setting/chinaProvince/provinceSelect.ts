// 省份选择组件的支撑，可以传入省份编码，则只查询该省份的城市
// 这里只适合小数据量的查询

import { ref } from 'vue';
import { listChinaProvince } from '@/api/setting/chinaProvince';
import type { ChinaProvinceVO } from '@/api/setting/chinaProvince/types';

export const useProvinceSelect = () => {
  const provinceOptions = ref<ChinaProvinceVO[]>([]);
  const provinceLoading = ref(false);

  const loadProvinceList = async () => {
    try {
      provinceLoading.value = true;
      const res = await listChinaProvince({ pageNum: 1, pageSize: 40 });
      provinceOptions.value = res.rows;
    } finally {
      provinceLoading.value = false;
    }
  };

  return {
    provinceOptions,
    provinceLoading,
    loadProvinceList
  };
};
