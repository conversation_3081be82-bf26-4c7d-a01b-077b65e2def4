import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ChinaProvinceVO, ChinaProvinceForm, ChinaProvinceQuery } from '@/api/setting/chinaProvince/types';

/**
 * 查询省份列表
 * @param query
 * @returns {*}
 */

export const listChinaProvince = (query?: ChinaProvinceQuery): AxiosPromise<ChinaProvinceVO[]> => {
  return request({
    url: '/setting/chinaProvince/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询省份详细
 * @param id
 */
export const getChinaProvince = (id: string | number): AxiosPromise<ChinaProvinceVO> => {
  return request({
    url: '/setting/chinaProvince/' + id,
    method: 'get'
  });
};

/**
 * 新增省份
 * @param data
 */
export const addChinaProvince = (data: ChinaProvinceForm) => {
  return request({
    url: '/setting/chinaProvince',
    method: 'post',
    data: data
  });
};

/**
 * 修改省份
 * @param data
 */
export const updateChinaProvince = (data: ChinaProvinceForm) => {
  return request({
    url: '/setting/chinaProvince',
    method: 'put',
    data: data
  });
};

/**
 * 删除省份
 * @param id
 */
export const delChinaProvince = (id: string | number | Array<string | number>) => {
  return request({
    url: '/setting/chinaProvince/' + id,
    method: 'delete'
  });
};
