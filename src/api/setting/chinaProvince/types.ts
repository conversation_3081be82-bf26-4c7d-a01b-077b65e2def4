export interface ChinaProvinceVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 省份编码
   */
  code: string;

  /**
   * 省份名称
   */
  name: string;

  /**
   * 备注
   */
  remark: string;

}

export interface ChinaProvinceForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 省份编码
   */
  code?: string;

  /**
   * 省份名称
   */
  name?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface ChinaProvinceQuery extends PageQuery {

  /**
   * 省份编码
   */
  code?: string;

  /**
   * 省份名称
   */
  name?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



