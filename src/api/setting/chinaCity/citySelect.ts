// 城市选择组件的支撑，可以传入省份编码，则只查询该省份的城市
// 这里只适合小数据量的查询

import { ref } from 'vue';
import { listChinaCity } from '@/api/setting/chinaCity';
import type { ChinaCityVO } from '@/api/setting/chinaCity/types';

export const useCitySelect = (provinceCode?: string) => {
  const cityOptions = ref<ChinaCityVO[]>([]);
  const cityLoading = ref(false);

  const loadCityList = async (provinceCode?: string) => {
    try {
      cityLoading.value = true;
      const res = await listChinaCity({ pageNum: 1, pageSize: 40, provinceCode: provinceCode });
      cityOptions.value = res.rows;
    } finally {
      cityLoading.value = false;
    }
  };

  return {
    cityOptions,
    cityLoading,
    loadCityList
  };
};
