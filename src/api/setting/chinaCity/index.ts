import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ChinaCityVO, ChinaCityForm, ChinaCityQuery } from '@/api/setting/chinaCity/types';

/**
 * 查询城市列表
 * @param query
 * @returns {*}
 */

export const listChinaCity = (query?: ChinaCityQuery): AxiosPromise<ChinaCityVO[]> => {
  return request({
    url: '/setting/chinaCity/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询城市详细
 * @param id
 */
export const getChinaCity = (id: string | number): AxiosPromise<ChinaCityVO> => {
  return request({
    url: '/setting/chinaCity/' + id,
    method: 'get'
  });
};

/**
 * 新增城市
 * @param data
 */
export const addChinaCity = (data: ChinaCityForm) => {
  return request({
    url: '/setting/chinaCity',
    method: 'post',
    data: data
  });
};

/**
 * 修改城市
 * @param data
 */
export const updateChinaCity = (data: ChinaCityForm) => {
  return request({
    url: '/setting/chinaCity',
    method: 'put',
    data: data
  });
};

/**
 * 删除城市
 * @param id
 */
export const delChinaCity = (id: string | number | Array<string | number>) => {
  return request({
    url: '/setting/chinaCity/' + id,
    method: 'delete'
  });
};
