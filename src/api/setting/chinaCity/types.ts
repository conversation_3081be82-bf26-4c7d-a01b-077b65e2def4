export interface ChinaCityVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 城市编码
   */
  code: string;

  /**
   * 城市名称
   */
  name: string;

  /**
   * 省份编码
   */
  provinceCode: string;

  /**
   * 省份名称
   */
  provinceName: string;

  /**
   * 备注
   */
  remark: string;
}

export interface ChinaCityForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 城市编码
   */
  code?: string;

  /**
   * 城市名称
   */
  name?: string;

  /**
   * 省份编码
   */
  provinceCode?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface ChinaCityQuery extends PageQuery {
  /**
   * 城市编码
   */
  code?: string;

  /**
   * 城市名称
   */
  name?: string;

  /**
   * 省份编码
   */
  provinceCode?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
