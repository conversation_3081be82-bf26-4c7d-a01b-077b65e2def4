import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ChinaAreaVO, ChinaAreaForm, ChinaAreaQuery } from '@/api/setting/chinaArea/types';

/**
 * 查询区县列表
 * @param query
 * @returns {*}
 */

export const listChinaArea = (query?: ChinaAreaQuery): AxiosPromise<ChinaAreaVO[]> => {
  return request({
    url: '/setting/chinaArea/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询区县详细
 * @param id
 */
export const getChinaArea = (id: string | number): AxiosPromise<ChinaAreaVO> => {
  return request({
    url: '/setting/chinaArea/' + id,
    method: 'get'
  });
};

/**
 * 新增区县
 * @param data
 */
export const addChinaArea = (data: ChinaAreaForm) => {
  return request({
    url: '/setting/chinaArea',
    method: 'post',
    data: data
  });
};

/**
 * 修改区县
 * @param data
 */
export const updateChinaArea = (data: ChinaAreaForm) => {
  return request({
    url: '/setting/chinaArea',
    method: 'put',
    data: data
  });
};

/**
 * 删除区县
 * @param id
 */
export const delChinaArea = (id: string | number | Array<string | number>) => {
  return request({
    url: '/setting/chinaArea/' + id,
    method: 'delete'
  });
};
