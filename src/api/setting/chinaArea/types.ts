export interface ChinaAreaVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 区县编码
   */
  code: string;

  /**
   * 区县名称
   */
  name: string;

  /**
   * 城市编码
   */
  cityCode: string;

  /**
   * 城市名称
   */
  cityName: string;

  /**
   * 省份编码
   */
  provinceCode: string;

  /**
   * 省份名称
   */
  provinceName: string;

  /**
   * 备注
   */
  remark: string;
}

export interface ChinaAreaForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 城市编码
   */
  code?: string;

  /**
   * 城市名称
   */
  name?: string;

  /**
   * 城市编码
   */
  cityCode?: string;

  /**
   * 省份编码
   */
  provinceCode?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface ChinaAreaQuery extends PageQuery {
  /**
   * 城市编码
   */
  code?: string;

  /**
   * 城市名称
   */
  name?: string;

  /**
   * 城市编码
   */
  cityCode?: string;

  /**
   * 省份编码
   */
  provinceCode?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
