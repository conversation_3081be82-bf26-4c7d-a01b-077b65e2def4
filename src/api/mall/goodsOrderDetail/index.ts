import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GoodsOrderDetailVO, GoodsOrderDetailForm, GoodsOrderDetailQuery } from '@/api/mall/goodsOrderDetail/types';

/**
 * 查询商品订单明细列表
 * @param query
 * @returns {*}
 */

export const listGoodsOrderDetail = (query?: GoodsOrderDetailQuery): AxiosPromise<GoodsOrderDetailVO[]> => {
  return request({
    url: '/mall/goodsOrderDetail/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询商品订单明细详细
 * @param id
 */
export const getGoodsOrderDetail = (id: string | number): AxiosPromise<GoodsOrderDetailVO> => {
  return request({
    url: '/mall/goodsOrderDetail/' + id,
    method: 'get'
  });
};

/**
 * 新增商品订单明细
 * @param data
 */
export const addGoodsOrderDetail = (data: GoodsOrderDetailForm) => {
  return request({
    url: '/mall/goodsOrderDetail',
    method: 'post',
    data: data
  });
};

/**
 * 修改商品订单明细
 * @param data
 */
export const updateGoodsOrderDetail = (data: GoodsOrderDetailForm) => {
  return request({
    url: '/mall/goodsOrderDetail',
    method: 'put',
    data: data
  });
};

/**
 * 删除商品订单明细
 * @param id
 */
export const delGoodsOrderDetail = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mall/goodsOrderDetail/' + id,
    method: 'delete'
  });
};
