import { GoodsVO } from '../goods/types';
export interface GoodsOrderDetailVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 商品订单id
   */
  goodsOrderId: string | number;

  /**
   * 商品id
   */
  goodsId: string | number;

  /**
   * 商品名称
   */
  goodsName: string;

  /**
   * 商品图片
   */
  goodsImage: string;

  /**
   * 商品图片Url
   */
  goodsImageUrl: string;

  /**
   * 销售单位
   */
  saleUnit: string;

  /**
   * 销售单位Label
   */
  saleUnitLabel: string;

  /**
   * 需求数量
   */
  qty: number;

  /**
   * 折扣
   */
  discount: number;

  /**
   * 单价，单位为分
   */
  unitPrice: number;

  /**
   * 明细总价，单位为分
   */
  detailTotal: number;

  /**
   * 被替换商品id
   */
  replacedGoodsId: string | number;

  /**
   * 被替换商品名称
   */
  replacedGoodsName: string;

  /**
   * 被替换商品图片
   */
  replacedGoodsImage: string;

  /**
   * 被替换数量
   */
  replacedQty: number;

  /**
   * 被替换原因
   */
  replacedReason: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新人
   */
  updateBy: number;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface GoodsOrderDetailForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 商品订单id
   */
  goodsOrderId?: string | number;

  /**
   * 商品id
   */
  goodsId?: string | number;

  /**
   * 需求数量
   */
  qty?: number;

  /**
   * 折扣
   */
  discount?: number;

  /**
   * 单价，单位为分
   */
  unitPrice?: number;

  /**
   * 明细总价，单位为分
   */
  detailTotal?: number;

  /**
   * 被替换商品id
   */
  replacedGoodsId?: string | number;

  /**
   * 被替换数量
   */
  replacedQty?: number;

  /**
   * 被替换原因
   */
  replacedReason?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface GoodsOrderDetailQuery extends PageQuery {
  /**
   * 商品订单id
   */
  goodsOrderId?: string | number;

  /**
   * 商品id
   */
  goodsId?: string | number;

  /**
   * 被替换商品id
   */
  replacedGoodsId?: string | number;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
