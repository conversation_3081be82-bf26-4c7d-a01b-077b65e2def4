import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { SubImageVO, SubImageForm, SubImageQuery } from '@/api/mall/subImage/types';

/**
 * 查询商品角标列表
 * @param query
 * @returns {*}
 */

export const listSubImage = (query?: SubImageQuery): AxiosPromise<SubImageVO[]> => {
  return request({
    url: '/mall/subImage/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询商品角标详细
 * @param id
 */
export const getSubImage = (id: string | number): AxiosPromise<SubImageVO> => {
  return request({
    url: '/mall/subImage/' + id,
    method: 'get'
  });
};

/**
 * 新增商品角标
 * @param data
 */
export const addSubImage = (data: SubImageForm) => {
  return request({
    url: '/mall/subImage',
    method: 'post',
    data: data
  });
};

/**
 * 修改商品角标
 * @param data
 */
export const updateSubImage = (data: SubImageForm) => {
  return request({
    url: '/mall/subImage',
    method: 'put',
    data: data
  });
};

/**
 * 删除商品角标
 * @param id
 */
export const delSubImage = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mall/subImage/' + id,
    method: 'delete'
  });
};

/**
 * 获取已经存在的商品角标的数量
 */
export const countSubImage = (): AxiosPromise<number> => {
  return request({
    url: '/mall/subImage/existCount',
    method: 'get'
  });
};
