export interface SubImageVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 名称
   */
  name: string;

  /**
   * 状态（0正常 1停用）
   */
  status: string;

  /**
   * 排序
   */
  sort: number;

  /**
   * 角标图片
   */
  image: string;

  /**
   * 角标图片Url
   */
  imageUrl: string;
  /**
   * 备注
   */
  remark: string;

  /**
   * 更新人
   */
  updateBy: number;

  /**
   * 更新人昵称
   */
  updateNickName: string;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface SubImageForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 名称
   */
  name?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

  /**
   * 排序
   */
  sort?: number;

  /**
   * 角标图片
   */
  image?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface SubImageQuery extends PageQuery {
  /**
   * 名称
   */
  name?: string;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
