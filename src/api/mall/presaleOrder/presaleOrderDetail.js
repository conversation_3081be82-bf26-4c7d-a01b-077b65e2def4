import request from '@/utils/request';

// 通过开始和结束流水号查询可用卡数
export function getAvaliable(data) {
  return request({
    url: '/mall/presaleOrderDetail/getAvailable',
    method: 'post',
    data: data
  });
}

// 添加卡
export function addCard(data) {
  return request({
    url: '/mall/presaleOrderDetail/addCard',
    method: 'post',
    data: data
  });
}

// 查询销售订单明细列表
export function listPresaleOrderDetail(query) {
  return request({
    url: '/mall/presaleOrderDetail/list',
    method: 'get',
    params: query
  });
}

// 撤销之前添加的卡
export function cancelAddedCard(data) {
  return request({
    url: '/mall/presaleOrderDetail/cancelAddedCard',
    method: 'post',
    data: data
  });
}

// 查询销售订单明细详细
export function getPresaleOrderDetail(id) {
  return request({
    url: '/mall/presaleOrderDetail/' + id,
    method: 'get'
  });
}

// 新增销售订单明细
export function addPresaleOrderDetail(data) {
  return request({
    url: '/mall/presaleOrderDetail',
    method: 'post',
    data: data
  });
}

// 修改销售订单明细
export function updatePresaleOrderDetail(data) {
  return request({
    url: '/mall/presaleOrderDetail',
    method: 'put',
    data: data
  });
}

// 删除销售订单明细
export function delPresaleOrderDetail(ids) {
  return request({
    url: '/mall/presaleOrderDetail/' + ids,
    method: 'delete'
  });
}

// 更新销售订单明细
export function updatePresaleOrderDetailList(data) {
  return request({
    url: '/mall/presaleOrderDetail',
    method: 'put',
    data: data
  });
}

// 删除销售订单明细
export function delPresaleOrderDetailList(ids) {
  return request({
    url: '/mall/presaleOrderDetail/' + ids,
    method: 'delete'
  });
}

// 新增销售订单明细
export function addPresaleOrderDetailList(data) {
  return request({
    url: '/mall/presaleOrderDetail',
    method: 'post',
    data: data
  });
}
