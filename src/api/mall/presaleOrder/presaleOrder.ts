import request from '@/utils/request';

// 查询销售订单列表
export function listPresaleOrder(query) {
  return request({
    url: '/mall/presaleOrder/list',
    method: 'get',
    params: query
  });
}

// 查询销售订单详细
export function getPresaleOrder(id) {
  return request({
    url: '/mall/presaleOrder/' + id,
    method: 'get'
  });
}

// 新增销售订单
export function addPresaleOrder(data) {
  return request({
    url: '/mall/presaleOrder',
    method: 'post',
    data: data
  });
}

// 修改销售订单
export function updatePresaleOrder(data) {
  return request({
    url: '/mall/presaleOrder',
    method: 'put',
    data: data
  });
}

// 删除销售订单
export function delPresaleOrder(ids) {
  return request({
    url: '/mall/presaleOrder/' + ids,
    method: 'delete'
  });
}

// 提交销售订单
export function submitPresaleOrder(id) {
  return request({
    url: '/mall/presaleOrder/submit/' + id,
    method: 'post'
  });
}

// 启用销售订单
export function enablePresaleOrder(id) {
  return request({
    url: '/mall/presaleOrder/enable/' + id,
    method: 'post'
  });
}

// 撤回销售订单
export function revokePresaleOrder(id) {
  return request({
    url: '/mall/presaleOrder/revoke/' + id,
    method: 'post'
  });
}

// 审核销售订单
export function auditPresaleOrder(data) {
  return request({
    url: '/mall/presaleOrder/audit',
    method: 'post',
    data: data
  });
}

// 确认出卡
export function confirmCard(id) {
  return request({
    url: '/mall/presaleOrder/finishGenerate/' + id,
    method: 'get'
  });
}

// 开卡
export function openCard(data) {
  return request({
    url: '/mall/presaleOrder/active',
    method: 'post',
    data
  });
}

// 获取销售订单明细
export function getPresaleOrderDetail(id) {
  return request({
    url: '/mall/presaleOrderDetail/' + id,
    method: 'get'
  });
}

// 查询销售订单明细列表
export function getPresaleOrderDetailList(params) {
  return request({
    url: '/mall/presaleOrderDetail/list',
    method: 'get',
    params: params
  });
}

// 作废订单
export function voidPresaleOrder(data) {
  return request({
    url: '/mall/presaleOrder/void',
    method: 'post',
    data: data
  });
}
