export interface WxUserVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 微信 OpenID
   */
  openid: string | number;

  /**
   * 小程序appid
   */
  appid: string | number;

  /**
   * 用户昵称
   */
  nickname: string;

  /**
   * 性别(0-未知, 1-男, 2-女)
   */
  gender: number;

  /**
   * 手机号
   */
  phoneNumber: string;

  /**
   * 城市
   */
  city: string;

  /**
   * 省份
   */
  province: string;

  /**
   * 国家
   */
  country: string;

  /**
   * 用户语言
   */
  language: string;

  /**
   * 0-正常；1-禁用
   */
  status: number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 创建时间
   */
  createTime: string;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface WxUserForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 微信 OpenID
   */
  openid?: string | number;

  /**
   * 微信 UnionID
   */
  unionid?: string | number;

  /**
   * 小程序appid
   */
  appid?: string | number;

  /**
   * 用户昵称
   */
  nickname?: string;

  /**
   * 用户头像
   */
  avatarUrl?: string;

  /**
   * 性别(0-未知, 1-男, 2-女)
   */
  gender?: number;

  /**
   * 手机号
   */
  phoneNumber?: string;

  /**
   * 邮箱
   */
  email?: string;

  /**
   * 城市
   */
  city?: string;

  /**
   * 省份
   */
  province?: string;

  /**
   * 国家
   */
  country?: string;

  /**
   * 用户语言
   */
  language?: string;

  /**
   * 0-正常；1-禁用
   */
  status?: number;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 创建者
   */
  createBy?: number;

  /**
   * 创建时间
   */
  createTime?: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;
}

export interface WxUserQuery extends PageQuery {
  /**
   * 微信 OpenID
   */
  openid?: string | number;

  /**
   * 小程序appid
   */
  appid?: string | number;

  /**
   * 用户昵称
   */
  nickname?: string;

  /**
   * 手机号
   */
  phoneNumber?: string;

  /**
   * 国家
   */
  country?: string;

  /**
   * 用户语言
   */
  language?: string;

  /**
   * 0-正常；1-禁用
   */
  status?: number;

  /**
   * 创建时间
   */
  createTime?: string;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
