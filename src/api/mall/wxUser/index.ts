import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { WxUserVO, WxUserForm, WxUserQuery } from '@/api/mall/wxUser/types';

/**
 * 查询用户列表列表
 * @param query
 * @returns {*}
 */

export const listWxUser = (query?: WxUserQuery): AxiosPromise<WxUserVO[]> => {
  return request({
    url: '/mall/user/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询用户列表详细
 * @param id
 */
export const getWxUser = (id: string | number): AxiosPromise<WxUserVO> => {
  return request({
    url: '/mall/user/' + id,
    method: 'get'
  });
};

/**
 * 新增用户列表
 * @param data
 */
export const addWxUser = (data: WxUserForm) => {
  return request({
    url: '/mall/user',
    method: 'post',
    data: data
  });
};

/**
 * 修改用户列表
 * @param data
 */
export const updateWxUser = (data: WxUserForm) => {
  return request({
    url: '/mall/user',
    method: 'put',
    data: data
  });
};

/**
 * 删除用户列表
 * @param id
 */
export const delWxUser = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mall/user/' + id,
    method: 'delete'
  });
};
