import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GeneralDetailVO, GeneralDetailForm, GeneralDetailQuery } from '@/api/mall/generalDetail/types';

/**
 * 查询通用详情列表
 * @param query
 * @returns {*}
 */

export const listGeneralDetail = (query?: GeneralDetailQuery): AxiosPromise<GeneralDetailVO[]> => {
  return request({
    url: '/mall/generalDetail/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询通用详情详细
 * @param id
 */
export const getGeneralDetail = (id: string | number): AxiosPromise<GeneralDetailVO> => {
  return request({
    url: '/mall/generalDetail/' + id,
    method: 'get'
  });
};

/**
 * 新增通用详情
 * @param data
 */
export const addGeneralDetail = (data: GeneralDetailForm) => {
  return request({
    url: '/mall/generalDetail',
    method: 'post',
    data: data
  });
};

/**
 * 修改通用详情
 * @param data
 */
export const updateGeneralDetail = (data: GeneralDetailForm) => {
  return request({
    url: '/mall/generalDetail',
    method: 'put',
    data: data
  });
};

/**
 * 删除通用详情
 * @param id
 */
export const delGeneralDetail = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mall/generalDetail/' + id,
    method: 'delete'
  });
};

/**
 * 获取已经存在的通用详情的数量
 */
export const countGeneralDetail = (): AxiosPromise<number> => {
  return request({
    url: '/mall/generalDetail/existCount',
    method: 'get'
  });
};
