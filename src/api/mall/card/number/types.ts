/**
 * 卡号查询参数
 */
export interface CardNumberQuery {
  /**
   * 分页参数
   */
  pageNum?: number;
  pageSize?: number;

  /**
   * id
   */
  id?: number;

  /**
   * 制卡批次号，EC+日期+3为自增，即13位
   */
  batchNumber?: string;

  /**
   * 开始卡号
   */
  cardNoStart?: string;

  /**
   * 结束卡号
   */
  cardNoEnd?: string;

  /**
   * 卡号流水号，批次号+6位自增，即19位
   */
  cardNo?: string;

  /**
   * 卡编码，21位字符编码
   */
  cardCode?: string;

  /**
   * 卡密
   */
  cardSecret?: string;

  /**
   * 销售状态，关联字典「mall_card_sale_status」
   */
  saleStatus?: string;

  /**
   * 销售订单id
   */
  saleOrderId?: number;

  /**
   * 销售时间，即关联到订单的出卡时间
   */
  saleTime?: string;

  /**
   * 可用状态，关联字典「mall_card_available_status」
   */
  availableStatus?: string;

  /**
   * 开卡时间
   */
  issueCardTime?: string;

  /**
   * 使用状态，关联字典「mall_card_usage_status」
   */
  usageStatus?: string;

  /**
   * 使用状态更新时间
   */
  usageStatusUpdateTime?: string;

  /**
   * 绑定手机号
   */
  bindPhone?: string;

  /**
   * 绑定时间
   */
  bindTime?: string;

  /**
   * 使用时间
   */
  useTime?: string;

  /**
   * 生效时间
   */
  effectiveTime?: string;

  /**
   * 失效时间
   */
  expireTime?: string;

  /**
   * 备注
   */
  remark?: string;
}

/**
 * 卡号视图对象
 */
export interface CardNumberVO {
  /**
   * id
   */
  id: number;

  /**
   * 组织id
   */
  createOrgId: number;

  /**
   * 制卡批次号，EC+日期+3为自增，即13位
   */
  batchNumber: string;

  /**
   * 卡号流水号，批次号+6位自增，即19位
   */
  cardNo: string;

  /**
   * 卡编码，21位字符编码
   */
  cardCode: string;

  /**
   * 批次信息
   */
  batch?: any;

  /**
   * 已兑换次数
   */
  exchangedTimes: number;

  /**
   * 卡密
   */
  cardSecret: string;

  /**
   * 销售状态，关联字典「mall_card_sale_status」
   */
  saleStatus: string;

  /**
   * 销售状态标签
   */
  saleStatusLabel: string;

  /**
   * 销售订单id
   */
  saleOrderId: number;

  /**
   * 销售时间，即关联到订单的出卡时间
   */
  saleTime: string;

  /**
   * 可用状态，关联字典「mall_card_available_status」
   */
  availableStatus: string;

  /**
   * 可用状态标签
   */
  availableStatusLabel: string;

  /**
   * 开卡时间
   */
  issueCardTime: string;

  /**
   * 使用状态，关联字典「mall_card_usage_status」
   */
  usageStatus: string;

  /**
   * 使用状态标签
   */
  usageStatusLabel: string;

  /**
   * 使用状态更新时间
   */
  usageStatusUpdateTime: string;

  /**
   * 绑定手机号
   */
  bindPhone: string;

  /**
   * 绑定时间
   */
  bindTime: string;

  /**
   * 使用时间
   */
  useTime: string;

  /**
   * 生效时间
   */
  effectiveTime: string;

  /**
   * 失效时间
   */
  expireTime: string;

  /**
   * 备注
   */
  remark: string;
}
