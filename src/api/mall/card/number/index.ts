import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CardNumberQuery, CardNumberVO } from './types';

// 查询卡号列表
export function listCardNumber(query?: CardNumberQuery): AxiosPromise<CardNumberVO[]> {
  return request({
    url: '/mall/exchangeCard/list',
    method: 'get',
    params: query
  });
}

// 导出卡号
export function exportCardNumber(query?: CardNumberQuery): AxiosPromise<any> {
  return request({
    url: '/mall/exchangeCard/export',
    method: 'post',
    params: query
  });
}

// 获取卡号详细信息
export function getCardNumber(id: string | number): AxiosPromise<CardNumberVO> {
  return request({
    url: `/mall/exchangeCard/${id}`,
    method: 'get'
  });
}

// 获取卡号详细信息
export function getCardDetail(id: number) {
  return request({
    url: `/mall/exchangeCard/${id}`,
    method: 'get'
  });
}

// 批量挂失卡号
export function reportLossCardNumbers(ids) {
  return request({
    url: '/mall/exchangeCard/reportLoss',
    method: 'put',
    data: ids
  });
}

// 批量恢复卡号
export function recoverCardNumbers(ids) {
  return request({
    url: '/mall/exchangeCard/recover',
    method: 'put',
    data: ids
  });
}

// 批量延期卡号
export function extendCardNumbers(data) {
  return request({
    url: '/mall/exchangeCard/extend',
    method: 'put',
    data
  });
}

// 调整卡号失效日期
export function adjustExpireDateCardNumbers(data) {
  return request({
    url: '/mall/exchangeCard/adjustCardExpireTime',
    method: 'post',
    data
  });
}

// 挂失卡号
export function lossCardNumbers(data) {
  return request({
    url: '/mall/exchangeCard/lossCard',
    method: 'post',
    data
  });
}

// 解除挂失卡号
export function unlockCardNumbers(data) {
  return request({
    url: '/mall/exchangeCard/unLossCard',
    method: 'post',
    data
  });
}

// 作废卡号
export function voidCardNumbers(data) {
  return request({
    url: '/mall/exchangeCard/voidCardByIds',
    method: 'post',
    data
  });
}

// 按批次作废卡号
export function voidCardByBatch(data) {
  return request({
    url: '/mall/exchangeCard/voidCard',
    method: 'post',
    data
  });
}
