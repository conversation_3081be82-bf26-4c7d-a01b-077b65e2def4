export interface GeneralCardVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 名称
   */
  name: string;

  /**
   * 状态
   */
  status: string;

  /**
   * 排序
   */
  sort: number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新人
   */
  updateBy: number;

  /**
   * 更新人昵称
   */
  updateNickname: string;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface GeneralCardQuery extends PageQuery {
  /**
   * 名称
   */
  name?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
