import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ExchangeBatchVO, ExchangeBatchForm, ExchangeBatchQuery, StatusChangeForm } from '@/api/mall/exchangeBatch/types';

/**
 * 查询卡批次列表
 * @param query
 * @returns {*}
 */
export const listExchangeBatch = (query?: ExchangeBatchQuery): AxiosPromise<ExchangeBatchVO[]> => {
  return request({
    url: '/mall/exchangeBatch/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询卡批次详细
 * @param id
 */
export const getExchangeBatch = (id: string | number): AxiosPromise<ExchangeBatchVO> => {
  return request({
    url: '/mall/exchangeBatch/' + id,
    method: 'get'
  });
};

/**
 * 新增卡批次
 * @param data
 */
export const addExchangeBatch = (data: ExchangeBatchForm) => {
  return request({
    url: '/mall/exchangeBatch',
    method: 'post',
    data: data
  });
};

/**
 * 修改卡批次
 * @param data
 */
export const updateExchangeBatch = (data: ExchangeBatchForm) => {
  return request({
    url: '/mall/exchangeBatch',
    method: 'put',
    data: data
  });
};

/**
 * 删除卡批次
 * @param id
 */
export const delExchangeBatch = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mall/exchangeBatch/' + id,
    method: 'delete'
  });
};

/**
 * 更改卡批次状态
 * @param data
 */
export const changeStatus = (data: StatusChangeForm) => {
  return request({
    url: '/mall/exchangeBatch/changeStatus',
    method: 'put',
    data: data
  });
};

/**
 * 提交卡批次
 * @param id 批次ID
 */
export const submitExchangeBatch = (id: string | number) => {
  return request({
    url: `/mall/exchangeBatch/submit/${id}`,
    method: 'get'
  });
};

// 修改卡批次
export const updateExchangeCardInstance = (data: ExchangeBatchForm) => {
  return request({
    url: '/mall/exchangeCardInstance',
    method: 'put',
    data: data
  });
};
