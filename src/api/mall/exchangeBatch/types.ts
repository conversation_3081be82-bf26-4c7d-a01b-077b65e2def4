export interface ExchangeBatchVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 组织id
   */
  createOrgId: string | number;

  /**
   * 制卡批次号
   */
  batchNumber: string;

  /**
   * 制卡数量
   */
  qty: number;

  /**
   * 销售范围
   */
  saleScope: string;

  /**
   * 仓库id
   */
  warehouseId: number;

  /**
   * 批次名称
   */
  name: string;

  /**
   * 封面图片
   */
  coverImage: string;

  /**
   * 封面图片URL
   */
  coverImageUrl: string;

  /**
   * 批次状态
   */
  status: string;

  /**
   * 卡类型
   */
  cardType: string;

  /**
   * 卡面形态
   */
  cardForm: string;

  /**
   * 兑换次数
   */
  times: number;

  /**
   * 批次描述
   */
  desc: string;

  /**
   * 使用说明
   */
  instruction: string;

  /**
   * 面值，单位为分
   */
  value: number;

  /**
   * 发货类型
   */
  deliveryType: string;

  /**
   * 发货周期
   */
  shippingCycle: string;

  /**
   * 周期第几天发货
   */
  cycleDay: number;

  /**
   * 激活方式
   */
  actType: string;

  /**
   * 有效期类型
   */
  effType: string;

  /**
   * 生效日期
   */
  effDate: string;

  /**
   * 失效日期
   */
  expireDate: string;

  /**
   * 失效前n天内禁止绑定
   */
  prohibitBindDays: number;

  /**
   * 激活后n天失效
   */
  daysAfterActivation: number;

  /**
   * 有效天数
   */
  effDays: number;

  /**
   * 兑换开始日期
   */
  startDate: string;

  /**
   * 兑换结束日期
   */
  endDate: string;

  /**
   * 排序
   */
  sort: number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新人昵称
   */
  updateNickName?: string;

  /**
   * 更新时间
   */
  updateTime?: string;
}

export interface ExchangeBatchForm {
  /**
   * id
   */
  id?: string | number;

  /**
   * 组织id
   */
  createOrgId?: string | number;

  /**
   * 制卡批次号
   */
  batchNumber?: string;

  /**
   * 制卡数量
   */
  qty?: number;

  /**
   * 销售范围
   */
  saleScope?: string;

  /**
   * 仓库id
   */
  warehouseId?: number;

  /**
   * 批次名称
   */
  name?: string;

  /**
   * 封面图片
   */
  coverImage?: string;

  /**
   * 批次状态
   */
  status?: string;

  /**
   * 卡类型
   */
  cardType?: string;

  /**
   * 卡面形态
   */
  cardForm?: string;

  /**
   * 兑换次数
   */
  times?: number;

  /**
   * 批次描述
   */
  desc?: string;

  /**
   * 使用说明
   */
  instruction?: string;

  /**
   * 面值，单位为分
   */
  value?: number;

  /**
   * 发货类型
   */
  deliveryType?: string;

  /**
   * 发货周期
   */
  shippingCycle?: string;

  /**
   * 周期第几天发货
   */
  cycleDay?: number;

  /**
   * 激活方式
   */
  actType?: string;

  /**
   * 有效期类型
   */
  effType?: string;

  /**
   * 生效日期
   */
  effDate?: string;

  /**
   * 失效日期
   */
  expireDate?: string;

  /**
   * 失效前n天内禁止绑定
   */
  prohibitBindDays?: number;

  /**
   * 激活后n天失效
   */
  daysAfterActivation?: number;

  /**
   * 有效天数
   */
  effDays?: number;

  /**
   * 兑换开始日期
   */
  startDate?: string;

  /**
   * 兑换结束日期
   */
  endDate?: string;

  /**
   * 排序
   */
  sort?: number;

  /**
   * 备注
   */
  remark?: string;
}

export interface ExchangeBatchQuery {
  /**
   * 当前页码
   */
  pageNum?: number;

  /**
   * 每页条数
   */
  pageSize?: number;

  /**
   * 批次名称
   */
  name?: string;

  /**
   * 批次号
   */
  batchNumber?: string;

  /**
   * 销售范围
   */
  saleScope?: string;

  /**
   * 仓库id
   */
  warehouseId?: number;

  /**
   * 批次状态
   */
  status?: string;

  /**
   * 卡类型
   */
  cardType?: string;

  /**
   * 卡面形态
   */
  cardForm?: string;

  /**
   * 激活方式
   */
  actType?: string;

  /**
   * 有效期类型
   */
  effType?: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

export interface StatusChangeForm {
  /**
   * 批次ID
   */
  id: string | number;

  /**
   * 状态
   */
  status: string;
}
