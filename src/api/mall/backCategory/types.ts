export interface BackCategoryVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 父级编号
   */
  parentId: string | number;

  /**
   * 祖级列表
   */
  ancestors: string | null;

  /**
   * 后台分类名称
   */
  name: string;

  /**
   * 分类图片
   */
  image: string;

  /**
   * 分类图片Url
   */
  imageUrl: string;
  /**
   * 状态
   */
  status: string;

  /**
   * 排序
   */
  sort: number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新人
   */
  updateBy: number;

  /**
   * 更新人昵称
   */
  updateNickName: string;

  /**
   * 更新时间
   */
  updateTime: string;

  /**
   * 子对象
   */
  children: BackCategoryVO[];
}

export interface BackCategoryForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 父级编号
   */
  parentId?: string | number;

  /**
   * 后台分类名称
   */
  name?: string;

  /**
   * 分类图片
   */
  image?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 排序
   */
  sort?: number;

  /**
   * 备注
   */
  remark?: string;
}

export interface BackCategoryQuery {
  /**
   * 父级编号
   */
  parentId?: string | number;

  /**
   * 后台分类名称
   */
  name?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
