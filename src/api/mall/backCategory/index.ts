import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { BackCategoryVO, BackCategoryForm, BackCategoryQuery } from '@/api/mall/backCategory/types';

/**
 * 查询后台分类列表
 * @param query
 * @returns {*}
 */

export const listBackCategory = (query?: BackCategoryQuery): AxiosPromise<BackCategoryVO[]> => {
  return request({
    url: '/mall/backCategory/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询后台分类详细
 * @param id
 */
export const getBackCategory = (id: string | number): AxiosPromise<BackCategoryVO> => {
  return request({
    url: '/mall/backCategory/' + id,
    method: 'get'
  });
};

/**
 * 新增后台分类
 * @param data
 */
export const addBackCategory = (data: BackCategoryForm) => {
  return request({
    url: '/mall/backCategory',
    method: 'post',
    data: data
  });
};

/**
 * 修改后台分类
 * @param data
 */
export const updateBackCategory = (data: BackCategoryForm) => {
  return request({
    url: '/mall/backCategory',
    method: 'put',
    data: data
  });
};

/**
 * 删除后台分类
 * @param id
 */
export const delBackCategory = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mall/backCategory/' + id,
    method: 'delete'
  });
};

/**
 * 获取前台分类的子类数量
 * @param parentId
 */
export const countBackCategoryByParentId = (parentId: string | number): AxiosPromise<number> => {
  return request({
    url: '/mall/backCategory/ChildrenCount/' + parentId,
    method: 'get'
  });
};
