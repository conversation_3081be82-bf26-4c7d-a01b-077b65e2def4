import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GoodsVO, GoodsForm, GoodsQuery } from '@/api/mall/goods/types';

/**
 * 查询商品列表
 * @param query
 * @returns {*}
 */

export const listGoods = (query?: GoodsQuery): AxiosPromise<GoodsVO[]> => {
  return request({
    url: '/mall/goods/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询商品详细
 * @param id
 */
export const getGoods = (id: string | number): AxiosPromise<GoodsVO> => {
  return request({
    url: '/mall/goods/' + id,
    method: 'get'
  });
};

/**
 * 新增商品
 * @param data
 */
export const addGoods = (data: GoodsForm): AxiosPromise<GoodsVO> => {
  return request({
    url: '/mall/goods',
    method: 'post',
    data: data
  });
};

/**
 * 修改商品
 * @param data
 */
export const updateGoods = (data: GoodsForm) => {
  return request({
    url: '/mall/goods',
    method: 'put',
    data: data
  });
};

/**
 * 删除商品
 * @param id
 */
export const delGoods = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mall/goods/' + id,
    method: 'delete'
  });
};

/**
 * 校验名称的唯一性
 */
export const checkGoodsNameUnique = (data: GoodsForm): AxiosPromise<boolean> => {
  return request({
    url: '/mall/goods/checkGoodsNameUnique',
    method: 'get',
    params: data
  });
};

/**
 * 校验编码的唯一性
 */
export const checkGoodsCodeUnique = (data: GoodsForm): AxiosPromise<boolean> => {
  return request({
    url: '/mall/goods/checkGoodsCodeUnique',
    method: 'get',
    params: data
  });
};
