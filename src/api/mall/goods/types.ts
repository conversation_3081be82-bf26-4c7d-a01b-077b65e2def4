export interface GoodsVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 商品名称
   */
  name: string;

  /**
   * 商品编码
   */
  code: string;

  /**
   * 是否erp商品
   */
  isErpGoods: string;

  /**
   * erp商品编码
   */
  erpCode: string;

  /**
   * 69码
   */
  barcode: string;

  /**
   * 采购单位
   */
  purchaseUnit: string;

  /**
   * 是否组合商品
   */
  isCombinedGoods: string;

  /**
   * 组合商品明细数量
   */
  combinedGoodsDetailCount: number;
  /**
   * 商品类型
   */
  goodsType: string;

  /**
   * 商品等级
   */
  goodsGrade: string;

  /**
   * 是否显示等级
   */
  isShowGrade: string;

  /**
   * 销售单位
   */
  saleUnit: string;

  /**
   * 销售单位Label
   */
  saleUnitLabel: string;

  /**
   * 商品重量(kg)
   */
  unitWeight: number;

  /**
   * 包装类型
   */
  packageType: string;

  /**
   * 品牌
   */
  brandKey: string;

  /**
   * 是否显示等级
   */
  isShow: string;

  /**
   * 销售状态
   */
  saleStatus: string;

  /**
   * 排序
   */
  sort: number;

  /**
   * 商品标签
   */
  tags: string;

  /**
   * 商品图片
   */
  images: string;

  /**
   * 商品图片Url
   */
  imagesUrl: string;

  /**
   * 商品角标
   */
  subImageId: string | number;

  /**
   * 通用商品详情
   */
  generalDetailId: string | number;

  /**
   * 前端分类
   */
  frontCategoryIds: string | number;

  /**
   * 后台分类
   */
  backCategoryId: string | number;

  /**
   * 库存
   */
  stockQty: number;

  /**
   * 零售价格
   */
  currentSalePrice: number;

  /**
   * 市场价格
   */
  currentMarketPrice: number;

  /**
   * 成本价
   */
  currentCostPrice: number;

  /**
   * 分享描述
   */
  shareDesc: string;

  /**
   * 更新人
   */
  updateBy: number;

  /**
   * 更新人昵称
   */
  updateNickname: string;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface GoodsForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 商品名称
   */
  name?: string;

  /**
   * 商品编码
   */
  code?: string;

  /**
   * 卖点
   */
  sellPoint?: string;

  /**
   * 是否erp商品
   */
  isErpGoods?: string;

  /**
   * erp商品编码
   */
  erpCode?: string;

  /**
   * 69码
   */
  barcode?: string;

  /**
   * 采购单位
   */
  purchaseUnit?: string;

  /**
   * 是否组合商品
   */
  isCombinedGoods?: string;

  /**
   * 商品类型
   */
  goodsType?: string;

  /**
   * 商品类型Label
   */
  goodsTypeLabel?: string;

  /**
   * 商品等级
   */
  goodsGrade?: string;

  /**
   * 商品等级Label
   */
  goodsGradeLabel?: string;
  /**
   * 是否显示等级
   */
  isShowGrade?: string;

  /**
   * 销售单位
   */
  saleUnit?: string;

  /**
   * 销售单位Label
   */
  saleUnitLabel?: string;

  /**
   * 商品重量(kg)
   */
  unitWeight?: number;

  /**
   * 包装类型
   */
  packageType?: string;

  /**
   * 包装类型Label
   */
  packageTypeLabel?: string;

  /**
   * 品牌
   */
  brandKey?: string;

  /**
   * 是否显示等级
   */
  isShow?: string;

  /**
   * 销售状态
   */
  saleStatus?: string;

  /**
   * 排序
   */
  sort?: number;

  /**
   * 商品标签
   */
  tags?: string;

  /**
   * 商品图片
   */
  images?: string;

  /**
   * 商品角标
   */
  subImageId?: string | number;

  /**
   * 商品视频
   */
  video?: string;

  /**
   * 通用商品详情
   */
  generalDetailId?: string | number;

  /**
   * 商品详情
   */
  detail?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 前端分类
   */
  frontCategoryIds?: string | number;

  /**
   * 前端分类Label
   */
  frontCategoryLabel?: string;

  /**
   * 后台分类
   */
  backCategoryId?: string | number;

  /**
   * 后台分类Label
   */
  backCategoryLabel?: string;

  /**
   * 库存
   */
  stockQty?: number;

  /**
   * 零售价格
   */
  currentSalePrice?: number;

  /**
   * 市场价格
   */
  currentMarketPrice?: number;

  /**
   * 成本价
   */
  currentCostPrice?: number;

  /**
   * 分享描述
   */
  shareDesc?: string;
}

export interface GoodsQuery extends PageQuery {
  /**
   * 商品名称
   */
  name?: string;

  /**
   * 商品编码
   */
  code?: string;

  /**
   * 是否erp商品
   */
  isErpGoods?: string;

  /**
   * erp商品编码
   */
  erpCode?: string;

  /**
   * 69码
   */
  barcode?: string;

  /**
   * 采购单位
   */
  purchaseUnit?: string;

  /**
   * 是否组合商品
   */
  isCombinedGoods?: string;

  /**
   * 商品类型
   */
  goodsType?: string;

  /**
   * 商品等级
   */
  goodsGrade?: string;

  /**
   * 是否显示等级
   */
  isShowGrade?: string;

  /**
   * 销售单位
   */
  saleUnit?: string;

  /**
   * 商品重量(kg)
   */
  unitWeight?: number;

  /**
   * 包装类型
   */
  packageType?: string;

  /**
   * 品牌
   */
  brandKey?: string;

  /**
   * 是否显示等级
   */
  isShow?: string;

  /**
   * 销售状态
   */
  saleStatus?: string;

  /**
   * 商品标签
   */
  tags?: string;

  /**
   * 商品角标
   */
  subImageId?: string | number;

  /**
   * 通用商品详情
   */
  generalDetailId?: string | number;

  /**
   * 前端分类
   */
  frontCategoryIds?: string | number;

  /**
   * 后台分类
   */
  backCategoryId?: string | number;

  /**
   * 库存
   */
  stockQty?: number;

  /**
   * 零售价格
   */
  currentSalePrice?: number;

  /**
   * 市场价格
   */
  currentMarketPrice?: number;

  /**
   * 分享描述
   */
  shareDesc?: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

export interface GoodsSimpleVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 商品名称
   */
  name: string;

  /**
   * 商品编码
   */
  code: string;

  /**
   * 是否erp商品
   */
  isErpGoods: string;

  /**
   * erp商品编码
   */
  erpCode: string;

  /**
   * 是否组合商品
   */
  isCombinedGoods: string;

  /**
   * 商品类型
   */
  goodsType: string;

  /**
   * 销售单位
   */
  saleUnit: string;

  /**
   * 销售单位Label
   */
  saleUnitLabel: string;

  /**
   * 销售状态
   */
  saleStatus: string;

  /**
   * 销售状态Label
   */
  saleStatusLabel: string;

  /**
   * 排序
   */
  sort: number;

  /**
   * 商品图片
   */
  images: string;

  /**
   * 商品图片Url
   */
  imagesUrl: string;

  /**
   * 前端分类
   */
  frontCategoryIds: string | number;

  /**
   * 前端分类Label
   */
  frontCategoryLabel: string;

  /**
   * 后台分类
   */
  backCategoryId: string | number;

  /**
   * 后台分类Label
   */
  backCategoryLabel: string;

  /**
   * 零售价
   */
  currentSalePrice: number;

  /**
   * 结算价
   */
  currentMarketPrice: number;

  /**
   * 成本价
   */
  currentCostPrice: number;
}
