export interface OrderLogisticsVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 订单id
   */
  orderId: string | number;

  /**
   * 仓库id
   */
  warehouseId: string | number;

  /**
   * 匹配类型
   */
  matchType: string;

  /**
   * 快递公司
   */
  courierCompanyKey: string;

  /**
   * 快递单号
   */
  courierNo: string;

  /**
   * 发货状态，关联数据字典「mall_courier_status」
   */
  shippingStatus: string;

  /**
   * 发货变更
   */
  changeStatusTime: string;

  /**
   * 物流状态
   */
  logisticsStatus: string;

  /**
   * 物流变更
   */
  logisticsStatusTime: string;

  /**
   * 快递费用
   */
  courierAmount: number;

  /**
   * 快递备注
   */
  courierRemark: string;

  /**
   * 收货时间
   */
  receptionTime: string;

  /**
   * 是否签收
   */
  isCheck: string;

  /**
   * 收货类型
   */
  receptionType: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新人
   */
  updateBy: number;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface OrderLogisticsForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 订单id
   */
  orderId?: string | number;

  /**
   * 仓库id
   */
  warehouseId?: string | number;

  /**
   * 匹配类型
   */
  matchType?: string;

  /**
   * 快递公司
   */
  courierCompanyKey?: string;

  /**
   * 快递单号
   */
  courierNo?: string;

  /**
   * 发货状态，关联数据字典「mall_courier_status」
   */
  shippingStatus?: string;

  /**
   * 发货变更
   */
  changeStatusTime?: string;

  /**
   * 物流状态
   */
  logisticsStatus?: string;

  /**
   * 物流变更
   */
  logisticsStatusTime?: string;

  /**
   * 快递费用
   */
  courierAmount?: number;

  /**
   * 快递备注
   */
  courierRemark?: string;

  /**
   * 收货时间
   */
  receptionTime?: string;

  /**
   * 是否签收
   */
  isCheck?: string;

  /**
   * 收货类型
   */
  receptionType?: string;

  /**
   * 快递单详情
   */
  detail?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface OrderLogisticsQuery extends PageQuery {
  /**
   * 订单id
   */
  orderId?: string | number;

  /**
   * 仓库id
   */
  warehouseId?: string | number;

  /**
   * 匹配类型
   */
  matchType?: string;

  /**
   * 快递公司
   */
  courierCompanyKey?: string;

  /**
   * 快递单号
   */
  courierNo?: string;

  /**
   * 发货状态，关联数据字典「mall_courier_status」
   */
  shippingStatus?: string;

  /**
   * 发货变更
   */
  changeStatusTime?: string;

  /**
   * 物流状态
   */
  logisticsStatus?: string;

  /**
   * 物流变更
   */
  logisticsStatusTime?: string;

  /**
   * 快递费用
   */
  courierAmount?: number;

  /**
   * 快递备注
   */
  courierRemark?: string;

  /**
   * 收货时间
   */
  receptionTime?: string;

  /**
   * 是否签收
   */
  isCheck?: string;

  /**
   * 收货类型
   */
  receptionType?: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
