import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { OrderLogisticsVO, OrderLogisticsForm, OrderLogisticsQuery } from '@/api/mall/orderLogistics/types';

/**
 * 查询快递物流列表
 * @param query
 * @returns {*}
 */

export const listOrderLogistics = (query?: OrderLogisticsQuery): AxiosPromise<OrderLogisticsVO[]> => {
  return request({
    url: '/mall/orderLogistics/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询快递物流详细
 * @param id
 */
export const getOrderLogistics = (id: string | number): AxiosPromise<OrderLogisticsVO> => {
  return request({
    url: '/mall/orderLogistics/' + id,
    method: 'get'
  });
};

/**
 * 新增快递物流
 * @param data
 */
export const addOrderLogistics = (data: OrderLogisticsForm) => {
  return request({
    url: '/mall/orderLogistics',
    method: 'post',
    data: data
  });
};

/**
 * 修改快递物流
 * @param data
 */
export const updateOrderLogistics = (data: OrderLogisticsForm) => {
  return request({
    url: '/mall/orderLogistics',
    method: 'put',
    data: data
  });
};

/**
 * 删除快递物流
 * @param id
 */
export const delOrderLogistics = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mall/orderLogistics/' + id,
    method: 'delete'
  });
};
