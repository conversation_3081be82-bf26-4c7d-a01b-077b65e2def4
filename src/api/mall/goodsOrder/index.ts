import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GoodsOrderVO, GoodsOrderForm, GoodsOrderQuery } from '@/api/mall/goodsOrder/types';

/**
 * 查询商品订单列表
 * @param query
 * @returns {*}
 */

export const listGoodsOrder = (query?: GoodsOrderQuery): AxiosPromise<GoodsOrderVO[]> => {
  return request({
    url: '/mall/goodsOrder/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询商品订单详细
 * @param id
 */
export const getGoodsOrder = (id: string | number): AxiosPromise<GoodsOrderVO> => {
  return request({
    url: '/mall/goodsOrder/' + id,
    method: 'get'
  });
};

/**
 * 新增商品订单
 * @param data
 */
export const addGoodsOrder = (data: GoodsOrderForm) => {
  return request({
    url: '/mall/goodsOrder',
    method: 'post',
    data: data
  });
};

/**
 * 修改商品订单
 * @param data
 */
export const updateGoodsOrder = (data: GoodsOrderForm) => {
  return request({
    url: '/mall/goodsOrder',
    method: 'put',
    data: data
  });
};

/**
 * 删除商品订单
 * @param id
 */
export const delGoodsOrder = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mall/goodsOrder/' + id,
    method: 'delete'
  });
};

/**
 * 关闭商品订单
 * @param data 包含ids和remark
 */
export const closeGoodsOrder = (data: { ids: string | number | Array<string | number>; remark: string }) => {
  return request({
    url: '/mall/goodsOrder/close',
    method: 'put',
    data: data
  });
};

/**
 * 上传同步商品订单
 * @param data 订单ID数组
 */
export const uploadGoodsOrder = (data: Array<string | number>) => {
  return request({
    url: '/mall/goodsOrder/upload',
    method: 'post',
    data: data
  });
};
