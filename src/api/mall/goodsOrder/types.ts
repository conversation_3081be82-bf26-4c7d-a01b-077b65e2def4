import { GoodsOrderDetailVO, GoodsOrderDetailForm } from '@/api/mall/goodsOrderDetail/types';
import { GoodsOrderPaymentForm, GoodsOrderPaymentVO } from '@/api/mall/goodsOrderPayment/types';
import { OrderLogisticsVO, OrderLogisticsForm } from '@/api/mall/orderLogistics/types';

export interface GoodsOrderVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 订单编号
   */
  orderCode: string;

  /**
   * 订单类型
   */
  orderType: string;

  /**
   * 订单状态
   */
  orderStatus: string;

  /**
   * 订单状态时间
   */
  statusChangeTime: string;

  /**
   * B端客户
   */
  customerId: string | number;

  /**
   * B端客户昵称
   */
  customerName: string;

  /**
   * C端客户
   */
  ccustomerPhone: string | number;

  /**
   * 承做人
   */
  ownerId: string | number;

  /**
   * 承做人昵称
   */
  ownerNickName: string;

  /**
   * 承做部门
   */
  ownerDeptId: string | number;

  /**
   * 承做部门名称
   */
  ownerDeptName: string;

  /**
   * 订单总金额
   */
  totalAmount: number;

  /**
   * 优惠金额
   */
  couponPrice: number;

  /**
   * 支付金额
   */
  paymentPrice: number;

  /**
   * 支付状态
   */
  orderPaymentStatus: string;

  /**
   * 计划发货日期
   */
  shippingDate: string;

  /**
   * 收件人姓名
   */
  recipientName: string;

  /**
   * 收件人电话
   */
  recipientPhone: string;

  /**
   * 收件人省份
   */
  recipientProvince: string;

  /**
   * 省份名称
   */
  provinceName: string;
  /**
   * 收件人城市
   */
  recipientCity: string;

  /**
   * 城市名称
   */
  cityName: string;

  /**
   * 收件人区县
   */
  recipientArea: string;

  /**
   * 区县名称
   */
  areaName: string;
  /**
   * 收件人地址
   */
  recipientAddress: string;

  /**
   * B端客户备注
   */
  bnote: string;

  /**
   * C端客户备注
   */
  cnote: string;

  /**
   * 标签
   */
  tags: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新人
   */
  updateBy: number;

  /**
   * 更新人昵称
   */
  updateByNickName: string;

  /**
   * 更新时间
   */
  updateTime: string;

  /**
   * 明细数量
   */
  detailCount: number;
  /**
   * 订单明细
   */
  goodsOrderDetailList: GoodsOrderDetailVO[];

  /**
   * 订单支付
   */
  goodsOrderPaymentList: GoodsOrderPaymentVO[];

  /**
   * 订单物流
   */
  orderLogisticsList: OrderLogisticsVO[];
}

export interface GoodsOrderForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 订单编号
   */
  orderCode?: string;

  /**
   * 订单类型
   */
  orderType?: string;

  /**
   * 订单状态
   */
  orderStatus?: string;

  /**
   * 订单状态时间
   */
  statusChangeTime?: string;

  /**
   * B端客户
   */
  customerId?: string | number;

  /**
   * B端客户昵称
   */
  customerName?: string;

  /**
   * C端客户
   */
  ccustomerPhone?: string | number;

  /**
   * 承做人
   */
  ownerId?: string | number;

  /**
   * 承做人昵称
   */
  ownerNickName?: string;

  /**
   * 承做部门
   */
  ownerDeptId?: string | number;

  /**
   * 承做部门名称
   */
  ownerDeptName?: string;

  /**
   * 订单总金额
   */
  totalAmount?: number;

  /**
   * 优惠金额
   */
  couponPrice?: number;

  /**
   * 支付金额
   */
  paymentPrice?: number;

  /**
   * 支付状态
   */
  orderPaymentStatus?: string;

  /**
   * 计划发货日期
   */
  shippingDate?: string;

  /**
   * 收件人姓名
   */
  recipientName?: string;

  /**
   * 收件人电话
   */
  recipientPhone?: string;

  /**
   * 收件人省份
   */
  recipientProvince?: string;

  /**
   * 省份名称
   */
  provinceName?: string;

  /**
   * 收件人城市
   */
  recipientCity?: string;

  /**
   * 城市名称
   */
  cityName?: string;

  /**
   * 收件人区县
   */
  recipientArea?: string;

  /**
   * 区县名称
   */
  areaName?: string;

  /**
   * 收件人地址
   */
  recipientAddress?: string;

  /**
   * B端客户备注
   */
  bnote?: string;

  /**
   * C端客户备注
   */
  cnote?: string;

  /**
   * 标签
   */
  tags?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 更新人昵称
   */
  updateByNickName?: string;

  /**
   * 明细数量
   */
  detailCount?: number;

  /**
   * 订单明细
   */
  goodsOrderDetailList?: GoodsOrderDetailForm[];

  /**
   * 订单支付
   */
  goodsOrderPaymentList?: GoodsOrderPaymentForm[];

  /**
   * 订单物流
   */
  orderLogisticsList?: OrderLogisticsForm[];
}

export interface GoodsOrderQuery extends PageQuery {
  /**
   * 订单编号
   */
  orderCode?: string;

  /**
   * 订单类型
   */
  orderType?: string;

  /**
   * 订单状态
   */
  orderStatus?: string;

  /**
   * 订单状态时间
   */
  statusChangeTime?: string;

  /**
   * B端客户
   */
  customerId?: string | number;

  /**
   * C端客户
   */
  cCustomerPhone?: string | number;

  /**
   * 承做人
   */
  ownerId?: string | number;

  /**
   * 承做部门
   */
  ownerDeptId?: string | number;

  /**
   * 支付状态
   */
  orderPaymentStatus?: string;

  /**
   * 计划发货日期
   */
  shippingDate?: string;

  /**
   * 收件人姓名
   */
  recipientName?: string;

  /**
   * 收件人电话
   */
  recipientPhone?: string;

  /**
   * 收件人省份
   */
  recipientProvince?: string;

  /**
   * 收件人城市
   */
  recipientCity?: string;

  /**
   * 收件人区县
   */
  recipientArea?: string;

  /**
   * 收件人地址
   */
  recipientAddress?: string;

  /**
   * B端客户备注
   */
  bnote?: string;

  /**
   * C端客户备注
   */
  cnote?: string;

  /**
   * 标签
   */
  tags?: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
