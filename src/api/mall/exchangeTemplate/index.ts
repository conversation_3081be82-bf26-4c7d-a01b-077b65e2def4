import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ExchangeTemplateVO, ExchangeTemplateForm, ExchangeTemplateQuery } from '@/api/mall/exchangeTemplate/types';

/**
 * 查询兑换卡模板列表
 * @param query
 * @returns {*}
 */
export const listExchangeTemplate = (query?: ExchangeTemplateQuery): AxiosPromise<ExchangeTemplateVO[]> => {
  return request({
    url: '/mall/exchangeTemplate/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询兑换卡模板详细
 * @param id
 */
export const getExchangeTemplate = (id: string | number): AxiosPromise<ExchangeTemplateVO> => {
  return request({
    url: '/mall/exchangeTemplate/' + id,
    method: 'get'
  });
};

/**
 * 新增兑换卡模板
 * @param data
 */
export const addExchangeTemplate = (data: ExchangeTemplateForm) => {
  return request({
    url: '/mall/exchangeTemplate',
    method: 'post',
    data: data
  });
};

/**
 * 修改兑换卡模板
 * @param data
 */
export const updateExchangeTemplate = (data: ExchangeTemplateForm) => {
  return request({
    url: '/mall/exchangeTemplate',
    method: 'put',
    data: data
  });
};

/**
 * 删除兑换卡模板
 * @param id
 */
export const delExchangeTemplate = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mall/exchangeTemplate/' + id,
    method: 'delete'
  });
};
