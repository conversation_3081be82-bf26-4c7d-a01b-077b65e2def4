export interface ExchangeTemplateVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 组织id
   */
  createOrgId: string | number;

  /**
   * 卡名称
   */
  name: string;

  /**
   * 卡编码
   */
  code: string;

  /**
   * 封面图片
   */
  coverImage: string;

  /**
   * 封面图片URL
   */
  coverImageUrl: string;

  /**
   * 状态（0启用 1停用）
   */
  status: string;

  /**
   * 卡类型
   */
  cardType: string;

  /**
   * 卡面形态
   */
  cardForm: string;

  /**
   * 兑换次数
   */
  times: number;

  /**
   * 卡描述
   */
  desc: string;

  /**
   * 使用说明
   */
  instruction: string;

  /**
   * 面值，单位为分
   */
  value: number;

  /**
   * 卡次明细
   */
  detail: string;

  /**
   * 激活方式
   */
  actType: string;

  /**
   * 有效期类型
   */
  effType: string;

  /**
   * 排序
   */
  sort: number;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新人昵称
   */
  updateNickName?: string;

  /**
   * 更新时间
   */
  updateTime?: string;
}

export interface ExchangeTemplateForm {
  /**
   * id
   */
  id?: string | number;

  /**
   * 组织id
   */
  createOrgId?: string | number;

  /**
   * 卡名称
   */
  name?: string;

  /**
   * 卡编码
   */
  code?: string;

  /**
   * 封面图片
   */
  coverImage?: string;

  /**
   * 状态（0启用 1停用）
   */
  status?: string;

  /**
   * 卡类型
   */
  cardType?: string;

  /**
   * 卡面形态
   */
  cardForm?: string;

  /**
   * 兑换次数
   */
  times?: number;

  /**
   * 卡描述
   */
  desc?: string;

  /**
   * 使用说明
   */
  instruction?: string;

  /**
   * 面值，单位为分
   */
  value?: number;

  /**
   * 卡次明细
   */
  detail?: string;

  /**
   * 激活方式
   */
  actType?: string;

  /**
   * 有效期类型
   */
  effType?: string;

  /**
   * 排序
   */
  sort?: number;

  /**
   * 备注
   */
  remark?: string;
}

export interface ExchangeTemplateQuery {
  /**
   * 当前页码
   */
  pageNum?: number;

  /**
   * 每页条数
   */
  pageSize?: number;

  /**
   * 卡名称
   */
  name?: string;

  /**
   * 卡编码
   */
  code?: string;

  /**
   * 状态（0启用 1停用）
   */
  status?: string;

  /**
   * 卡类型
   */
  cardType?: string;

  /**
   * 卡面形态
   */
  cardForm?: string;

  /**
   * 激活方式
   */
  actType?: string;

  /**
   * 有效期类型
   */
  effType?: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
