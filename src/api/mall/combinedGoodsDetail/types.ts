import { GoodsVO } from '@/api/mall/goods/types';

export interface CombinedGoodsDetailVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 商品
   */
  goodsId: string | number;

  /**
   * 商品名称
   */
  goodsName: string;

  /**
   * 商品明细
   */
  detailId: string | number;

  /**
   * 商品明细名称
   */
  detailName: string;

  /**
   * 商品明细信息
   */
  detailGoodsVo: GoodsVO;

  /**
   * 数量
   */
  qty: number;

  /**
   * 是否计价，关联字典「sys_yes_no」
   */
  isInPrice: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新人
   */
  updateBy: number;

  /**
   * 更新人昵称
   */
  updateNickname: string;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface CombinedGoodsDetailForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 商品
   */
  goodsId?: string | number;

  /**
   * 商品明细
   */
  detailId?: string | number;

  /**
   * 数量
   */
  qty?: number;

  /**
   * 销售单位标签
   */
  saleUnitLabel?: string;

  /**
   * 是否计价，关联字典「sys_yes_no」
   */
  isInPrice?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface CombinedGoodsDetailQuery extends PageQuery {
  /**
   * 商品
   */
  goodsId?: string | number;

  /**
   * 商品明细
   */
  detailId?: string | number;

  /**
   * 是否计价，关联字典「sys_yes_no」
   */
  isInPrice?: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
