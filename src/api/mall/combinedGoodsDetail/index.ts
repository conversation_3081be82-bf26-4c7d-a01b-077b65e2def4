import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CombinedGoodsDetailVO, CombinedGoodsDetailForm, CombinedGoodsDetailQuery } from '@/api/mall/combinedGoodsDetail/types';

/**
 * 查询组合商品明细列表
 * @param query
 * @returns {*}
 */

export const listCombinedGoodsDetail = (query?: CombinedGoodsDetailQuery): AxiosPromise<CombinedGoodsDetailVO[]> => {
  return request({
    url: '/mall/combinedGoodsDetail/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询组合商品明细详细
 * @param id
 */
export const getCombinedGoodsDetail = (id: string | number): AxiosPromise<CombinedGoodsDetailVO> => {
  return request({
    url: '/mall/combinedGoodsDetail/' + id,
    method: 'get'
  });
};

/**
 * 新增组合商品明细
 * @param data
 */
export const addCombinedGoodsDetail = (data: CombinedGoodsDetailForm) => {
  return request({
    url: '/mall/combinedGoodsDetail',
    method: 'post',
    data: data
  });
};

/**
 * 修改组合商品明细
 * @param data
 */
export const updateCombinedGoodsDetail = (data: CombinedGoodsDetailForm) => {
  return request({
    url: '/mall/combinedGoodsDetail',
    method: 'put',
    data: data
  });
};

/**
 * 删除组合商品明细
 * @param id
 */
export const delCombinedGoodsDetail = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mall/combinedGoodsDetail/' + id,
    method: 'delete'
  });
};
