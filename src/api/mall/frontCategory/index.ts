import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { FrontCategoryVO, FrontCategoryForm, FrontCategoryQuery } from '@/api/mall/frontCategory/types';

/**
 * 查询前台分类列表
 * @param query
 * @returns {*}
 */

export const listFrontCategory = (query?: FrontCategoryQuery): AxiosPromise<FrontCategoryVO[]> => {
  return request({
    url: '/mall/frontCategory/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询前台分类详细
 * @param id
 */
export const getFrontCategory = (id: string | number): AxiosPromise<FrontCategoryVO> => {
  return request({
    url: '/mall/frontCategory/' + id,
    method: 'get'
  });
};

/**
 * 新增前台分类
 * @param data
 */
export const addFrontCategory = (data: FrontCategoryForm) => {
  return request({
    url: '/mall/frontCategory',
    method: 'post',
    data: data
  });
};

/**
 * 修改前台分类
 * @param data
 */
export const updateFrontCategory = (data: FrontCategoryForm) => {
  return request({
    url: '/mall/frontCategory',
    method: 'put',
    data: data
  });
};

/**
 * 删除前台分类
 * @param id
 */
export const delFrontCategory = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mall/frontCategory/' + id,
    method: 'delete'
  });
};

/**
 * 获取前台分类的子类数量
 * @param parentId
 */
export const countFrontCategoryByParentId = (parentId: string | number): AxiosPromise<number> => {
  return request({
    url: '/mall/frontCategory/ChildrenCount/' + parentId,
    method: 'get'
  });
};
