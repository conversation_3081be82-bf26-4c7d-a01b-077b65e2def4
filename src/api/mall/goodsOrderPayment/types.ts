export interface GoodsOrderPaymentVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 订单id
   */
  orderId: string | number;

  /**
   * 支付方式
   */
  paymentMethod: string;

  /**
   * 支付状态
   */
  paymentStatus: string;

  /**
   * 支付流水号
   */
  paymentId: string | number;

  /**
   * 卡号
   */
  cardCode: string;

  /**
   * 卡次
   */
  cardInstance: string;

  /**
   * 支付时间
   */
  paymentTime: string;

  /**
   * 支付金额
   */
  paymentAmount: number;

  /**
   * 退款流水号
   */
  refundId: string | number;

  /**
   * 退款时间
   */
  refundTime: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 更新人
   */
  updateBy: number;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface GoodsOrderPaymentForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 订单id
   */
  orderId?: string | number;

  /**
   * 支付方式
   */
  paymentMethod?: string;

  /**
   * 支付状态
   */
  paymentStatus?: string;

  /**
   * 支付流水号
   */
  paymentId?: string | number;

  /**
   * 卡号
   */
  cardCode?: string;

  /**
   * 卡次
   */
  cardInstance?: string;

  /**
   * 支付时间
   */
  paymentTime?: string;

  /**
   * 支付金额
   */
  paymentAmount?: number;

  /**
   * 退款流水号
   */
  refundId?: string | number;

  /**
   * 退款时间
   */
  refundTime?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface GoodsOrderPaymentQuery extends PageQuery {
  /**
   * 订单id
   */
  orderId?: string | number;

  /**
   * 支付方式
   */
  paymentMethod?: string;

  /**
   * 支付状态
   */
  paymentStatus?: string;

  /**
   * 支付流水号
   */
  paymentId?: string | number;

  /**
   * 卡号
   */
  cardCode?: string;

  /**
   * 卡次
   */
  cardInstance?: string;

  /**
   * 支付时间
   */
  paymentTime?: string;

  /**
   * 支付金额
   */
  paymentAmount?: number;

  /**
   * 退款流水号
   */
  refundId?: string | number;

  /**
   * 退款时间
   */
  refundTime?: string;

  /**
   * 更新人
   */
  updateBy?: number;

  /**
   * 更新时间
   */
  updateTime?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
