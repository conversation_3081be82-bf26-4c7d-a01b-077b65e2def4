import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { GoodsOrderPaymentVO, GoodsOrderPaymentForm, GoodsOrderPaymentQuery } from '@/api/mall/goodsOrderPayment/types';

/**
 * 查询商品订单支付列表
 * @param query
 * @returns {*}
 */

export const listGoodsOrderPayment = (query?: GoodsOrderPaymentQuery): AxiosPromise<GoodsOrderPaymentVO[]> => {
  return request({
    url: '/mall/goodsOrderPayment/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询商品订单支付详细
 * @param id
 */
export const getGoodsOrderPayment = (id: string | number): AxiosPromise<GoodsOrderPaymentVO> => {
  return request({
    url: '/mall/goodsOrderPayment/' + id,
    method: 'get'
  });
};

/**
 * 新增商品订单支付
 * @param data
 */
export const addGoodsOrderPayment = (data: GoodsOrderPaymentForm) => {
  return request({
    url: '/mall/goodsOrderPayment',
    method: 'post',
    data: data
  });
};

/**
 * 修改商品订单支付
 * @param data
 */
export const updateGoodsOrderPayment = (data: GoodsOrderPaymentForm) => {
  return request({
    url: '/mall/goodsOrderPayment',
    method: 'put',
    data: data
  });
};

/**
 * 删除商品订单支付
 * @param id
 */
export const delGoodsOrderPayment = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mall/goodsOrderPayment/' + id,
    method: 'delete'
  });
};
