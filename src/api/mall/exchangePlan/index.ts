import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ExchangePlanVO, ExchangePlanForm, ExchangePlanQuery } from '@/api/mall/exchangePlan/types';

/**
 * 查询兑换方案列表
 * @param query
 * @returns {*}
 */

export const listExchangePlan = (query?: ExchangePlanQuery): AxiosPromise<ExchangePlanVO[]> => {
  return request({
    url: '/mall/exchangePlan/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询兑换方案详细
 * @param id
 */
export const getExchangePlan = (id: string | number): AxiosPromise<ExchangePlanVO> => {
  return request({
    url: '/mall/exchangePlan/' + id,
    method: 'get'
  });
};

/**
 * 新增兑换方案
 * @param data
 */
export const addExchangePlan = (data: ExchangePlanForm) => {
  return request({
    url: '/mall/exchangePlan',
    method: 'post',
    data: data
  });
};

/**
 * 修改兑换方案
 * @param data
 */
export const updateExchangePlan = (data: ExchangePlanForm) => {
  return request({
    url: '/mall/exchangePlan',
    method: 'put',
    data: data
  });
};

/**
 * 删除兑换方案
 * @param id
 */
export const delExchangePlan = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mall/exchangePlan/' + id,
    method: 'delete'
  });
};
