export interface ExchangePlanVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 组织id
   */
  createOrgId: string | number;

  /**
   * 方案名称
   */
  name: string;

  /**
   * 方案编码
   */
  code: string;

  /**
   * 状态
   */
  status: string;

  /**
   * 排序
   */
  sort: number;

  /**
   * 方案类型
   */
  type: string;

  /**
   * 市场价格
   */
  price: number;

  /**
   * 成本价
   */
  costPrice: number;

  /**
   * 最低参考售价
   */
  minSellPrice: number;
  /**
   * 可选数量
   */
  optionalQty: number;

  /**
   * 兑换描述
   */
  desc: string;

  /**
   * 兑换方案明细
   */
  detail: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 兑换开始日期
   */
  exchangeStartDate: string;

  /**
   * 兑换结束日期
   */
  exchangeEndDate: string;

  /**
   * 创建时间
   */
  createTime: string;

  /**
   * 更新人
   */
  updateBy: string;

  /**
   * 更新时间
   */
  updateTime: string;
}

export interface ExchangePlanForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 组织id
   */
  createOrgId?: string | number;

  /**
   * 方案名称
   */
  name?: string;

  /**
   * 方案编码
   */
  code?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 排序
   */
  sort?: number;

  /**
   * 兑换类型
   */
  type?: string;

  /**
   * 兑换价格
   */
  price?: number;

  /**
   * 可选数量
   */
  optionalQty?: number;

  /**
   * 兑换描述
   */
  desc?: string;

  /**
   * 兑换方案明细
   */
  detail?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 兑换开始日期
   */
  exchangeStartDate?: string;

  /**
   * 兑换结束日期
   */
  exchangeEndDate?: string;
}

export interface ExchangePlanQuery extends PageQuery {
  /**
   * 组织id
   */
  createOrgId?: string | number;

  /**
   * 方案名称
   */
  name?: string;

  /**
   * 方案编码
   */
  code?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 排序
   */
  sort?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
