export const convertToWan = (value: number): string => {
  if (!value) return '';

  // 格式化数字，去除末尾的.00
  const formatNumber = (num: number, unit: string): string => {
    return num.toFixed(2).replace(/\.?0+$/, '') + unit;
  };

  // 小于等于1万，显示原值
  if (value <= 10000) {
    return formatNumber(value, '元');
  }

  // 小于1亿，显示万为单位
  if (value < 100000000) {
    const wan = value / 10000;
    return formatNumber(wan, '万元');
  }

  // 大于等于1亿，显示亿为单位
  const yi = value / 100000000;
  return formatNumber(yi, '亿元');
};
