/**
 * 将分转换为元
 * @param cent 分为单位的金额
 * @returns 元为单位的金额字符串
 */
export const centToYuan = (cent: number | undefined | null): string => {
  if (cent === undefined || cent === null) return '';
  return (cent / 100).toFixed(2);
};

/**
 * 将元转换为分
 * @param yuan 元为单位的金额
 * @returns 分为单位的金额数值
 */
export const yuanToCent = (yuan: number | string | undefined | null): number => {
  if (yuan === undefined || yuan === null) return 0;
  const amount = typeof yuan === 'string' ? parseFloat(yuan) : yuan;
  return Math.round(amount * 100);
};
