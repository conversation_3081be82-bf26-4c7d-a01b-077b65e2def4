<template>
  <el-select
    v-model="selectedWarehouseId"
    placeholder="请选择大仓"
    filterable
    remote
    reserve-keyword
    :loading="warehouseLoading"
    :remote-method="remoteWarehouseListMethod"
    @change="handleWarehouseChange"
    @keyup.enter="handleWarehouseChange"
    required
    :disabled="disabled"
    clearable
  >
    <el-option v-for="item in warehouseOptions" :key="item.id" :label="item.mainLable" :value="item.value">
      <el-tooltip content="大仓名称" placement="top">
        <span>{{ item.mainLable }}</span>
      </el-tooltip>
      <el-tooltip content="备注" placement="top">
        <span style="float: right; color: #999">{{ item.desc }}</span>
      </el-tooltip>
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
// 获取大仓列表作为option内容
import { listWarehouse, getWarehouse } from '@/api/warehouse/warehouse';
import { useRemoteListMoreMethod } from '@/hooks/useBusiness/useSelectMore/remote';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 组件属性
const props = defineProps<{
  modelValue?: string | number; // v-model 绑定值
  disabled?: boolean; // 添加 disabled 属性
}>();

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string | number];
  'change': [value: string | number];
}>();

// 组件内部状态
const selectedWarehouseId = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const {
  list: warehouseOptions,
  loading: warehouseLoading,
  remoteMethod: remoteWarehouseListMethod
} = useRemoteListMoreMethod(proxy, {
  FetchUrl: listWarehouse,
  value: 'id',
  mainLable: 'name',
  secondLable: 'address',
  desc: 'remark',
  queryStr: 'name',
  query: { pageNum: 1, pageSize: 10, name: '', status: '0' }
});

// 处理大仓选择变化
const handleWarehouseChange = (value: string | number) => {
  emit('change', value);
};

// 初始化默认值
const initDefaultValue = async (newValue) => {
  let currentItem = null;
  try {
    if (newValue) {
      const res = await getWarehouse(newValue);
      if (res.data) {
        currentItem = {
          id: res.data.id,
          value: res.data.id, // 添加 value 字段
          mainLable: res.data.name,
          secondLable: res.data.address,
          desc: res.data.remark
        };
      }
    } else {
      currentItem = null;
    }
    return currentItem;
  } catch (error) {
    console.error('Failed to load default warehouse:', error);
    return null;
  }
};

watch(
  () => props.modelValue,
  async (newValue) => {
    const currentItem = await initDefaultValue(newValue);
    // 加载列表数据
    await remoteWarehouseListMethod('', '', '');
    if (currentItem !== null) {
      const isExist = warehouseOptions.value.find((item) => item.value === currentItem.value);
      if (!isExist) {
        warehouseOptions.value = [currentItem, ...warehouseOptions.value];
      }
    }
  },
  { immediate: true }
);

// 暴露方法给父组件
defineExpose({
  remoteWarehouseListMethod
});
</script>
