<template>
  <el-tree-select
    v-model="selectedCategoryId"
    :data="frontCategoryOptions"
    :props="{ value: 'id', label: 'name', children: 'children' }"
    node-key="id"
    placeholder="请选择前端分类"
    filterable
    clearable
    @change="handleChange"
  />
</template>

<script setup lang="ts">
// 获取后台分类列表
import { listFrontCategory } from '@/api/mall/frontCategory';
import { FrontCategoryVO, FrontCategoryQuery, FrontCategoryForm } from '@/api/mall/frontCategory/types';

// 组件属性
const props = defineProps<{
  modelValue: string | number;
  disabled?: boolean;
}>();

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string | number];
  'change': [value: string | number];
}>();

// 组件内部状态
const selectedCategoryId = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 监听前端分类选择
const handleChange = (value: string | number) => {
  emit('change', value);
};

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

type FrontCategoryOption = {
  id: number;
  name: string;
  children?: FrontCategoryOption[];
};

const frontCategoryOptions = ref<FrontCategoryOption[]>([]);
const query = ref<FrontCategoryQuery>({
  status: '0'
});

/** 查看前端分类下拉树结构 */
const getTreeselect = async () => {
  const res = await listFrontCategory(query.value);
  frontCategoryOptions.value = [];
  frontCategoryOptions.value = proxy?.handleTree<FrontCategoryOption>(res.data, 'id', 'parentId');
};

onMounted(() => {
  getTreeselect();
});
</script>
