<template>
  <el-select
    v-model="selectedCity"
    placeholder="请选择城市"
    filterable
    remote
    reserve-keyword
    :loading="loading"
    :remote-method="remoteCityListMethod"
    @change="handleCityChange"
    :provinceCode="provinceCode"
    required
    :disabled="disabled"
    clearable
  >
    <el-option v-for="item in cityOptions" :key="item.id" :label="item.mainLable" :value="item.value">
      <el-tooltip content="城市名称" placement="top">
        <span>{{ item.mainLable }}</span>
      </el-tooltip>
      <el-tooltip content="城市编码" placement="top">
        <span style="float: right; color: #999">{{ item.secondLable }}</span>
      </el-tooltip>
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
// 获取城市列表作为option内容
import { listChinaCity, getChinaCity } from '@/api/setting/chinaCity';
import { useRemoteListMoreMethod } from '@/hooks/useBusiness/useSelectMore/remote';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 组件属性
const props = defineProps<{
  modelValue?: string | number; // v-model 绑定值
  disabled?: boolean; // 添加 disabled 属性
  provinceCode?: string | number; // 省份编码
}>();

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string | number];
  'change': [value: string | number];
}>();

// 组件内部状态
const selectedCity = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const {
  list: cityOptions,
  loading: loading,
  remoteMethod: remoteCityListMethod
} = useRemoteListMoreMethod(proxy, {
  FetchUrl: listChinaCity,
  value: 'code',
  mainLable: 'name',
  secondLable: 'code',
  desc: 'remark',
  queryStr: 'name',
  query: { pageNum: 1, pageSize: 10, name: '', provinceCode: props.provinceCode }
});

// 处理城市选择变化
const handleCityChange = (value: string | number) => {
  emit('change', value);
};

// 初始化默认值
const initDefaultValue = async (newValue) => {
  let currentItem = null;
  try {
    if (newValue) {
      const res = await getChinaCity(newValue);
      if (res.data) {
        currentItem = {
          id: res.data.id,
          value: res.data.id,
          mainLable: res.data.name,
          secondLable: res.data.code,
          desc: res.data.remark
        };
      }
    } else {
      currentItem = null;
    }
    return currentItem;
  } catch (error) {
    console.error('Failed to load default city:', error);
    return null;
  }
};

watch(
  () => props.modelValue,
  async (newValue) => {
    const currentItem = await initDefaultValue(newValue);
    // 加载列表数据
    await remoteCityListMethod('', '', '');
    if (currentItem !== null) {
      const isExist = cityOptions.value.find((item) => item.value === currentItem.value);
      if (!isExist) {
        cityOptions.value = [currentItem, ...cityOptions.value];
      }
    }
  },
  { immediate: true }
);

watch(
  () => props.provinceCode,
  async (newValue) => {
    selectedCity.value = '';
    await remoteCityListMethod('', '', '', { provinceCode: newValue });
  }
);

// 暴露方法给父组件
defineExpose({
  remoteCityListMethod
});
</script>
