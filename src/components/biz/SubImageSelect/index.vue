<template>
  <div>
    <el-select
      v-model="selectedSubImageId"
      placeholder="请选择角标"
      filterable
      remote
      reserve-keyword
      :loading="subImageLoading"
      :remote-method="remoteSubImageListMethod"
      @change="handleSubImageChange"
      @keyup.enter="handleSubImageChange"
      required
      :disabled="disabled"
      clearable
    >
      <el-option v-for="item in subImageOptions" :key="item.id" :label="item.mainLable" :value="item.value" />
    </el-select>
  </div>
</template>

<script setup lang="ts">
// 获取商品角标列表作为option内容
import { listSubImage, getSubImage } from '@/api/mall/subImage';
import { useRemoteListMoreMethod } from '@/hooks/useBusiness/useSelectMore/remote';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 组件属性
const props = defineProps<{
  modelValue?: string | number; // v-model 绑定值
  disabled?: boolean; // 添加 disabled 属性
}>();

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string | number];
  'change': [value: string | number];
}>();

// 组件内部状态
const selectedSubImageId = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const {
  list: subImageOptions,
  loading: subImageLoading,
  remoteMethod: remoteSubImageListMethod
} = useRemoteListMoreMethod(proxy, {
  FetchUrl: listSubImage,
  value: 'id',
  mainLable: 'name',
  desc: 'remark',
  queryStr: 'name',
  query: { pageNum: 1, pageSize: 10, name: '', status: '0' }
});

// 处理角标选择变化
const handleSubImageChange = (value: string | number) => {
  emit('change', value);
};

// 初始化默认值
const initDefaultValue = async (newValue) => {
  let currentItem = null;
  try {
    // 只在有 modelValue 时获取客户信息
    if (newValue) {
      const res = await getSubImage(newValue);
      if (res.data) {
        currentItem = {
          value: res.data.id,
          mainLable: res.data.name,
          desc: res.data.remark
        };
      }
    } else {
      currentItem = null;
    }
    return currentItem;
  } catch (error) {
    console.error('Failed to load default subImage:', error);
  }
};

watch(
  () => props.modelValue,
  async (newValue) => {
    const currentItem = await initDefaultValue(newValue);
    // 加载列表数据
    await remoteSubImageListMethod('', '', '');
    if (currentItem !== null) {
      const isExist = subImageOptions.value.find((item) => item.value === currentItem.value);
      if (!isExist) {
        subImageOptions.value = [currentItem, ...subImageOptions.value];
      }
    }
  },
  { immediate: true }
);

// 暴露方法给父组件
defineExpose({
  remoteSubImageListMethod
});
</script>
