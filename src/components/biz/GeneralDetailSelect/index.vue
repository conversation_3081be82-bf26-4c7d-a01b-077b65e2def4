<template>
  <div>
    <el-select
      v-model="selectedGeneralDetailId"
      placeholder="请选择通用详情"
      filterable
      remote
      reserve-keyword
      :loading="generalDetailLoading"
      :remote-method="remoteGeneralDetailListMethod"
      @change="handleGeneralDetailChange"
      @keyup.enter="handleGeneralDetailChange"
      required
      :disabled="disabled"
      clearable
    >
      <el-option v-for="item in generalDetailOptions" :key="item.id" :label="item.mainLable" :value="item.value" />
    </el-select>
  </div>
</template>

<script setup lang="ts">
// 获取通用详情列表作为option内容
import { listGeneralDetail, getGeneralDetail } from '@/api/mall/generalDetail';
import { useRemoteListMoreMethod } from '@/hooks/useBusiness/useSelectMore/remote';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 组件属性
const props = defineProps<{
  modelValue?: string | number; // v-model 绑定值
  disabled?: boolean; // 添加 disabled 属性
}>();

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string | number];
  'change': [value: string | number];
}>();

// 组件内部状态
const selectedGeneralDetailId = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const {
  list: generalDetailOptions,
  loading: generalDetailLoading,
  remoteMethod: remoteGeneralDetailListMethod
} = useRemoteListMoreMethod(proxy, {
  FetchUrl: listGeneralDetail,
  value: 'id',
  mainLable: 'name',
  desc: 'remark',
  queryStr: 'name',
  query: { pageNum: 1, pageSize: 10, name: '', status: '0' }
});

// 处理通用详情选择变化
const handleGeneralDetailChange = (value: string | number) => {
  emit('change', value);
};

// 初始化默认值
const initDefaultValue = async (newValue) => {
  let currentItem = null;
  try {
    // 只在有 modelValue 时获取通用详情信息
    if (newValue) {
      const res = await getGeneralDetail(newValue);
      if (res.data) {
        currentItem = {
          value: res.data.id,
          mainLable: res.data.name,
          desc: res.data.remark
        };
      }
    }
    return currentItem;
  } catch (error) {
    console.error('Failed to load default generalDetail:', error);
  }
};

watch(
  () => props.modelValue,
  async (newValue) => {
    const currentItem = await initDefaultValue(newValue);
    // 加载列表数据
    await remoteGeneralDetailListMethod('', '', '');
    if (currentItem !== null) {
      const isExist = generalDetailOptions.value.find((item) => item.value === currentItem.value);
      if (!isExist) {
        generalDetailOptions.value = [currentItem, ...generalDetailOptions.value];
      }
    }
  },
  { immediate: true }
);

// 暴露方法给父组件
defineExpose({
  remoteGeneralDetailListMethod
});
</script>
