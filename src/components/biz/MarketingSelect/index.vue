<template>
  <el-select
    v-model="selectedMarketingId"
    placeholder="请选择客户"
    filterable
    remote
    reserve-keyword
    :loading="marketingLoading"
    :remote-method="remoteMarketingListMethod"
    @change="handleHighSeaChange"
    @keyup.enter="handleHighSeaChange"
    required
    :disabled="disabled"
  >
    <el-option v-for="item in marketingOptions" :key="item.id" :label="item.mainLable" :value="item.value">
      <el-tooltip content="活动名称" placement="top">
        <span style="float: left; margin-right: 20px">{{ item.mainLable }}</span>
      </el-tooltip>
      <el-tooltip content="活动状态" placement="top">
        <span style="float: right; color: #999">{{ item.secondLable }}</span>
      </el-tooltip>
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
// 获取公海列表作为option内容
import { listMarketing, getMarketing } from '@/api/crm/marketing/index';
import { useRemoteListMoreMethod } from '@/hooks/useBusiness/useSelectMore';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 组件属性
const props = defineProps<{
  modelValue?: string | number; // v-model 绑定值
  disabled?: boolean; // 添加 disabled 属性
}>();

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string | number];
  'change': [value: string | number];
}>();

// 组件内部状态
const selectedMarketingId = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const {
  list: marketingOptions,
  loading: marketingLoading,
  remoteMethod: remoteMarketingListMethod
} = useRemoteListMoreMethod(proxy, {
  FetchUrl: listMarketing,
  value: 'id',
  mainLable: 'name',
  secondLable: 'statusLabel',
  desc: 'remark',
  queryStr: 'name',
  query: { pageNum: 1, pageSize: 10, name: '' }
});

// 处理公海选择变化
const handleHighSeaChange = (value: string | number) => {
  emit('change', value);
};

// 初始化默认值
const initDefaultValue = async () => {
  try {
    // 只在有 modelValue 时获取公海信息
    if (props.modelValue) {
      const res = await getMarketing(props.modelValue);
      if (res.data) {
        // 立即设置选项，确保有值可以显示
        marketingOptions.value = [
          {
            value: res.data.id,
            mainLable: res.data.name,
            secondLable: res.data.statusLabel,
            desc: res.data.remark
          }
        ];
      }
    }
  } catch (error) {
    console.error('Failed to load default customer:', error);
  }
};

// 组件挂载时初始化
onMounted(async () => {
  await initDefaultValue();
  // 加载列表数据，但不覆盖当前选中项
  const currentValue = marketingOptions.value[0];
  await remoteMarketingListMethod('', '', '');
  // 确保当前选中项在列表最前面
  if (currentValue) {
    marketingOptions.value = [currentValue, ...marketingOptions.value.filter((item) => item.value !== currentValue.value)];
  }
});

// 暴露方法给父组件
defineExpose({
  remoteMarketingListMethod
});
</script>
