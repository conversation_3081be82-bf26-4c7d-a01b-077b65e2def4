<template>
  <!-- <div class="address-detail"></div> -->
  <!-- <el-form :model="address" label-width="100px"> </el-form> -->
  <el-form-item label="地址">
    <template #label>
      <span>地址</span>
      <el-tooltip content="地图选择地址" placement="top">
        <el-button type="text" @click="showMapDialog">
          <el-icon :size="16" class="el-input__icon"><Location /></el-icon>
        </el-button>
      </el-tooltip>
    </template>
    <el-input
      v-model="address.completeAddress"
      type="textarea"
      :rows="3"
      placeholder="请地图选择地址或输入地址"
      clearable
      :disabled="disabled"
      maxlength="100"
      show-word-limit
    >
    </el-input>
  </el-form-item>
  <!-- 地图弹窗 -->
  <el-dialog v-if="dialogVisible" v-model="dialogVisible" title="选择地址" width="800px" destroy-on-close>
    <div class="map-container">
      <!-- 搜索框 -->
      <div class="search-box">
        <el-input v-model="searchKeyword" placeholder="请输入地址关键词" clearable @keyup.enter="handleSearch">
          <template #append>
            <el-button @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
      </div>

      <!-- 地图容器 -->
      <div ref="mapContainer" class="map"></div>

      <!-- 搜索结果列表 -->
      <div class="search-result" v-if="searchResults.length">
        <ul>
          <li v-for="(item, index) in searchResults" :key="index" @click="handleSelectAddress(item)">
            {{ item.name }}
            <div class="address-detail-text">{{ item.address }}</div>
          </li>
        </ul>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAddress">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, reactive } from 'vue';
import { ElMessage } from 'element-plus';

// 高德地图 API Key 和安全密钥
const key = '512272541697ca3c33afac2cf308be4e';
const securityJsCode = '0cfde7d7810cde63f2d391ec1124a897'; // 需要在高德开放平台设置

// 设置安全密钥
window._AMapSecurityConfig = {
  securityJsCode: securityJsCode
};

// 数据定义
const address = reactive({
  completeAddress: '',
  longitude: '',
  latitude: ''
});
const dialogVisible = ref(false);
const searchKeyword = ref('');
const searchResults = ref([]);
const selectedLocation = ref(null);
const mapContainer = ref(null);
const map = ref(null);
const marker = ref(null);
const addressDetail = ref({
  province: '',
  city: '',
  district: '',
  address: '',
  location: {
    lng: '',
    lat: ''
  }
});

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      completeAddress: '',
      lng: '',
      lat: ''
    })
  },
  defaultLocation: {
    type: Object,
    default: () => ({ lat: 39.908802, lng: 116.397502 })
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

// 监听数据变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      address.completeAddress = newVal.completeAddress || '';
      address.longitude = newVal.lng || '';
      address.latitude = newVal.lat || '';
    }
  },
  { immediate: true }
);

// 初始化地图
const initMap = () => {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载过高德地图脚本
    if (window.AMap) {
      createMap();
      resolve();
      return;
    }

    // 动态加载高德地图脚本
    const script = document.createElement('script');
    script.src = `https://webapi.amap.com/maps?v=2.0&key=${key}&plugin=AMap.PlaceSearch,AMap.Geocoder&security=1`;
    script.async = true;

    script.onload = () => {
      createMap();
      resolve();
    };

    script.onerror = reject;
    document.head.appendChild(script);
  });
};

// 创建地图实例
const createMap = () => {
  // eslint-disable-next-line no-undef
  map.value = new AMap.Map(mapContainer.value, {
    zoom: 12,
    center: [props.defaultLocation.lng, props.defaultLocation.lat]
  });

  // 创建标记
  // eslint-disable-next-line no-undef
  marker.value = new AMap.Marker({
    position: [props.defaultLocation.lng, props.defaultLocation.lat],
    draggable: true
  });

  marker.value.setMap(map.value);

  // 绑定事件
  map.value.on('click', handleMapClick);
  marker.value.on('dragend', handleMarkerDragend);
};

// 显示地图弹窗
const showMapDialog = async () => {
  dialogVisible.value = true;
  await nextTick();
  try {
    await initMap();
    // 如果有选中的位置，设置地图中心点和标记
    if (selectedLocation.value) {
      map.value.setCenter([selectedLocation.value.lng, selectedLocation.value.lat]);
      marker.value.setPosition([selectedLocation.value.lng, selectedLocation.value.lat]);
    }
  } catch (error) {
    ElMessage.error('地图加载失败，请刷新重试');
    console.error('地图初始化失败:', error);
  }
};

// 搜索地址
const handleSearch = () => {
  if (!searchKeyword.value) {
    ElMessage.warning('请输入搜索关键词');
    return;
  }
  // eslint-disable-next-line no-undef
  const placeSearch = new AMap.PlaceSearch({
    city: '全国',
    extensions: 'all'
  });

  placeSearch.search(searchKeyword.value, (status, result) => {
    if (status === 'complete' && result.info === 'OK') {
      searchResults.value = result.poiList.pois;
    } else {
      searchResults.value = [];
      ElMessage.warning('未找到相关地址');
    }
  });
};

// 选择搜索结果
const handleSelectAddress = (item) => {
  selectedLocation.value = {
    lng: item.location.lng,
    lat: item.location.lat
  };

  // 更新地址详情
  addressDetail.value = {
    province: item.pname || '',
    city: item.cityname || '',
    district: item.adname || '',
    address: item.address || '',
    location: {
      lng: item.location.lng,
      lat: item.location.lat
    }
  };

  map.value.setCenter([item.location.lng, item.location.lat]);
  marker.value.setPosition([item.location.lng, item.location.lat]);

  // 更新 address 对象
  address.completeAddress = `${addressDetail.value.province}${addressDetail.value.city}${addressDetail.value.district}${addressDetail.value.address}`;
  address.longitude = item.location.lng;
  address.latitude = item.location.lat;

  searchResults.value = [];
};

// 地图点击事件
const handleMapClick = (e) => {
  const lngLat = e.lnglat;
  selectedLocation.value = {
    lng: lngLat.lng,
    lat: lngLat.lat
  };
  marker.value.setPosition([lngLat.lng, lngLat.lat]);
  updateAddress([lngLat.lng, lngLat.lat]);
};

// 标记点拖动结束事件
const handleMarkerDragend = (e) => {
  const lngLat = e.target.getPosition();
  selectedLocation.value = {
    lng: lngLat.lng,
    lat: lngLat.lat
  };
  updateAddress([lngLat.lng, lngLat.lat]);
};

// 更新地址
const updateAddress = (lngLat) => {
  // eslint-disable-next-line no-undef
  const geocoder = new AMap.Geocoder();
  geocoder.getAddress(lngLat, (status, result) => {
    if (status === 'complete' && result.info === 'OK') {
      const addressComponent = result.regeocode.addressComponent;
      const formattedAddress = result.regeocode.formattedAddress;

      // 更新地址详情
      addressDetail.value = {
        province: addressComponent.province || '',
        city: addressComponent.city || '',
        district: addressComponent.district || '',
        address: formattedAddress,
        location: {
          lng: lngLat[0],
          lat: lngLat[1]
        }
      };

      // 更新 address 对象
      address.completeAddress = formattedAddress;
      address.longitude = lngLat[0];
      address.latitude = lngLat[1];
    }
  });
};

// 监听弹窗关闭
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    // 弹窗关闭时，销毁地图实例
    if (map.value) {
      map.value.destroy();
      map.value = null;
      marker.value = null;
    }
  }
});

// 修改清空地址函数
const clearAddress = () => {
  address.completeAddress = '';
  address.longitude = '';
  address.latitude = '';
  addressDetail.value = {
    province: '',
    city: '',
    district: '',
    address: '',
    location: {
      lng: '',
      lat: ''
    }
  };
  selectedLocation.value = null;

  // 更新为对象格式
  emit('update:modelValue', {
    completeAddress: '',
    lng: '',
    lat: ''
  });

  emit('change', {
    address: '',
    addressDetail: addressDetail.value,
    location: null
  });
};

// 修改 confirmAddress 函数
const confirmAddress = () => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择一个地址');
    return;
  }

  // 更新为对象格式
  emit('update:modelValue', {
    completeAddress: address.completeAddress,
    lng: address.longitude,
    lat: address.latitude
  });

  emit('change', {
    address: address.completeAddress,
    addressDetail: addressDetail.value,
    location: selectedLocation.value
  });
  dialogVisible.value = false;
};

onMounted(() => {
  if (props.modelValue) {
    address.completeAddress = props.modelValue.completeAddress || '';
    address.longitude = props.modelValue.lng || '';
    address.latitude = props.modelValue.lat || '';
  }
});
</script>

<style scoped>
/* .address-detail {
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
} */

.address-form {
  max-width: 800px;
}

.address-form :deep(.el-form-item) {
  margin-bottom: 18px;
}

.address-form :deep(.el-form-item__label) {
  padding-right: 12px;
}

.address-input-wrapper {
  width: 100%;
  max-width: 600px;
}

.address-input {
  width: 100%;
  cursor: pointer;
}

.coordinate-input {
  width: 300px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.button-group .el-button {
  margin: 0;
}

:deep(.el-input__wrapper) {
  background-color: #f5f7fa;
}

:deep(.el-input.is-disabled .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

:deep(.el-dialog__body) {
  height: 500px;
}

.map-container {
  position: relative;
  height: 500px;
}

.search-box {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 300px;
  z-index: 999;
}

.map {
  width: 100%;
  height: 100%;
}

.search-result {
  position: absolute;
  top: 50px;
  left: 10px;
  width: 300px;
  max-height: 400px;
  overflow-y: auto;
  background: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.search-result ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.search-result li {
  padding: 10px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
}

.search-result li:hover {
  background-color: #f5f7fa;
}

.address-detail-text {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}
</style>
