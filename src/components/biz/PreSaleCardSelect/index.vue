<template>
  <el-select
    v-model="selectedCardId"
    placeholder="请选择订单号"
    filterable
    remote
    reserve-keyword
    :loading="cardLoading"
    :remote-method="remoteCardListMethod"
    @change="handleCardChange"
    @keyup.enter="handleCardChange"
    required
    :disabled="disabled"
    clearable
  >
    <el-option v-for="item in cardOptions" :key="item.id" :label="item.mainLable" :value="item.value">
      <el-tooltip content="订单号" placement="top">
        <span style="float: left; margin-right: 20px">{{ item.mainLable }}</span>
      </el-tooltip>
      <el-tooltip content="B端客户" placement="top">
        <span style="float: right; color: #999">{{ item.secondLable }}</span>
      </el-tooltip>
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
import { listPresaleOrder, getPresaleOrder } from '@/api/mall/presaleOrder/presaleOrder';
import { useRemoteListMoreMethod } from '@/hooks/useBusiness/useSelectMore/remote';
import { getCurrentInstance, computed, watch } from 'vue';
import type { ComponentInternalInstance } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 组件属性
const props = defineProps<{
  modelValue?: string | number;
  disabled?: boolean;
}>();

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string | number];
  'change': [value: string | number];
}>();

// 组件内部状态
const selectedCardId = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const {
  list: cardOptions,
  loading: cardLoading,
  remoteMethod: remoteCardListMethod
} = useRemoteListMoreMethod(proxy, {
  FetchUrl: listPresaleOrder,
  value: 'id',
  mainLable: 'orderNo',
  secondLable: 'customerName',
  desc: '给其演示年度方案的兑换逻辑',
  queryStr: 'orderNo',
  query: { pageNum: 1, pageSize: 10, orderNo: '' }
});

// 处理预购卡选择变化
const handleCardChange = (value: string | number) => {
  emit('change', value);
};

// 初始化默认值
const initDefaultValue = async (newValue) => {
  let currentItem = null;
  try {
    if (newValue) {
      const res = await getPresaleOrder(newValue);
      if (res.data) {
        currentItem = {
          value: res.data.id,
          mainLable: res.data.cardName,
          secondLable: res.data.cardNo,
          desc: res.data.remark
        };
      }
    } else {
      currentItem = null;
    }
    return currentItem;
  } catch (error) {
    console.error('Failed to load default presale card:', error);
  }
};

watch(
  () => props.modelValue,
  async (newValue) => {
    const currentItem = await initDefaultValue(newValue);
    await remoteCardListMethod('', '', '');
    if (currentItem !== null) {
      const isExist = cardOptions.value.find((item) => item.value === currentItem.value);
      if (!isExist) {
        cardOptions.value = [currentItem, ...cardOptions.value];
      }
    }
  },
  { immediate: true }
);

// 暴露方法给父组件
defineExpose({
  remoteCardListMethod
});
</script>
