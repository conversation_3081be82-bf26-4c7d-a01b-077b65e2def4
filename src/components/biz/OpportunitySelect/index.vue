<template>
  <el-select
    v-model="selectedOpportunityId"
    placeholder="请选择销售机会"
    filterable
    remote
    reserve-keyword
    :loading="opportunityLoading"
    :remote-method="remoteOpportunityListMethod"
    @change="handleOpportunityChange"
    @keyup.enter="handleOpportunityChange"
    required
    :disabled="disabled"
  >
    <el-option v-for="item in opportunityOptions" :key="item.id" :label="item.mainLable" :value="item.value">
      <el-tooltip content="机会名称" placement="top">
        <span style="float: left; margin-right: 20px">{{ item.mainLable }}</span>
      </el-tooltip>
      <el-tooltip content="客户名称" placement="top">
        <span style="float: right; color: #999">{{ item.secondLable }}</span>
      </el-tooltip>
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
// 获取公海列表作为option内容
import { listOpportunity, getOpportunity } from '@/api/crm/opportunity/index';
import { useRemoteListMoreMethod } from '@/hooks/useBusiness/useSelectMore';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 组件属性
const props = defineProps<{
  modelValue?: string | number; // v-model 绑定值
  disabled?: boolean; // 添加 disabled 属性
}>();

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string | number];
  'change': [value: string | number];
}>();

// 组件内部状态
const selectedOpportunityId = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const {
  list: opportunityOptions,
  loading: opportunityLoading,
  remoteMethod: remoteOpportunityListMethod
} = useRemoteListMoreMethod(proxy, {
  FetchUrl: listOpportunity,
  value: 'id',
  mainLable: 'name',
  secondLable: 'customerName',
  desc: 'remark',
  queryStr: 'name',
  query: { pageNum: 1, pageSize: 10, name: '' }
});

// 处理公海选择变化
const handleOpportunityChange = (value: string | number) => {
  emit('change', value);
};

// 初始化默认值
const initDefaultValue = async () => {
  try {
    // 只在有 modelValue 时获取公海信息
    if (props.modelValue) {
      const res = await getOpportunity(props.modelValue);
      if (res.data) {
        // 立即设置选项，确保有值可以显示
        opportunityOptions.value = [
          {
            value: res.data.id,
            mainLable: res.data.name,
            secondLable: res.data.customerName,
            desc: res.data.remark
          }
        ];
      }
    }
  } catch (error) {
    console.error('Failed to load default customer:', error);
  }
};

// 组件挂载时初始化
onMounted(async () => {
  await initDefaultValue();
  // 加载列表数据，但不覆盖当前选中项
  const currentValue = opportunityOptions.value[0];
  await remoteOpportunityListMethod('', '', '');
  // 确保当前选中项在列表最前面
  if (currentValue) {
    opportunityOptions.value = [currentValue, ...opportunityOptions.value.filter((item) => item.value !== currentValue.value)];
  }
});

// 暴露方法给父组件
defineExpose({
  remoteOpportunityListMethod
});
</script>
