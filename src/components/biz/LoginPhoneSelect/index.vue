<template>
  <el-select
    v-model="selectedOrgId"
    placeholder="请选择登录手机"
    filterable
    remote
    reserve-keyword
    :loading="baseUserLoading"
    :remote-method="remoteBaseUserListMethod"
    @change="handleOrgChange"
    @keyup.enter="handleOrgChange"
    required
    :disabled="disabled"
  >
    <el-option v-for="item in baseUserOptions" :key="item.id" :label="item.label" :value="item.value" />

    <!-- 添加底部自定义内容 -->
    <template #footer v-if="isFooterVisible">
      <div class="select-footer" v-has-permi="['org:baseUser:add']">
        <el-button type="primary" link @click="handleAdd">
          <el-tooltip content="新增登录手机" placement="top">
            <el-icon class="mr-1"><Plus /></el-icon>
          </el-tooltip>
        </el-button>
        <el-button type="primary" link @click="refresh">
          <el-tooltip content="刷新列表" placement="top">
            <el-icon class="mr-1"><RefreshRight /></el-icon>
          </el-tooltip>
        </el-button>
      </div>
    </template>
  </el-select>

  <el-dialog v-model="changeLoginPhoneDialogVisible" title="更换登录手机" width="400px" append-to-body>
    <el-form ref="changeLoginPhoneFormRef" :model="changeLoginPhoneForm" :rules="changeLoginPhoneFormRules" label-width="120px" label-position="top">
      <el-form-item label="准备更换的新登录手机" prop="loginPhone">
        <el-input v-model="changeLoginPhoneForm.loginPhone" placeholder="请填写正确的11位手机号" />
      </el-form-item>
      <el-form-item label="用户名称">
        <el-input v-model="changeLoginPhoneForm.userName" placeholder="请输入用户名称" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input type="textarea" v-model="changeLoginPhoneForm.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitChangeLoginPhone">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 组件属性
const props = defineProps<{
  modelValue?: string | number; // v-model 绑定值
  disabled?: boolean; // 添加 disabled 属性
  isFooterVisible?: boolean; // 控制底部显示
}>();

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string | number];
  'change': [value: string | number];
  'add': [];
}>();

// 组件内部状态
const selectedOrgId = computed({
  get: () => props.modelValue, // 移除默认值
  set: (value) => emit('update:modelValue', value)
});

// 获取组织列表作为option内容
import { listBaseUser, getBaseUser } from '@/api/org/baseUser';
import { useRemoteListMethod } from '@/hooks/useBusiness/useSelect';

const {
  list: baseUserOptions,
  loading: baseUserLoading,
  remoteMethod: remoteBaseUserListMethod
} = useRemoteListMethod(proxy, {
  FetchUrl: listBaseUser,
  value: 'id',
  label: 'loginPhone',
  queryStr: 'loginPhone',
  query: { pageNum: 1, pageSize: 10, name: '', isBlacklist: '0' }
});

// 处理组织选择变化
const handleOrgChange = (value: string | number) => {
  emit('change', value);
};

// 初始化默认值
const initDefaultValue = async () => {
  try {
    // 只在有 modelValue 时获取组织信息
    if (props.modelValue) {
      const res = await getBaseUser(props.modelValue);
      if (res.data) {
        // 立即设置选项，确保有值可以显示
        baseUserOptions.value = [
          {
            id: res.data.id,
            value: res.data.id,
            label: res.data.loginPhone
          }
        ];
      }
    }
  } catch (error) {
    console.error('Failed to load default org:', error);
  }
};

// 修改刷新方法
const refresh = async () => {
  baseUserLoading.value = true;
  try {
    // 先清空当前列表
    baseUserOptions.value = [];

    // 获取当前选中值的数据
    const currentValue = selectedOrgId.value;

    // 重新获取列表数据
    const res = await listBaseUser({
      pageNum: 1,
      pageSize: 10,
      isBlacklist: '0'
    });

    if (res.rows) {
      // 转换数据格式
      const options = res.rows.map((item) => ({
        id: item.id,
        value: item.id,
        label: item.loginPhone
      }));

      // 如果有选中值，确保它在列表最前面
      if (currentValue) {
        const selectedItem = options.find((item) => item.value === currentValue);
        if (selectedItem) {
          baseUserOptions.value = [selectedItem, ...options.filter((item) => item.value !== currentValue)];
        } else {
          baseUserOptions.value = options;
        }
      } else {
        baseUserOptions.value = options;
      }

      ElMessage.success('刷新成功');
    }
  } catch (error) {
    console.error('Refresh failed:', error);
    ElMessage.error('刷新失败');
  } finally {
    baseUserLoading.value = false;
  }
};

// 修改组件挂载时的初始化
onMounted(async () => {
  await initDefaultValue();
  refresh(); // 使用新的refresh方法加载初始数据
});

// 暴露方法给父组件
defineExpose({
  remoteBaseUserListMethod
});

// 新建用户
import { getBaseUserOrAdd } from '@/api/org/baseUser';
import { BaseUserForm, BaseUserQuery } from '@/api/org/baseUser/types';
const changeLoginPhoneDialogVisible = ref(false);
const changeLoginPhoneFormRef = ref<ElFormInstance>();
const buttonLoading = ref(false);
const initChangeLoginPhoneForm: BaseUserForm = {
  id: undefined,
  loginPhone: undefined,
  userName: undefined,
  isBlacklist: '0',
  remark: undefined
};

const dataChangeLoginPhone = reactive<PageData<BaseUserForm, BaseUserQuery>>({
  form: initChangeLoginPhoneForm,
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    params: {}
  },
  rules: {
    loginPhone: [{ required: true, message: '请输入登录手机号', trigger: 'blur' }]
  }
});

const { form: changeLoginPhoneForm, rules: changeLoginPhoneFormRules } = toRefs(dataChangeLoginPhone);

const cancel = () => {
  changeLoginPhoneDialogVisible.value = false;
  buttonLoading.value = false;
};

const resetForm = () => {
  changeLoginPhoneForm.value = {
    ...initChangeLoginPhoneForm
  };
};

// 新增方法
const handleAdd = () => {
  resetForm();
  changeLoginPhoneDialogVisible.value = true;
};

const submitChangeLoginPhone = () => {
  buttonLoading.value = true;
  changeLoginPhoneFormRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        const res = await getBaseUserOrAdd(changeLoginPhoneForm.value);
        if (res.data) {
          // 关闭弹窗和重置表单
          changeLoginPhoneDialogVisible.value = false;
          resetForm();
          ElMessage.success('新增成功');

          // 刷新下拉选项并选中新增的选项
          await remoteBaseUserListMethod('');
          if (res.data.id) {
            // 更新选中值
            selectedOrgId.value = res.data.id;
            // 确保新增的选项在列表中
            baseUserOptions.value = [
              {
                id: res.data.id,
                value: res.data.id,
                label: res.data.loginPhone
              },
              ...baseUserOptions.value.filter((item) => item.id !== res.data.id)
            ];
            // 触发change事件
            emit('change', res.data.id);
          }
        }
      } catch (error) {
        console.error('Failed to add phone:', error);
        ElMessage.error('新增失败');
      } finally {
        buttonLoading.value = false;
      }
    } else {
      buttonLoading.value = false;
    }
  });
};
</script>
