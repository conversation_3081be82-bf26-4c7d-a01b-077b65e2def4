<template>
  <el-select
    v-model="selectedOrgId"
    placeholder="请选择组织"
    filterable
    remote
    reserve-keyword
    :loading="orgLoading"
    :remote-method="remoteOrgListMethod"
    @change="handleOrgChange"
    @keyup.enter="handleOrgChange"
    required
    :disabled="disabled"
  >
    <el-option v-for="item in orgOptions" :key="item.id" :label="item.label" :value="item.value" />
  </el-select>
</template>

<script setup lang="ts">
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 组件属性
const props = defineProps<{
  modelValue?: string | number; // v-model 绑定值
  disabled?: boolean; // 添加 disabled 属性
}>();

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string | number];
  'change': [value: string | number];
}>();

// 组件内部状态
const selectedOrgId = computed({
  get: () => props.modelValue, // 移除默认值
  set: (value) => emit('update:modelValue', value)
});

// 获取组织列表作为option内容
import { listOrg, getOrgById } from '@/api/org/org';
import { useRemoteListMethod } from '@/hooks/useBusiness/useSelect';
const {
  list: orgOptions,
  loading: orgLoading,
  remoteMethod: remoteOrgListMethod
} = useRemoteListMethod(proxy, {
  FetchUrl: listOrg,
  value: 'id',
  label: 'orgName',
  queryStr: 'orgName',
  query: { pageNum: 1, pageSize: 10, name: '', status: '0' }
});

// 处理组织选择变化
const handleOrgChange = (value: string | number) => {
  emit('change', value);
};

// 初始化默认值
const initDefaultValue = async () => {
  try {
    // 只在有 modelValue 时获取组织信息
    if (props.modelValue) {
      const res = await getOrgById(props.modelValue);
      if (res.data) {
        // 立即设置选项，确保有值可以显示
        orgOptions.value = [
          {
            id: res.data.id,
            value: res.data.id,
            label: res.data.orgName
          }
        ];
      }
    }
  } catch (error) {
    console.error('Failed to load default org:', error);
  }
};

// 组件挂载时初始化
onMounted(async () => {
  await initDefaultValue();
  // 加载列表数据，但不覆盖当前选中项
  const currentValue = orgOptions.value[0];
  await remoteOrgListMethod('');
  // 确保当前选中项在列表最前面
  if (currentValue) {
    orgOptions.value = [currentValue, ...orgOptions.value.filter((item) => item.value !== currentValue.value)];
  }
});

// 暴露方法给父组件
defineExpose({
  remoteOrgListMethod
});
</script>
