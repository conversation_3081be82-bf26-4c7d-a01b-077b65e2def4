<template>
  <el-tree-select
    v-model="selectedCategoryId"
    :data="backCategoryOptions"
    :props="{ value: 'id', label: 'name', children: 'children' }"
    node-key="id"
    placeholder="请选择后台分类"
    filterable
    clearable
    @change="handleChange"
  />
</template>

<script setup lang="ts">
// 获取后台分类列表
import { listBackCategory } from '@/api/mall/backCategory';
import { BackCategoryVO, BackCategoryQuery, BackCategoryForm } from '@/api/mall/backCategory/types';

// 组件属性
const props = defineProps<{
  modelValue: string | number;
  disabled?: boolean;
}>();

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string | number];
  'change': [value: string | number];
}>();

// 组件内部状态
const selectedCategoryId = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 监听后台分类选择
const handleChange = (value: string | number) => {
  emit('change', value);
};

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

type BackCategoryOption = {
  id: number;
  name: string;
  children?: BackCategoryOption[];
};

const backCategoryOptions = ref<BackCategoryOption[]>([]);
const query = ref<BackCategoryQuery>({
  status: '0'
});

/** 查看后台分类下拉树结构 */
const getTreeselect = async () => {
  const res = await listBackCategory(query.value);
  backCategoryOptions.value = [];
  backCategoryOptions.value = proxy?.handleTree<BackCategoryOption>(res.data, 'id', 'parentId');
};

onMounted(() => {
  getTreeselect();
});
</script>
