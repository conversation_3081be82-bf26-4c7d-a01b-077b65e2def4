<template>
  <el-select
    v-model="selectedHighSeaId"
    placeholder="请选择公海"
    filterable
    remote
    reserve-keyword
    :loading="highSeaLoading"
    :remote-method="remoteHighSeaListMethod"
    @change="handleHighSeaChange"
    @keyup.enter="handleHighSeaChange"
    required
    :disabled="disabled"
  >
    <el-option v-for="item in highSeaOptions" :key="item.id" :label="item.label" :value="item.value"> </el-option>
  </el-select>
</template>

<script setup lang="ts">
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 组件属性
const props = defineProps<{
  modelValue?: string | number; // v-model 绑定值
  disabled?: boolean; // 添加 disabled 属性
}>();

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string | number];
  'change': [value: string | number];
}>();

// 组件内部状态
const selectedHighSeaId = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 获取公海列表作为option内容
import { listHighSea, getHighSea } from '@/api/crm/highSea';
import { useRemoteListMethod } from '@/hooks/useBusiness/useSelect';

const {
  list: highSeaOptions,
  loading: highSeaLoading,
  remoteMethod: remoteHighSeaListMethod
} = useRemoteListMethod(proxy, {
  FetchUrl: listHighSea,
  value: 'id',
  label: 'seaName',
  queryStr: 'seaName',
  query: { pageNum: 1, pageSize: 10, seaName: '', status: '0' }
});

// 处理公海选择变化
const handleHighSeaChange = (value: string | number) => {
  emit('change', value);
};

// 初始化默认值
const initDefaultValue = async () => {
  try {
    // 只在有 modelValue 时获取公海信息
    if (props.modelValue) {
      const res = await getHighSea(props.modelValue);
      if (res.data) {
        // 立即设置选项，确保有值可以显示
        highSeaOptions.value = [
          {
            id: res.data.id,
            value: res.data.id,
            label: res.data.seaName
          }
        ];
      }
    }
  } catch (error) {
    console.error('Failed to load default highSea:', error);
  }
};

// 组件挂载时初始化
onMounted(async () => {
  await initDefaultValue();
  // 加载列表数据，但不覆盖当前选中项
  const currentValue = highSeaOptions.value[0];
  await remoteHighSeaListMethod('');
  // 确保当前选中项在列表最前面
  if (currentValue) {
    highSeaOptions.value = [currentValue, ...highSeaOptions.value.filter((item) => item.value !== currentValue.value)];
  }
});

// 暴露方法给父组件
defineExpose({
  remoteHighSeaListMethod
});
</script>
