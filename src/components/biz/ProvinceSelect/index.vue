<template>
  <el-select
    v-model="selectedProvince"
    placeholder="请选择省份"
    filterable
    remote
    reserve-keyword
    :loading="loading"
    :remote-method="remoteProvinceListMethod"
    @change="handleProvinceChange"
    @keyup.enter="handleProvinceChange"
    required
    :disabled="disabled"
    clearable
  >
    <el-option v-for="item in provinceOptions" :key="item.id" :label="item.mainLable" :value="item.value">
      <el-tooltip content="省份名称" placement="top">
        <span>{{ item.mainLable }}</span>
      </el-tooltip>
      <el-tooltip content="省份编码" placement="top">
        <span style="float: right; color: #999">{{ item.secondLable }}</span>
      </el-tooltip>
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
// 获取省份列表作为option内容
import { listChinaProvince, getChinaProvince } from '@/api/setting/chinaProvince';
import { useRemoteListMoreMethod } from '@/hooks/useBusiness/useSelectMore/remote';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 组件属性
const props = defineProps<{
  modelValue?: string | number; // v-model 绑定值
  disabled?: boolean; // 添加 disabled 属性
}>();

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string | number];
  'change': [value: string | number];
}>();

// 组件内部状态
const selectedProvince = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const {
  list: provinceOptions,
  loading: loading,
  remoteMethod: remoteProvinceListMethod
} = useRemoteListMoreMethod(proxy, {
  FetchUrl: listChinaProvince,
  value: 'code',
  mainLable: 'name',
  secondLable: 'code',
  desc: 'remark',
  queryStr: 'name',
  query: { pageNum: 1, pageSize: 10, name: '' }
});

// 处理省份选择变化
const handleProvinceChange = (value: string | number) => {
  emit('change', value);
};

// 初始化默认值
const initDefaultValue = async (newValue) => {
  let currentItem = null;
  try {
    if (newValue) {
      const res = await getChinaProvince(newValue);
      if (res.data) {
        currentItem = {
          id: res.data.id,
          value: res.data.id,
          mainLable: res.data.name,
          secondLable: res.data.code,
          desc: res.data.remark
        };
      }
    } else {
      currentItem = null;
    }
    return currentItem;
  } catch (error) {
    console.error('Failed to load default province:', error);
    return null;
  }
};

watch(
  () => props.modelValue,
  async (newValue) => {
    const currentItem = await initDefaultValue(newValue);
    // 加载列表数据
    await remoteProvinceListMethod('', '', '');
    if (currentItem !== null) {
      const isExist = provinceOptions.value.find((item) => item.value === currentItem.value);
      if (!isExist) {
        provinceOptions.value = [currentItem, ...provinceOptions.value];
      }
    }
  },
  { immediate: true }
);

// 暴露方法给父组件
defineExpose({
  remoteProvinceListMethod
});
</script>
