<template>
  <el-select
    v-model="batchNumber"
    placeholder="请选择批次号"
    filterable
    clearable
    reserve-keyword
    remote
    :remote-method="remoteRechargeBatchListMethod"
    :loading="rechargeBatchLoading"
    @change="handleRechargeBatchChange"
    @keyup.enter="handleRechargeBatchChange"
    required
    :disabled="disabled"
  >
    <el-option v-for="item in rechargeBatchOptions" :key="item.id" :label="item.mainLable" :value="item.mainLable">
      <el-tooltip content="批次号" placement="top">
        <span style="float: left; margin-right: 20px">{{ item.mainLable }}</span>
      </el-tooltip>
      <el-tooltip content="可售数量" placement="top">
        <span style="float: right; color: #999">可售 {{ item.secondLable }} 张</span>
      </el-tooltip>
    </el-option>
  </el-select>
</template>

<script setup name="RechargeBatchSelect" lang="ts">
import { getRechargeBatchByBatchNumber, listRechargeBatchExcludeZero } from '@/api/recharge/rechargeBatch';
import { useRemoteListMoreMethod } from '@/hooks/useBusiness/useSelectMore/remote';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const props = defineProps<{
  modelValue?: string | number;
  disabled?: boolean;
  orderType?: string;
}>();

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string | number];
  'change': [value: string];
}>();

// 组件内部状态
const batchNumber = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const {
  list: rechargeBatchOptions,
  loading: rechargeBatchLoading,
  remoteMethod: remoteRechargeBatchListMethod
} = useRemoteListMoreMethod(proxy, {
  FetchUrl: listRechargeBatchExcludeZero,
  value: 'id',
  mainLable: 'batchNumber',
  secondLable: 'availableSaleQty',
  desc: 'batchName',
  queryStr: 'batchNumber',
  query: { pageNum: 1, pageSize: 20, batchNumber: '', batchStatus: '51' }
});

// 处理批次号选择变化
const handleRechargeBatchChange = (value: string) => {
  emit('change', value);
};

// 初始化默认值
const initDefaultValue = async (newValue) => {
  let currentItem = null;
  try {
    // 只在有 modelValue 时获取批次号信息
    if (newValue) {
      const res = await getRechargeBatchByBatchNumber(newValue);
      if (res.data) {
        currentItem = {
          value: res.data.id,
          mainLable: res.data.batchNumber,
          secondLable: res.data.availableSaleQty,
          desc: res.data.batchName
        };
      }
    } else {
      currentItem = null;
    }
    return currentItem;
  } catch (error) {
    console.error('Failed to load default batch number:', error);
  }
};

watch(
  () => props.modelValue,
  async (newValue) => {
    const currentItem = await initDefaultValue(newValue);
    // 加载列表数据
    await remoteRechargeBatchListMethod('', '', '');
    if (currentItem !== null) {
      const isExist = rechargeBatchOptions.value.find((item) => item.value === currentItem.value);
      if (!isExist) {
        rechargeBatchOptions.value = [currentItem, ...rechargeBatchOptions.value];
      }
    }
  },
  { immediate: true }
);

// 暴露方法给父组件
defineExpose({
  remoteRechargeBatchListMethod
});
</script>
