<template>
  <el-select
    v-model="selectedCustomerId"
    placeholder="请选择客户"
    filterable
    remote
    reserve-keyword
    :loading="customerLoading"
    :remote-method="remoteCustomerListMethod"
    @change="handleCustomerChange"
    @keyup.enter="handleCustomerChange"
    required
    :disabled="disabled"
    clearable
  >
    <el-option v-for="item in customerOptions" :key="item.id" :label="item.mainLable" :value="item.value">
      <el-tooltip content="客户名称" placement="top">
        <span style="float: left; margin-right: 20px">{{ item.mainLable }}</span>
      </el-tooltip>
      <el-tooltip content="承做人（A角）" placement="top">
        <span style="float: right; color: #999">{{ item.secondLable }}</span>
      </el-tooltip>
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
// 获取客户列表作为option内容
import { listCustomer, getCustomer } from '@/api/crm/customer/index';
import { useRemoteListMoreMethod } from '@/hooks/useBusiness/useSelectMore/remote';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 组件属性
const props = defineProps<{
  modelValue?: string | number; // v-model 绑定值
  disabled?: boolean; // 添加 disabled 属性
}>();

// 组件事件
const emit = defineEmits<{
  'update:modelValue': [value: string | number];
  'change': [value: string | number];
}>();

// 组件内部状态
const selectedCustomerId = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const {
  list: customerOptions,
  loading: customerLoading,
  remoteMethod: remoteCustomerListMethod
} = useRemoteListMoreMethod(proxy, {
  FetchUrl: listCustomer,
  value: 'id',
  mainLable: 'name',
  secondLable: 'ownerNickName',
  desc: 'remark',
  queryStr: 'name',
  query: { pageNum: 1, pageSize: 10, name: '' }
});

// 处理客户选择变化
const handleCustomerChange = (value: string | number) => {
  emit('change', value);
};

// 初始化默认值 http://localhost/crm/bizMg/opportunity?customerId=1886728300431241218&openAdd=true
const initDefaultValue = async (newValue) => {
  let currentItem = null;
  try {
    // 只在有 modelValue 时获取客户信息
    if (newValue) {
      const res = await getCustomer(newValue);
      if (res.data) {
        // 立即设置选项，确保有值可以显示
        currentItem = {
          value: res.data.id,
          mainLable: res.data.name,
          secondLable: res.data.ownerNickName,
          desc: res.data.remark
        };
      }
    } else {
      currentItem = null;
    }
    return currentItem;
  } catch (error) {
    console.error('Failed to load default customer:', error);
  }
};

watch(
  () => props.modelValue,
  async (newValue) => {
    const currentItem = await initDefaultValue(newValue);
    // 加载列表数据
    await remoteCustomerListMethod('', '', '');
    if (currentItem !== null) {
      const isExist = customerOptions.value.find((item) => item.value === currentItem.value);
      if (!isExist) {
        customerOptions.value = [currentItem, ...customerOptions.value];
      }
    }
  },
  { immediate: true }
);

// 暴露方法给父组件
defineExpose({
  remoteCustomerListMethod
});
</script>
