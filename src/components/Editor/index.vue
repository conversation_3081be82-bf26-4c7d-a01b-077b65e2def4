<template>
  <div class="editor-container">
    <Toolbar :editor="editorRef" :defaultConfig="toolbarConfig" :mode="mode" style="border-bottom: 1px solid #ccc" />
    <Editor
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      :mode="mode"
      @onCreated="handleCreated"
      style="overflow-y: hidden"
      :style="{ height: `${props.height}px`, 'min-height': `${props.minHeight}px` }"
    />
  </div>
</template>

<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { IDomEditor, IToolbarConfig } from '@wangeditor/editor';
import { globalHeaders } from '@/utils/request';

const props = defineProps({
  /* 编辑器的内容 */
  modelValue: {
    type: String,
    default: ''
  },
  /* 高度 */
  height: {
    type: Number,
    default: 400
  },
  /* 最小高度 */
  minHeight: {
    type: Number,
    default: 400
  },
  /* 只读 */
  readOnly: {
    type: Boolean,
    default: false
  },
  /* 上传文件大小限制(MB) */
  fileSize: {
    type: Number,
    default: 5
  }
});

const emit = defineEmits(['update:modelValue']);
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef<IDomEditor>();

// 内容 HTML
const valueHtml = ref('');

// 模式
const mode = computed(() => (props.readOnly ? 'preview' : 'default'));

// 编辑器配置
const editorConfig = {
  placeholder: '请输入内容...',
  MENU_CONF: {
    uploadImage: {
      server: import.meta.env.VITE_APP_BASE_API + '/resource/oss/upload', // 上传地址
      fieldName: 'file', // 确保这个字段名与后端API要求的一致
      headers: {
        ...globalHeaders(),
        'Content-Type': 'multipart/form-data'
      },
      // 自定义上传参数，确保文件参数正确传递
      customUpload(file: File, insertFn: any) {
        const formData = new FormData();
        formData.append('file', file); // 确保使用正确的字段名

        // 使用自定义上传逻辑
        proxy?.$modal.loading('正在上传图片，请稍候...');

        // 这里可以使用项目中的请求工具发送请求
        fetch(import.meta.env.VITE_APP_BASE_API + '/resource/oss/upload', {
          method: 'POST',
          headers: {
            ...globalHeaders()
          },
          body: formData
        })
          .then((res) => res.json())
          .then((res) => {
            proxy?.$modal.closeLoading();
            if (res.code === 200) {
              insertFn(res.data.url);
            } else {
              proxy?.$modal.msgError('图片上传失败');
            }
          })
          .catch(() => {
            proxy?.$modal.closeLoading();
            proxy?.$modal.msgError('图片上传失败');
          });
      },
      maxFileSize: props.fileSize * 1024 * 1024,
      allowedFileTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
      base64LimitSize: 5 * 1024, // 5kb
      allowPasteImage: true, // 允许粘贴图片
      customPasteImage(file: File, insertFn: any) {
        // 自定义粘贴图片的上传
        uploadImageFile(file, insertFn);
      },
      customDropImage(file: File, insertFn: any) {
        // 自定义拖拽上传
        uploadImageFile(file, insertFn);
      }
    }
  }
};

// 工具栏配置
const toolbarConfig = {
  excludeKeys: ['group-video', 'fullScreen', 'fontSize', 'fontFamily', 'headerSelect', 'insertTable', 'codeView']
} as IToolbarConfig;

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});

// 编辑器创建完成时的回调
const handleCreated = (editor: IDomEditor) => {
  editorRef.value = editor;
};

// 监听 value 变化
watch(
  () => props.modelValue,
  (val: string) => {
    valueHtml.value = val;
  },
  { immediate: true }
);

// 监听编辑器内容变化
watch(
  () => valueHtml.value,
  (val: string) => {
    emit('update:modelValue', val);
  }
);

// 添加统一的上传处理函数
const uploadImageFile = (file: File, insertFn: any) => {
  const formData = new FormData();
  formData.append('file', file);

  proxy?.$modal.loading('正在上传图片，请稍候...');

  fetch(import.meta.env.VITE_APP_BASE_API + '/resource/oss/upload', {
    method: 'POST',
    headers: {
      ...globalHeaders()
    },
    body: formData
  })
    .then((res) => res.json())
    .then((res) => {
      proxy?.$modal.closeLoading();
      if (res.code === 200) {
        insertFn(res.data.url);
      } else {
        proxy?.$modal.msgError('图片上传失败');
      }
    })
    .catch(() => {
      proxy?.$modal.closeLoading();
      proxy?.$modal.msgError('图片上传失败');
    });
};
</script>

<style lang="scss" scoped>
.editor-container {
  z-index: 0;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;

  :deep(.w-e-toolbar) {
    border: none !important;
    border-bottom: 1px solid #dcdfe6 !important;
    background-color: #fafafa;
    margin-left: -1px;
    margin-right: -1px;
  }

  :deep(.w-e-text-container) {
    border: none !important;
    border-bottom: 1px solid #dcdfe6 !important;
    margin-left: -1px;
    margin-right: -1px;

    .w-e-text-placeholder {
      color: #c0c4cc;
    }
  }

  :deep(.w-e-bar-item) {
    &:hover {
      background-color: #ecf5ff !important;
    }
  }

  :deep(.w-e-bar-item-active) {
    background-color: #ecf5ff !important;
  }
}
</style>
