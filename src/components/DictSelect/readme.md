# DictSelect 字典选择器组件

一个基于 Element Plus 的下拉选择组件,集成了字典数据的加载、刷新和维护功能。

## 功能特性

- 支持字典数据的自动加载
- 支持字典数据的实时刷新
- 支持快速跳转到字典维护页面
- 完全继承 el-select 的所有属性和事件
- 支持 v-model 双向绑定
- 支持单选和多选模式
- 可控制底部工具栏的显示/隐藏

## 基础用法

<template>
  <!-- 基础用法 -->
  <dict-select
    v-model="form.type"
    dict-key="dict_markting_type"
    placeholder="请选择类型"
    style="width: '100%'"
  />

  <!-- 多选模式 -->

<dict-select
    v-model="form.tags"
    dict-key="dict_crm_tag"
    placeholder="请选择标签"
    multiple
  />

  <!-- 隐藏底部工具栏 -->

<dict-select
    v-model="form.type"
    dict-key="dict_markting_type"
    :show-footer="false"
    style="width: '100%'"
  />

  <!-- 带清除功能 -->

<dict-select
v-model="form.type"
dict-key="dict_markting_type"
clearable
@change="handleChange"
/>
</template>

<script setup lang="ts">
import DictSelect from '@/components/DictSelect/index.vue';
</script>

## API

### Props

| 参数                 | 说明               | 类型            | 默认值 |
| -------------------- | ------------------ | --------------- | ------ |
| modelValue / v-model | 绑定值             | string / number | -      |
| dictKey              | 字典类型键值(必填) | string          | -      |
| showFooter           | 是否显示底部工具栏 | boolean         | true   |
| multiple             | 是否多选           | boolean         | false  |

### Events

| 事件名            | 说明                  | 回调参数     |
| ----------------- | --------------------- | ------------ |
| change            | 选中值发生变化时触发  | 目前的选中值 |
| update:modelValue | 更新 v-model 值时触发 | 新的值       |

### Slots

| 插槽名 | 说明                                         |
| ------ | -------------------------------------------- |
| -      | el-select 的默认插槽                         |
| footer | 底部工具栏区域(当 showFooter 为 true 时可用) |

### 继承属性

该组件继承了 Element Plus 的 el-select 组件的所有属性和事件,比如:

- placeholder
- clearable
- disabled
- size
- etc...

## 多选值格式说明

当使用多选模式时：

- 组件内部会自动处理数组与字符串的转换
- 存储到数据库时使用英文逗号(,)分隔的字符串
- 在表单中使用时会自动转换为数组以支持多选功能

## 使用场景

1. 搜索表单中使用(建议隐藏底部工具栏):

```vue
<dict-select v-model="queryParams.type" dict-key="dict_markting_type" :show-footer="false" clearable @change="handleQuery" />
```

2. 编辑表单中使用(显示底部工具栏):

```vue
<dict-select v-model="form.type" dict-key="dict_markting_type" style="width: '100%'" />
```

3. 多选标签场景：

```vue
<dict-select v-model="form.tags" dict-key="dict_crm_tag" placeholder="请选择标签" multiple />
```

## 注意事项

1. 必须提供 dictKey 属性,且对应的字典数据必须存在
2. 组件会在挂载时自动加载字典数据
3. 点击设置图标会在新窗口打开字典维护页面
4. 点击刷新图标会重新加载字典数据
5. 组件宽度默认为 100%,可通过 style 或 class 自定义
6. 多选模式下，v-model 绑定值在组件内部会自动处理为数组，但最终存储时会转换为逗号分隔的字符串

## 使用场景

1. 搜索表单中使用(建议隐藏底部工具栏):

```vue
<dict-select v-model="queryParams.type" dict-key="dict_markting_type" :show-footer="false" clearable @change="handleQuery" />
```

2. 编辑表单中使用(显示底部工具栏):

```vue
<dict-select v-model="form.type" dict-key="dict_markting_type" style="width: '100%'" />
```

3. 多选标签场景：

```vue
<dict-select v-model="form.tags" dict-key="dict_crm_tag" placeholder="请选择标签" multiple />
```

## 依赖说明

- Element Plus
- Vue 3
- 需要后端提供字典相关的 API 接口
