<template>
  <el-select
    ref="selectRef"
    v-model="selectValue"
    v-bind="$attrs"
    @change="handleChange"
    :multiple="multiple"
    filterable
    clearable
    collapse-tags
    collapse-tags-tooltip
    :multiple-limit="5"
  >
    <el-option v-for="dict in dictOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
    <template #footer v-if="showFooter">
      <div class="dict-select-footer">
        <el-tooltip content="前往字典维护页面" placement="top">
          <el-icon class="dict-icon" @click.stop="handleGoToDict">
            <Setting />
          </el-icon>
        </el-tooltip>
        <el-tooltip content="刷新字典" placement="top">
          <el-icon class="dict-icon" @click.stop="handleRefreshDict">
            <Refresh />
          </el-icon>
        </el-tooltip>
      </div>
    </template>
  </el-select>
</template>

<script setup lang="ts">
import { Setting, Refresh } from '@element-plus/icons-vue';
import { getInfoByDictType } from '@/api/system/dict/type';
import { getDicts } from '@/api/system/dict/data';

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  dictKey: {
    type: String,
    required: true
  },
  showFooter: {
    type: Boolean,
    default: true
  },
  multiple: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

// 使用计算属性处理双向绑定
const selectValue = computed({
  get: () => {
    if (props.multiple && typeof props.modelValue === 'string') {
      return props.modelValue ? props.modelValue.split(',') : [];
    }
    return props.modelValue;
  },
  set: (value) => {
    let emitValue = value;
    if (props.multiple && Array.isArray(value)) {
      emitValue = value.join(',');
    }
    emit('update:modelValue', emitValue);
    emit('change', emitValue);
  }
});

const dictOptions = ref([]);

// 加载字典数据
const loadDictData = async () => {
  const { data } = await getDicts(props.dictKey);
  if (data) {
    dictOptions.value = data.map((item) => ({
      label: item.dictLabel,
      value: item.dictValue
    }));
  }
};

// 前往字典维护页面
const handleGoToDict = async () => {
  const { data } = await getInfoByDictType(props.dictKey);
  if (data?.dictId) {
    window.open(`/system/dict-data/index/${data.dictId}`);
  } else {
    ElMessage.warning('未找到对应的字典信息');
  }
};

// 刷新字典数据
const handleRefreshDict = async () => {
  await loadDictData();
  ElMessage.success('刷新成功');
};

// 值变化事件
const handleChange = (value) => {
  emit('change', value);
};

// 初始加载
onMounted(() => {
  loadDictData();
});

// 获取select元素引用
const selectRef = ref();
</script>

<style scoped>
.dict-select-footer {
  display: flex;
  align-items: center;
  justify-content: left;
}

.dict-icon {
  font-size: 16px;
  cursor: pointer;
  color: var(--el-text-color-secondary);
  margin-left: 8px;
}

.dict-icon:hover {
  color: var(--el-color-primary);
}
</style>
