<template>
  <div ref="chartRef" :style="{ width: '100%', height: '100%' }"></div>
</template>

<script setup>
import { ref, onMounted, watch, onUnmounted } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  // x轴数据
  xData: {
    type: Array,
    required: true
  },
  // y轴数据
  yData: {
    type: Array,
    required: true
  },
  // 图表标题
  title: {
    type: String,
    default: ''
  }
});

const chartRef = ref(null);
let myChart = null;

// 随机产生颜色
const colors = Array.from({ length: 20 }, () => `#${Math.floor(Math.random() * 16777215).toString(16)}`);

// 初始化图表配置
const initChartOption = () => {
  return {
    title: {
      text: props.title,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: '#cccccc',
          width: 1,
          type: 'dashed'
        }
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false, // 坐标轴两边留白策略
      data: props.xData,
      axisLine: {
        lineStyle: {
          color: '#cccccc'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#cccccc'
        }
      },
      splitLine: {
        lineStyle: {
          color: ['#eee'],
          type: 'dashed'
        }
      }
    },
    series: [
      {
        type: 'line',
        data: props.yData,
        smooth: true, // 平滑曲线
        symbol: 'circle', // 数据点的图形
        symbolSize: 8, // 数据点的大小
        itemStyle: {
          color: colors[0]
        },
        lineStyle: {
          width: 3
        },
        areaStyle: {
          // 区域填充样式
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: colors[0] + 'aa' // 透明度 0.67
            },
            {
              offset: 1,
              color: colors[0] + '11' // 透明度 0.07
            }
          ])
        }
      }
    ]
  };
};

// 初始化图表
const initChart = () => {
  if (chartRef.value) {
    myChart = echarts.init(chartRef.value);
    myChart.setOption(initChartOption());
  }
};

// 监听数据变化
watch(
  () => [props.xData, props.yData],
  () => {
    myChart?.setOption(initChartOption());
  },
  { deep: true }
);

// 监听窗口大小变化
const handleResize = () => {
  myChart?.resize();
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  myChart?.dispose();
});
</script>
