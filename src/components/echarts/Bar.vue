<template>
  <div ref="chartRef" :style="{ width: '100%', height: '100%' }"></div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  // x轴数据
  xData: {
    type: Array,
    required: true
  },
  // y轴数据
  yData: {
    type: Array,
    required: true
  },
  // 图表标题
  title: {
    type: String,
    default: ''
  }
});

const chartRef = ref(null);
let myChart = null;

// 初始化图表配置
const initChartOption = () => {
  return {
    title: {
      text: props.title,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: props.xData,
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        type: 'bar',
        data: props.yData.map((value) => ({
          value,
          // 为每个柱子设置随机颜色
          itemStyle: {
            color: `rgb(${Math.random() * 250},${Math.random() * 250},${Math.random() * 150})`
          }
        })),
        barWidth: '60%'
      }
    ]
  };
};

// 初始化图表
const initChart = () => {
  if (chartRef.value) {
    myChart = echarts.init(chartRef.value);
    myChart.setOption(initChartOption());
  }
};

// 监听数据变化
watch(
  () => [props.xData, props.yData],
  () => {
    myChart?.setOption(initChartOption());
  },
  { deep: true }
);

// 监听窗口大小变化
const handleResize = () => {
  myChart?.resize();
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  myChart?.dispose();
});
</script>
