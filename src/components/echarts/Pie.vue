<template>
  <div ref="chartRef" :style="{ width: '100%', height: '100%' }"></div>
</template>

<script setup>
import { ref, onMounted, watch, onUnmounted } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  // 数据数组，格式为 [{name: '分类1', value: 100}, {name: '分类2', value: 200}]
  data: {
    type: Array,
    required: true,
    default: () => []
  },
  // 图表标题
  title: {
    type: String,
    default: ''
  }
});

const chartRef = ref(null);
let myChart = null;

// 随机产生颜色 几种好看的颜色
const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#b23b3a', '#539aa6', '#deb83d'];

// 初始化图表配置
const initChartOption = () => {
  return {
    title: {
      text: props.title,
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: '5%'
    },
    series: [
      {
        name: props.title,
        top: 20,
        type: 'pie',
        radius: ['30%', '60%'], // 设置成环形图，如果想要实心饼图可以设置为 '70%'
        center: ['50%', '45%'],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 1,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          formatter: '{b}: {d}%'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        data: props.data.map((item, index) => ({
          ...item,
          itemStyle: {
            color: colors[index % colors.length]
          }
        }))
      }
    ],
    color: colors
  };
};

// 初始化图表
const initChart = () => {
  if (chartRef.value) {
    myChart = echarts.init(chartRef.value);
    myChart.setOption(initChartOption());
  }
};

// 监听数据变化
watch(
  () => props.data,
  () => {
    myChart?.setOption(initChartOption());
  },
  { deep: true }
);

// 监听窗口大小变化
const handleResize = () => {
  myChart?.resize();
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  myChart?.dispose();
});
</script>
