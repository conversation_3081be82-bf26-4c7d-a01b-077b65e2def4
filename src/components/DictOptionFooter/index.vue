<template>
  <div>
    <el-tooltip content="前往字典维护页面；如果您有字典维护权限，请转到字典维护选项" placement="top">
      <el-button link type="primary" icon="Setting" @click="handleGoToDict"></el-button>
    </el-tooltip>
    <el-tooltip content="刷新字典选项" placement="top">
      <el-button link type="primary" icon="Refresh" @click="handleRefreshDict"></el-button>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { getInfoByDictType } from '@/api/system/dict/type';
import { getDicts } from '@/api/system/dict/data';
import { propTypes } from '@/utils/propTypes';

const props = defineProps({
  dictKey: propTypes.string.def('org_role')
});

/** 前往字典维护选项 */
const handleGoToDict = async () => {
  // 获取字典的DictId
  const { data } = await getInfoByDictType(props.dictKey);
  const dictId = ref<string | number>();
  if (data) {
    dictId.value = data.dictId;
    // console.log('orgRoleDictId:' + dictId.value);
  } else {
    alert('未找到对应的字典信息');
    return;
  }
  // 拼接path
  const path = '/system/dict-data/index/' + dictId.value;
  // 路由跳转
  window.open(path);
};
// 定义暴露给父组件的订阅事件
const emit = defineEmits(['refreshDict']);
const dict = ref<any>();
// 手动触发刷新字典数据;
const handleRefreshDict = async () => {
  const { data } = await getDicts(props.dictKey);
  if (data) {
    dict.value = data.map((item) => ({ label: item.dictLabel, value: item.dictValue }));
    emit('refreshDict', dict.value);
    ElMessage.success('Refresh Success');
  }
};
</script>
