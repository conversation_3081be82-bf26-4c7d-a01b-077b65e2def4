<template>
  <el-image :src="`${realSrc}`" fit="cover" :style="`width:${realWidth};height:${realHeight};`" :preview-src-list="realSrcList" preview-teleported>
    <template #error>
      <div class="image-slot">
        <el-icon><picture-filled /></el-icon>
      </div>
    </template>
  </el-image>
</template>

<script setup lang="ts">
import { propTypes } from '@/utils/propTypes';
import { listByIds } from '@/api/system/oss';
import { OssVO } from '@/api/system/oss/types';

const props = defineProps({
  src: propTypes.string.def(''),
  width: {
    type: [Number, String],
    default: ''
  },
  height: {
    type: [Number, String],
    default: ''
  },
  // 是否为ID格式，默认为false
  isId: {
    type: Boolean,
    default: false
  }
});

// 存储图片详情信息
const imageInfo = ref<OssVO[]>([]);

// 获取图片信息
const fetchImageInfo = async () => {
  if (props.isId && props.src) {
    try {
      const res = await listByIds(props.src);
      imageInfo.value = res.data || [];
    } catch (error) {
      console.error('获取图片信息失败', error);
    }
  }
};

// 当src或isId变化时，重新获取图片信息
watch(
  () => [props.src, props.isId],
  () => {
    if (props.isId && props.src) {
      fetchImageInfo();
    }
  },
  { immediate: true }
);

const realSrc = computed(() => {
  if (!props.src) {
    return '';
  }

  // 如果是ID格式且已获取到图片信息
  if (props.isId && imageInfo.value.length > 0) {
    return imageInfo.value[0]?.url || '';
  }

  // 非ID格式直接使用第一个URL
  let real_src = props.src.split(',')[0];
  return real_src;
});

const realSrcList = computed(() => {
  if (!props.src) {
    return [];
  }

  // 如果是ID格式且已获取到图片信息
  if (props.isId && imageInfo.value.length > 0) {
    return imageInfo.value.map((item) => item.url);
  }

  // 非ID格式直接使用分割后的URL列表
  let real_src_list = props.src.split(',');
  let srcList: string[] = [];
  real_src_list.forEach((item: string) => {
    if (item.trim() === '') {
      return;
    }
    srcList.push(item);
  });
  return srcList;
});

const realWidth = computed(() => (typeof props.width == 'string' ? props.width : `${props.width}px`));

const realHeight = computed(() => (typeof props.height == 'string' ? props.height : `${props.height}px`));
</script>

<style lang="scss" scoped>
.el-image {
  border-radius: 5px;
  background-color: #ebeef5;
  box-shadow: 0 0 5px 1px #ccc;
  :deep(.el-image__inner) {
    transition: all 0.3s;
    cursor: pointer;

    &:hover {
      transform: scale(1.2);
    }
  }

  :deep(.image-slot) {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    color: #909399;
    font-size: 30px;
  }
}
</style>
